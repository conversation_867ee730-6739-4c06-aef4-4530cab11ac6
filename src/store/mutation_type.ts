export namespace commonType {
    export interface commonLocal {
        [key: string]: any;
    }

    export interface commonSession {
        [key: string]: any;
    }
}

export const commonLocal: commonType.commonLocal = {
    evmsOrdertQuery: null
};
export const commonSession: commonType.commonSession = {};
export const LOCAL_CONFIG = 'LOCAL_CONFIG';
export const SESSION_CONFIG = 'SESSION_CONFIG';
