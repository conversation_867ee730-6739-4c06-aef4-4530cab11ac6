<script lang="ts">
export default {
    name: 'ConsultationHospitalBase'
};
</script>

<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { FormInstance } from 'element-plus/es/components/form';
import {
    ConsultationExpert,
    ConsultationHospital,
    ConsultationHospitalParams,
    Leader
} from '@/model/consultation';
import { getGroupDetail, addGroup, updateGroup } from '@/api/evms/consultation';
import { listDoctorUser } from '@/api/evms/doctor_user';
import { NList } from '@/model/api';
import { DoctorUser } from '@/model/doctor_user';

interface ApiResponse<T = any> {
    code: number;
    data: T;
    msg?: string;
}

interface GroupResponse {
    id: string;

    [key: string]: any;
}

interface DoctorResponse {
    rows: ConsultationExpert[];

    [key: string]: any;
}

interface FormRules {
    name: {
        required: boolean;
        message: string;
        trigger: 'blur';
    }[];
    type: {
        required: boolean;
        message: string;
        trigger: 'change';
    }[];
}

const rules: FormRules = {
    name: [{ required: true, message: '请输入群组名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择群组专家', trigger: 'change' }]
};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

interface StateInfo extends Partial<ConsultationHospital> {
    leaders?: Leader[];
}

const state = reactive({
    info: {} as StateInfo
});

const props = defineProps<{
    groupId?: string;
}>();

const emit = defineEmits(['update:groupId']);

let addId = -1;

// 获取群组信息
const getGroupInfo = async (id: string) => {
    const res = (await getGroupDetail(id)) as unknown as ApiResponse<ConsultationHospital>;
    if (res.code === 200 && res.data) {
        state.info = Array.isArray(res.data) ? res.data[0] : res.data;
    }
};

if (props.groupId) {
    // 获取设备信息
    getGroupInfo(props.groupId).then(res => {
    });
}

// 返回
const router = useRouter();
const cancel = () => {
    router.back();
};

const formRef = ref<FormInstance>();

// 保存
const save = async (fn?: () => void) => {
    if (addId > -1) {
        if (fn) {
            fn();
            return;
        }
        proxy!.$modal.msgWarning('已经保存了');
        return;
    }

    if (!formRef.value) return;

    try {
        const valid = await formRef.value.validate();
        if (!valid) return false;

        // 构建参数，确保类型正确
        const params: ConsultationHospitalParams = {
            name: state.info.name || '',
            leaders: state.info.leaders?.map(leader => leader.userid) || []
            // ({
            //     userid: leader.userid,
            //     realname: leader.realname,
            //     avatar: leader.avatar,
            //     title: leader.title,
            //     company: leader.company,
            // })
            // ),
        };

        if (props.groupId) {
            params.id = props.groupId;
            // 编辑
            const res = (await updateGroup(params)) as unknown as ApiResponse<null>;
            if (res.code === 200) {
                proxy!.$modal.msgSuccess('保存成功');
                if (fn) {
                    fn();
                    return;
                }
                await getGroupInfo(props.groupId);
            }
        } else {
            // 新增
            const res = (await addGroup(params)) as unknown as ApiResponse<{ id: string }>;
            if (res.code === 200) {
                proxy!.$modal.msgSuccess('保存成功');
                addId = -1;
                if (fn) {
                    fn();
                    return;
                }
            }
        }
    } catch (error) {
        proxy!.$modal.msgError('保存失败');
    }
};

// 保存并返回
const saveAndBack = () => {
    save(cancel);
};

// 专家列表
const jbList = ref<Leader[]>([]);

// 添加分页相关状态
const pageQuery = reactive({
    loading: false,
    query: ''
});

// 修改 remoteMethod，添加初始化逻辑
const remoteMethod = async (query: string) => {
    pageQuery.query = query;
    pageQuery.loading = true;

    try {
        const results = await listDoctorUser({
            q_type: 0,
            q_like_realname: query
        });

        if (results.code === 200) {
            const newList = (results.data as NList<DoctorUser>)!.rows;

            // 搜索时重置列表
            if (state.info.leaders) {
                jbList.value = [...state.info.leaders];
            } else {
                jbList.value = [];
            }

            // 添加新的数据
            const filteredList = newList.filter(
                (i: Leader) => !state.info.leaders?.some(v => v.userid === i.userid)
            );
            jbList.value.push(...filteredList);
        }
    } finally {
        pageQuery.loading = false;
    }
};
remoteMethod('');
</script>

<template>
    <div class="consultation_hospital_base_container">
        <el-form ref="formRef" :model="state.info" :rules="rules" label-position="top">
            <el-row class="form-con" justify="space-between">
                <el-col :span="24">
                    <el-row :gutter="30">
                        <el-col :span="9" class="one">
                            <el-form-item label="群组名称" prop="name">
                                <el-input v-model="state.info.name" placeholder="请输入群组名称" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="群组所属专家(可多选)" prop="hospitalId">
                                <el-select
                                    v-model="state.info.leaders"
                                    :loading="pageQuery.loading"
                                    :remote-method="remoteMethod"
                                    filterable
                                    multiple
                                    placeholder="请选择专家"
                                    remote
                                    remote-show-suffix
                                    reserve-keyword
                                    value-key="userid"
                                >
                                    <el-option
                                        v-for="item in jbList"
                                        :key="item.userid"
                                        :label="item.realname"
                                        :value="item"
                                    >
                                        <span>{{ item.realname }}</span>
                                        <span style="color: #8492a6; font-size: 13px; margin-left: 8px">
                                            {{ item.title }} - {{ item.company }}
                                        </span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="24" style="height: 100px; margin-top: auto">
                    <el-row justify="end">
                        <el-button @click="cancel">返回</el-button>
                        <el-button type="primary" @click="() => save()">保存</el-button>
                        <el-button type="primary" @click="saveAndBack">保存并返回</el-button>
                    </el-row>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
.consultation_hospital_base_container {
    :deep(.el-row) {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .form-con {
        height: 100%;
    }

    :deep(.el-card__body) {
        padding: 0 !important;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;

        .el-tabs__nav {
            border: none;
        }

        .el-tabs__item {
            margin-right: 1px;
            border: none;
            border-radius: 0 0 6px 6px;
            background: #ecf7ff;
            padding: 8px 10px;
            justify-content: center;
            align-items: center;
            color: #009dff;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%;

            &.is-active {
                background: #009dff;
                color: #fff;
            }
        }
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
        width: 100% !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        min-height: 40px;
    }

    :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
        width: 100% !important;
    }

    :deep(.el-select-dropdown__wrap) {
        max-height: 300px;
    }

    :deep(.el-select-dropdown__item) {
        height: auto;
        padding: 8px 12px;
        line-height: 1.5;
    }
}
</style>
