<script setup lang="ts">
import { ref, defineProps, getCurrentInstance, ComponentInternalInstance } from 'vue';
import { getTableInfo } from '@/minix/tables';
import { UserDevice } from '@/model/dms_user';
import { DeviceUserPermission } from '@/model/dms_device';
import { updateDeviceUser } from '@/api/evms/dms_device';
import { ElMessage } from 'element-plus';
import {
    addDeviceByUserId,
    deleteDeviceByUserId,
    getHospitalListByKeyAndUserId
} from '@/api/evms/dms_permission';
import { DmsHospitalInfo } from '@/model/dms_hospital';
import { switchType } from '@/assets/constant/switchType';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 医院名称推荐选择
type HopitalItem = {
    value: string;
    id: number;
};
const open = ref(false);

const props = defineProps(['userId']);

// 获取用户设备
// const getUserDevice = async () => {
//     const res = await listDeviceByUserId(props.userId)
// }
const tableInfo = getTableInfo<UserDevice>('/evms/dms_device/getDeviceListByUserId', {
    userId: props.userId
});
tableInfo.load();
const tableInfoAdd = getTableInfo<UserDevice>('/evms/dms_device/getDeviceListByNotUserId', {
    userId: props.userId
});
// 获取医院名称搜索结果
const querySearch = async (queryString: string, cb: any) => {
    const results = await getHospitalListByKeyAndUserId({ userId: props.userId, keywords: queryString });
    // call callback function to return suggestions
    if (results.code === 200) {
        console.log(results.data, '--1');
        cb(
            results?.data && (results.data as DmsHospitalInfo[]).length > 0
                ? (results.data as DmsHospitalInfo[])?.map((item: any) => {
                    return {
                        value: item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};
const handleSelect = (item: HopitalItem) => {
    console.log(item);
    tableInfo.from.like_Jhospital_name = item.value;
};

// 新增按钮
const handleAdd = () => {
    open.value = true;
    tableInfoAdd.load();
};
// 删除按钮
// 删除按钮loading
const deleteBtnLoading = ref(false);
// 表格勾选
const ids = ref<number[]>([]);
const multiple = ref(true);
const handleDelete = async (row?: UserDevice) => {
    if (ids.value.length === 0 && !row) {
        ElMessage.error('请选择设备');
        return;
    }
    deleteBtnLoading.value = true;
    const str = row ? row.id + '' : ids.value.join(',');
    // 询问
    proxy!.$modal
        .confirm('是否确认删除DMS设备编号为"' + str + '"的数据项？')
        .then(function() {
            return deleteDeviceByUserId({
                userId: props.userId,
                deviceIdsStr: str
            });
        })
        .then(res => {
            if (res.code === 200) {
                // getList();
                proxy!.$modal.msgSuccess('删除成功');
                // 重置列表
                tableInfo.reset();
            }
        })
        .catch(() => {
        });
    deleteBtnLoading.value = false;
};
const idsForDevice = ref<number[]>([]);
const handleSelectionChangeForDevice = (selection: UserDevice[]) => {
    idsForDevice.value = selection.map(item => item.id);
};

// 新增设备
const submitForm = async () => {
    if (idsForDevice.value.length === 0) {
        ElMessage.error('请选择设备');
        return;
    }
    const res = await addDeviceByUserId({
        userId: (props.userId - 0) as number,
        deviceIdsStr: idsForDevice.value.join(',')
    });
    if (res.code === 200) {
        ElMessage.success('新增成功');
        tableInfo.reset();
        idsForDevice.value = [];
        open.value = false;
    } else {
        ElMessage.error(res.msg);
    }
};
const cancel = () => {
    open.value = false;
    idsForDevice.value = [];
};

const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map(item => item.id);
    multiple.value = !selection.length;
};

// 修改用户
const openEdit = ref(false);
const userPermission = ref<DeviceUserPermission>({} as DeviceUserPermission);
// 修改的设备id
const editId = ref<number>(-1);
// 取消修改用户
const cancelEdit = () => {
    openEdit.value = false;
};
// 修改用户权限
const handleUpdate = (row: UserDevice) => {
    openEdit.value = true;
    editId.value = row.id;
    userPermission.value = { ...row.permission! };
};
// 提交修改用户权限
const editBtnLoading = ref(false);
const submitEditForm = async () => {
    editBtnLoading.value = true;
    const res = await updateDeviceUser({
        deviceId: editId.value,
        userId: props.userId,
        permission: JSON.stringify(userPermission.value)
    });
    if (res.code === 200) {
        openEdit.value = false;
        ElMessage.success('修改成功');
        tableInfo.reset();
        userPermission.value = {} as DeviceUserPermission;
        editId.value = -1;
    } else {
        ElMessage.error(res.msg);
    }
    editBtnLoading.value = false;
};
</script>

<template>
    <div class="dms_hospital_info_video">
        <el-form :model="tableInfo.from" :inline="true" label-width="100">
            <el-form-item label="设备名称">
                <el-input
                    v-model="tableInfo.from.like_name"
                    placeholder="请输入设备名称"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-form-item label="所在医院">
                <el-autocomplete
                    v-model="tableInfo.from.like_Jhospital_name"
                    :fetch-suggestions="querySearch"
                    clearable
                    placeholder="请输入医院名称"
                    @select="handleSelect"
                    @keyup.enter="tableInfo.search"
                />
            </el-form-item>
            <el-button type="primary" icon="search" @click="tableInfo.search">搜索</el-button>
            <el-button icon="refresh" @click="tableInfo.reset">重置</el-button>
        </el-form>
        <el-row :gutter="10" class="mb8" style="margin-top: 10px">
            <el-col :span="1.5">
                <el-button
                    color="#009dff"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:dms_hospital:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    color="#ff5c00"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    :loading="deleteBtnLoading"
                    @click="() => handleDelete()"
                    v-hasPermi="['evms:dms_hospital:remove']"
                >删除
                </el-button>
            </el-col>
        </el-row>
        <el-table
            row-key="id"
            v-loading="tableInfo.TableLoading.value"
            :data="tableInfo.list.value"
            height="450"
            style="margin-top: 20px"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="设备名称" align="center" prop="name" />
            <el-table-column label="所在医院" align="center" prop="hospitalVo.name" />
            <el-table-column label="在线会议" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.meeting ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="实时视频" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.realplay ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="回放视频" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.backplay ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="下载视频" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.download ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="对讲" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.talk ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="远程控制" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.control ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="视频水印" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.permission.watermark ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:dms_hospital:edit']"
                    >修改
                    </el-button>
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        :disabled="deleteBtnLoading"
                        v-hasPermi="['evms:dms_hospital:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-row justify="space-between" align="bottom">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                :total="tableInfo.total.value"
                v-model:page="tableInfo.pageParam.pageNum"
                v-model:limit="tableInfo.pageParam.pageSize"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
    <!--    新增设备-->
    <el-dialog v-model="open" append-to-body modal-class="dms_permission_info_user_dialog" draggable>
        <template #header>
            <div class="my-header">
                <div class="dialog_header">新增设备</div>
            </div>
        </template>
        <div>
            <el-form :model="tableInfoAdd.from" :inline="true" label-width="100">
                <el-form-item label="设备名称" prop="realname">
                    <el-input
                        v-model="tableInfoAdd.from.like_name"
                        placeholder="请输入设备名称"
                        style="width: 160px"
                        @keyup.enter="tableInfo.search"
                    ></el-input>
                </el-form-item>
                <el-form-item label="所在医院" prop="username">
                    <el-input
                        v-model="tableInfoAdd.from.like_Jhospital_name"
                        placeholder="请输入医院名称"
                        style="width: 160px"
                        @keyup.enter="tableInfo.search"
                    ></el-input>
                </el-form-item>
                <el-row justify="start" :gutter="20">
                    <el-button type="primary" icon="Search" @click="tableInfoAdd.search">搜索</el-button>
                    <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                </el-row>
            </el-form>

            <el-table
                v-loading="tableInfoAdd.TableLoading.value"
                height="300"
                :data="tableInfoAdd.list.value"
                style="width: 100%; margin-top: 20px"
                @selection-change="handleSelectionChangeForDevice"
            >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="name" label="设备名称" width="120"></el-table-column>
                <el-table-column prop="hospitalVo.name" label="所在医院"></el-table-column>
            </el-table>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
    <!--    修改用户权限-->
    <el-dialog
        v-model="openEdit"
        width="561px"
        append-to-body
        modal-class="dms_permission_info_user_edit"
        draggable
    >
        <template #header>
            <div class="my-header">
                <div class="dialog_edit_header">修改用户权限</div>
            </div>
        </template>
        <el-form :label-position="'left'" style="background-color: rgba(244, 244, 244, 1)">
            <el-row :gutter="1" style="margin-bottom: 1px">
                <el-col :span="12">
                    <el-form-item label="在线会议" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.meeting"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实时视频" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.realplay"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="1" style="margin-bottom: 1px">
                <el-col :span="12">
                    <el-form-item label="回放视频" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.backplay"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="下载视频" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.download"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="1" style="margin-bottom: 1px">
                <el-col :span="12">
                    <el-form-item label="对讲" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.talk"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="远程控制" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.control"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="1" style="margin-bottom: 8px">
                <el-col :span="12">
                    <el-form-item label="视频水印" prop="watermark" style="width: 100%">
                        <el-switch
                            v-model="userPermission.watermark"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12" style="width: 100%; display: flex; justify-content: flex-start">
                    <div class="el-form-item__label"></div>
                    <div class="el-form-item__content" style="flex: 1"></div>
                </el-col>
            </el-row>
            <el-row>
                <el-col class="last_item">
                    <el-form-item
                        label="摄像头视频可见"
                        prop="watermark"
                        style="width: 100%; background-color: #fff"
                    >
                        <el-radio-group v-model="userPermission.visibleState">
                            <!-- works when >=2.6.0, recommended ✔️ not work when <2.6.0 ❌ -->
                            <el-radio :label="1">可见</el-radio>
                            <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
                            <el-radio :label="0">不可见</el-radio>
                            <el-radio :label="2">以设备设置为准</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelEdit">取 消</el-button>
                <el-button type="primary" @click="submitEditForm" :loading="editBtnLoading">确 定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss">
.dms_permission_info_user_dialog,
.dms_permission_info_user_edit {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}

.dms_permission_info_user_edit {
    .el-dialog {
        border-radius: 20px;
        border: 1px solid rgba(204, 204, 204, 0.8);
        background: #f8f9fb;
        box-shadow: 10px 10px 20px 0 rgba(0, 0, 0, 0.15);
        overflow: clip;
    }

    .dialog_edit_header {
        padding: 6.5px 0;
        display: flex;
        justify-content: center;
        color: #333;
        text-align: center;
        font-size: 14px;
        background-color: #fff;
    }

    .el-dialog__headerbtn {
        top: 4.5px;
    }

    .el-form-item__content {
        justify-content: end;
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-form-item {
        height: 53px;
        padding: 0;

        &:last-child {
            border-bottom: none;
        }
    }

    .el-form-item--default {
        margin: 0;
    }

    .el-form-item__label {
        color: #666;
        font-size: 14px;
        line-height: 150%; /* 21px */
        height: 100%;
        width: 120px;
        padding-left: 30px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        background-color: rgba(250, 250, 250, 1);
    }
    .last_item .el-form-item__label {
        width: 172px;
    }
    .el-form-item__content {
        height: 100%;
        padding-right: 30px;
        background-color: rgba(248, 249, 251, 1);
    }
    .el-switch {
        height: 18px;
    }

    .dialog-footer {
        padding-top: 30px;
        display: flex;
        justify-content: center;

        .el-button {
            padding: 5px 36px;
            border-radius: 15px;
        }
    }
}
</style>
