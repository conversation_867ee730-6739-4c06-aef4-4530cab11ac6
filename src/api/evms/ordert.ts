import request from '@/utils/request';
interface Response<T> {
    msg: string;
    code: number;
    data: T;
    timestamp: number;
    success: boolean;
}
interface PageResponse<T> {
    rows: Array<T>;
    total: number;
    pages: number;
}

export interface Experts {
    agree: number;
    avatar: string;
    department: string;
    expertId: number;
    doctorId: number;
    expertName: string;
    professionalTitle: string;
    operationTimeStamp: number;
    mobile: string;
    // 自己添加
    agreeText: string;
}
export interface EvmsCousultOrderDetail {
    cancelReason: string;
    cancelTime: string;
    cancelTimeStamp: number;
    completionTime: string;
    completionTimeStamp: number;
    createTime: string;
    createTimeStamp: number;
    cousultType: number;
    description: string;
    deviceId: number;
    deviceName: string;
    expertId: number | null;
    expertName: string;
    experts: Experts[];
    hospitalId: number;
    hospitalName: string;
    mainDescription: string;
    meetingId: number;
    orderNumber: string;
    orderStatus: string;
    patientId: number;
    patientName: string;
    startTime: string;
    startTimeStamp: number;
    surgicalExpectEndTime: string;
    surgicalExpectEndTimeStamp: number;
    surgicalTime: string;
    surgicalTimeStamp: number;
    surgicalType: string;
    userId: number;
    userName: string;
    realName: string;
    start: number | null;
    end: number | null;
    expertMobile: string;
    duration: number;
}

export interface OrderParams {
    cousultType: number;
    description: string;
    deviceId: number;
    // expertId: number;
    experts: Array<{
        expertId: number;
        expertName: string;
    }>;
    mainDescription: string;
    surgicalTimeStamp: number;
    surgicalType: string;
    // 修改时传递
    cancelReason?: string;
    orderNumber?: string;
    orderStatus?: string;
    // 前端自己要用的
    deviceName: string;
    realName: string;
    surgicalTime: string;
    expertNameStr: string;
}
export interface EvmsVideoDetail {
    duration: number;
    height: number;
    orderNumber: string;
    size: number;
    videoId: number;
    videoUrl: string;
    width: number;
}

export interface EvmsVideoListDetail {
    list: EvmsVideoDetail[];
}

export interface CancelOrderParams {
    cancelReason: string;
    orderNumber: string;
}

// 查询会诊订单列表
export function listOrdert(query: any) {
    return request({
        url: '/evms/ordert/list',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<PageResponse<EvmsCousultOrderDetail>>>;
}

// 查询录像列表
export function videoList(query: any) {
    return request({
        url: '/evms/ordert/get/video/list',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<EvmsVideoListDetail>>;
}

// 查询会诊订单详细
export function getOrdert(data: { orderNumber: string }) {
    return request({
        url: '/evms/ordert/getOrder',
        method: 'get',
        params: data,
    }) as unknown as Promise<Response<EvmsCousultOrderDetail>>;
}

// 新增会诊订单
export function addOrdert(data: OrderParams) {
    return request({
        url: '/evms/ordert/saveConsult',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 修改会诊订单
export function updateOrdert(data: OrderParams) {
    return request({
        url: '/evms/ordert/updateConsult',
        method: 'put',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 取消会诊订单
export function cancelOrdert(data: CancelOrderParams) {
    return request({
        url: '/evms/ordert/cancleConsult',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 邀请医生同意
export function inviteAgree(data: { agree: boolean; doctorId: number; orderNumber: string }) {
    return request({
        url: '/evms/ordert/invite/agree',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}
