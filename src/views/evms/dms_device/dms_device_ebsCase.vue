<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['cl:ebscase:add']" type="primary" plain icon="Plus" @click="handleAdd"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebscase:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebscase:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="ebscaseList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" width="100" prop="id" />

            <el-table-column label="手术名称" align="center" width="300" prop="elabCaseName" />

            <el-table-column label="术者" align="center" prop="authorName" />

            <el-table-column label="设备名称" align="center" prop="device.name" />

            <el-table-column label="产品" align="center" prop="product" />

            <el-table-column label="品牌" align="center" prop="brand" />

            <el-table-column label="应用活动" align="center" prop="activity" />

            <el-table-column label="商务" align="center" prop="business" />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:ebscase:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:ebscase:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>

                    <el-button
                        link
                        type="success"
                        icon="Search"
                        @click="handleEbsInternalManageReport(scope.row)"
                        v-hasPermi="['cl:ebscase:list']"
                    >内部管理
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改EBS病例对话框 -->
        <el-dialog v-model="open" :title="title" width="800px" append-to-body>
            <el-form ref="ebscaseRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="E-lab手术" prop="elabId">
                            <el-select
                                style="width: 100%"
                                v-model="form.elabId"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入手术名称"
                                remote-show-suffix
                                @change="selectChange"
                                :remote-method="searchElabList"
                                :loading="load"
                            >
                                <el-option
                                    v-for="item in elabCaseList"
                                    :key="item.id"
                                    :label="item.caseName"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="术者" prop="authorId">
                            <el-select
                                style="width: 100%"
                                v-model="form.authorId"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入术者姓名"
                                remote-show-suffix
                                :remote-method="searchAuthorList"
                                :loading="load"
                            >
                                <el-option
                                    v-for="item in authorList"
                                    :key="item.id"
                                    :label="item.authorName"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="图钉标记" prop="thumbtack">
                            <el-input v-model="form.thumbtack" placeholder="请输入图钉标记" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="手术日期" prop="operationDate">
                            <el-input v-model="form.operationDate" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="分类" prop="type">
                            <el-input v-model="form.type" placeholder="请输入分类" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="手术时间(起)" prop="beginTime">
                            <el-input v-model="form.beginTime" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="产品" prop="product">
                            <el-input v-model="form.product" placeholder="请输入产品" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="手术时间(止)" prop="endTime">
                            <el-input v-model="form.endTime" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="品牌" prop="brand">
                            <el-input v-model="form.brand" placeholder="请输入品牌" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="患者姓名全拼" prop="fullSpell">
                            <el-input v-model="form.fullSpell" placeholder="请输入患者姓名全拼" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12"></el-col>

                    <el-col :span="24">
                        <el-form-item label="mp4" prop="mp4">
                            <FileUpload
                                ref="fileUpload"
                                v-model:modelValue="form.mp4"
                                :limit="1"
                                :fileType="videoTypes"
                                :show-file-list="true"
                            ></FileUpload>
                        </el-form-item>
                    </el-col>
                    <!--文件上传-->
                    <el-col :span="24">
                        <el-form-item label="病情介绍PPT" prop="ppt">
                            <FileUpload
                                ref="fileUpload"
                                v-model:modelValue="form.ppt"
                                :limit="1"
                                :fileType="pptTypes"
                                :show-file-list="true"
                            ></FileUpload>
                        </el-form-item>
                    </el-col>
                    <!--文件上传-->
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Ebscase" lang="ts">
import {
    listEbscase,
    getEbscase,
    delEbscase,
    addEbscase,
    updateEbscase,
    getElabCaseByName,
    getAuthorById,
    getElabCaseById,
    getAuthorListByName
} from '@/api/cl/ebscase';
import { queryFormat } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import FileUpload from '@/components/FileUpload/index.vue';
import { getDmsDevice } from '@/api/evms/dms_device';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const ebscaseList = ref<any[]>([]);
const authorList = ref<any[]>([]);
const elabCaseList = ref<any[]>([]);
const videoTypes = ref<any[]>(['wmv', 'mp4', 'avi', '3gp', 'm3u8']);
const pptTypes = ref<any[]>(['ppt', 'pptx', 'pdf']);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const load = ref(true);
const route = useRoute();
const router = useRouter();
const deviceId = ref(route.params.id);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        elabId: undefined,
        device_id: undefined,
        authorId: undefined,
        thumbtack: undefined,
        product: undefined,
        brand: undefined,
        type: undefined,
        fullSpell: undefined,
        ppt: undefined,
        mp4: undefined,
        beginTime: undefined,
        endTime: undefined,
        operationDate: undefined
    },
    rules: {
        elabId: [{ required: true, message: 'E-lab手术不能为空', trigger: 'blur' }],
        authorId: [{ required: true, message: '术者不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询EBS病例列表 */
function getList() {
    loading.value = true;
    listEbscase(queryFormat(queryParams.value)).then((response: any) => {
        ebscaseList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 关闭按钮 */
function close() {
    console.log('返回');
    router.back();
}

/**
 * 获取E-lab手术列表
 */
function searchElabList(name?: String) {
    load.value = true;
    elabCaseList.value = [];
    getElabCaseByName(name).then(response => {
        elabCaseList.value = response.data;
    });
    load.value = false;
}

function selectChange(value: any) {
    getElabCaseById(value).then(response => {
        if (response.data && response.data[0].beginTime) {
            let startTime = new Date(response.data[0].beginTime);
            form.value.operationDate = startTime.toISOString().split('T')[0];
            form.value.beginTime = startTime.toLocaleTimeString([], { hour12: false });
        }

        if (response.data && response.data[0].endTime) {
            let endTime = new Date(response.data[0].endTime);
            form.value.endTime = endTime.toLocaleTimeString([], { hour12: false });
        }
    });
}

/**
 * 获取术者列表
 */
function searchAuthorList(name?: String) {
    load.value = true;
    authorList.value = [];
    getAuthorListByName(name).then(response => {
        authorList.value = response.data;
    });
    load.value = false;
}

/** Ebs病例内部管理 */
function handleEbsInternalManageReport(row: any) {
    router.push({ path: '/evms/dms/dms_device_ebscase_internalmanage/' + row.id });
}

/**
 * 取消按钮
 */
function cancel() {
    open.value = false;
    reset();
}

/**
 * 表单重置
 */
function reset() {
    form.value = {
        id: undefined,
        elabId: undefined,
        deviceId: undefined,
        authorId: undefined,
        thumbtack: undefined,
        product: undefined,
        brand: undefined,
        type: undefined,
        fullSpell: undefined,
        ppt: undefined,
        mp4: undefined,
        beginTime: undefined,
        endTime: undefined,
        operationDate: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
    };
    proxy?.resetForm('ebscaseRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    form.value.deviceId = queryParams.value.device_id;
    title.value = '添加EBS病例';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getEbscase(_id).then((response: any) => {
        form.value = response.data;
        if (form.value.authorId) {
            authorList.value = [];
            getAuthorById(form.value.authorId).then(response => {
                authorList.value = response.data;
            });
        }
        //E-lab 手术信息
        if (form.value.elabId) {
            elabCaseList.value = [];
            getElabCaseById(form.value.elabId).then(response => {
                elabCaseList.value = response.data;
                //设置时间
                if (elabCaseList.value.length > 0 && elabCaseList.value[0].beginTime) {
                    let startTime = new Date(elabCaseList.value[0].beginTime);
                    form.value.operationDate = startTime.toISOString().split('T')[0];
                    form.value.beginTime = startTime.toLocaleTimeString([], { hour12: false });
                }
                if (elabCaseList.value.length > 0 && elabCaseList.value[0].endTime) {
                    let endTime = new Date(elabCaseList.value[0].endTime);
                    form.value.endTime = endTime.toLocaleTimeString([], { hour12: false });
                }
            });
        }
        load.value = false;
        open.value = true;
        title.value = '修改EBS病例';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['ebscaseRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateEbscase(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addEbscase(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除EBS病例编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delEbscase(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy?.download(
        'cl/ebscase/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `ebscase_${new Date().getTime()}#.xlsx`
    );
}

function getCLEbscases(id: any) {
    if (id) {
        queryParams.value.device_id = id;
    }
    getList();
}

getCLEbscases(route.params.id);
</script>
