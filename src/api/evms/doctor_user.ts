import request from '@/utils/request';

// 查询医生用户信息列表
export function listDoctorUser(query: any) {
    return request({
        url: '/evms/doctor_user/list',
        method: 'get',
        params: query,
    });
}

// 查询医生用户信息详细
export function getDoctorUser(id: any) {
    return request({
        url: '/evms/doctor_user/' + id,
        method: 'get',
    });
}

// 新增医生用户信息
export function addDoctorUser(data: any) {
    return request({
        url: '/evms/doctor_user',
        method: 'post',
        data: data,
    });
}

// 修改医生用户信息
export function updateDoctorUser(data: any) {
    return request({
        url: '/evms/doctor_user',
        method: 'put',
        data: data,
    });
}

// 删除医生用户信息
export function delDoctorUser(id: any) {
    return request({
        url: '/evms/doctor_user/' + id,
        method: 'delete',
    });
}
