<script setup lang="ts">
import { reactive } from 'vue';
import router from '@/router';
import { getDmsPermission, updateDmsPermission } from '@/api/evms/dms_permission';
import { DmsUserInfo } from '@/model/dms_user';
import { ElMessage } from 'element-plus';
type StateType = {
    deviceInfo: DmsUserInfo;
};
const state = reactive<StateType>({
    deviceInfo: {} as DmsUserInfo,
});
const props = defineProps(['userId']);
// 根据userId获取用户信息
const getUserInfo = async () => {
    const res = await getDmsPermission(props.userId);
    if (res.code === 200 && res.data) {
        // state.deviceInfo.username = res.data.username
        // state.deviceInfo.realname = res.data.realname
        // state.deviceInfo.company = res.data.company || ''
        // state.deviceInfo.department = res.data.department || ''
        // state.deviceInfo.permission!.videoExportPermission = res.data.permission.videoExportPermission
        state.deviceInfo = { ...res.data };
    }
};
// 如果有userId则获取用户信息
if (props.userId) {
    getUserInfo();
}
// 返回
const cancel = () => {
    router.back();
};

// 保存
const save = async (callback?: () => void) => {
    const rea = await updateDmsPermission(state.deviceInfo);
    if (rea.code === 200) {
        ElMessage.success('保存成功');
        console.log(callback);
        callback && callback();
    }
};

// 保存并返回
const saveAndBack = () => {
    save(cancel);
};
</script>

<template>
    <el-form v-if="state.deviceInfo" ref="formRef" :model="state.deviceInfo" label-position="top">
        <el-row class="form-con" justify="space-between">
            <el-col :span="24">
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="用户名" prop="name">
                            <el-input
                                disabled
                                v-model="state.deviceInfo.username"
                                placeholder="请输入设备名称"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="姓名" prop="name">
                            <el-input
                                disabled
                                v-model="state.deviceInfo.realname"
                                placeholder="请输入设备名称"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="单位" prop="name">
                            <el-input
                                disabled
                                v-model="state.deviceInfo.company"
                                placeholder="请输入设备名称"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="科室" prop="name">
                            <el-input
                                disabled
                                v-model="state.deviceInfo.department"
                                placeholder="请输入设备名称"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="30" v-if="state.deviceInfo && state.deviceInfo.permission">
                    <el-col :span="9">
                        <el-form-item label="MP4导出权限" prop="name">
                            <el-radio-group v-model="state.deviceInfo.videoExportPermission">
                                <el-radio :label="1">开</el-radio>
                                <el-radio :label="0">关</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="24" style="height: 100px">
                <el-row justify="end">
                    <el-button @click="cancel">返回</el-button>
                    <el-button type="primary" @click="() => save()">保存</el-button>
                    <el-button type="primary" @click="saveAndBack">保存并返回</el-button>
                </el-row>
            </el-col>
        </el-row>
    </el-form>
</template>

<style scoped lang="scss">
:deep(.el-card__body) {
    padding: 0 !important;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    margin-right: 1px;
    border: none;
    border-radius: 0 0 6px 6px;
    background: #ecf7ff;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    color: #009dff;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 24px */
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border-radius: 0 0 6px 6px;
    background: #009dff;
    color: #fff;
}

:deep(.el-tabs--card > .el-tabs__header) {
    border: none;
}
:deep(.el-textarea__inner) {
    height: 111px !important;
    background: #f8f9fb !important;
}
:deep(.el-input__wrapper) {
    background: #f8f9fb !important;
}
:deep(.el-select) {
    width: 100% !important;
    margin: 0 !important;
}
:deep(.el-input__inner) {
    height: 40px !important;
}
:deep(.el-input__wrapper) {
    width: 100% !important;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 100% !important;
}
</style>
