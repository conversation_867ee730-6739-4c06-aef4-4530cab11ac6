<template>
    <el-form ref="elabonlinereportRef" :model="form" :rules="rules" label-width="100px">
        <el-row class="form-con" justify="space-between">
            <el-col :span="24">
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="填写人" prop="writer">
                            <el-input v-model="form.writer" placeholder="请输入填写人姓名" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="办事处" prop="office">
                            <el-input v-model="form.office" placeholder="请输入办事处" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="网络情况" prop="network">
                            <el-input v-model="form.network" placeholder="请输入网络情况" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="设备状态" prop="status">
                            <el-input v-model="form.status" placeholder="请输入设备状态" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="故障摄像头" prop="cameraId">
                            <el-select
                                style="width: 100%"
                                v-model="form.cameraId"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请选择故障摄像头"
                                remote-show-suffix
                                :remote-method="searchCameraList"
                                :loading="load"
                            >
                                <el-option
                                    v-for="item in cameraList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id.toString()"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="巡检结果" prop="result">
                            <el-input v-model="form.result" placeholder="请输入巡检结果" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="故障原因" prop="reason">
                            <el-input
                                type="textarea"
                                :rows="3"
                                v-model="form.reason"
                                placeholder="请输入故障原因"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="故障解决时间" prop="solveTime">
                            <el-date-picker
                                clearable
                                v-model="form.solveTime"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="请选择故障解决时间"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>

            <el-col :span="24" style="height: 100px">
                <el-row justify="end">
                    <el-button @click="cancel">返回</el-button>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                </el-row>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup name="Elabonlinereport" lang="ts">
import { getElabpatrolreport, addElabpatrolreport, updateElabpatrolreport } from '@/api/cl/elabpatrolreport';
import { queryFormat } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getDmsCamera, listCamera } from '@/api/evms/dms_camera';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const elabonlinereportList = ref<any[]>([]);
const cameraList = ref<any[]>([]);
const loading = ref(true);
const load = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const route = useRoute();
const router = useRouter();

const data = reactive<{
    form: any;
    rules: any;
}>({
    form: {},
    rules: {
        status: [{ required: true, message: '设备状态不能为空', trigger: 'blur' }],
        writer: [{ required: true, message: '填写人不能为空', trigger: 'blur' }]
    }
});

const { form, rules } = toRefs(data);

/** 关闭按钮 */
function cancel() {
    console.log('返回');
    router.back();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        deviceId: undefined,
        cameraId: undefined,
        writer: undefined,
        office: undefined,
        result: undefined,
        inspectTime: undefined,
        writeTime: undefined,
        record: undefined,
        elabImage: undefined,
        ebsImage: undefined,
        network: undefined,
        logo: undefined,
        type: 'U',
        status: undefined,
        fault: undefined,
        reason: undefined,
        solveTime: undefined,
        hospital: undefined,
        courtyard: undefined,
        provinceCity: undefined,
        area: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
    };
    proxy?.resetForm('elabonlinereportRef');
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['elabonlinereportRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateElabpatrolreport(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('修改成功');
                });
            } else {
                addElabpatrolreport(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('新增成功');
                });
            }
        }
    });
}

/**
 * 获取摄像头列表
 */
function searchCameraList() {
    load.value = true;
    cameraList.value = [];
    listCamera(queryFormat({ device_id: route.params.deviceId })).then((response: any) => {
        cameraList.value = response.data.rows;
    });
    load.value = false;
}

function getCLElabOnlineReports() {
    reset();
    const reportId = route.params.id;
    const deviceId = route.params.deviceId;
    if (reportId && reportId != 0) {
        getElabpatrolreport(reportId).then((response: any) => {
            form.value = response.data;
        });
    }
    form.value.deviceId = deviceId;
    form.value.type = 'U';
}

searchCameraList();
getCLElabOnlineReports();
</script>
