<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
    addDmsDevice,
    getDmsDevice,
    getHospitalListByKeywords,
    listDmsRabbitMQ,
    updateDmsDevice
} from '@/api/evms/dms_device';
import { DmsDeviceInfo, RabbitmqInfo } from '@/model/dms_device';
import { FormInstance } from 'element-plus';

// 表单校验规则
// ip校验
const validateIP = (_: any, value: string, callback: (error?: Error) => void) => {
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(value)) {
        callback(new Error('请输入有效的IP地址'));
    } else {
        callback();
    }
};
// 端口校验
const validatePort = (_: any, value: string, callback: (error?: Error) => void) => {
    const portRegex = /^[1-9]\d*$/;
    if (!portRegex.test(value)) {
        callback(new Error('请输入有效的端口号'));
    } else {
        callback();
    }
};
const rules = {
    name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    nativeId: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
    hospitalId: [{ required: true, message: '请输入所在医院', trigger: 'change' }],
    rabbitmqConfigId: [{ required: true, message: '请选择所连服务器', trigger: 'change' }],
    nvrIp: [
        { required: true, message: '请输入NVR IP', trigger: 'blur' },
        { validator: validateIP, trigger: 'blur' }
    ],
    nvrPort: [
        { required: true, message: '请输入NVR TCP访问端口', trigger: 'blur' },
        { validator: validatePort, trigger: 'blur' }
    ],
    nvrPwd: [{ required: true, message: '请输入NVR 访问密码', trigger: 'blur' }]
};
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_device_type } = proxy!.useDict('evms_dms_device_type');
const state = reactive({
    deviceInfo: {
        status: {
            status: '',
            onofflineTime: ''
        },
        rabbitmqConfigVo: {
            id: -1,
            name: ''
        },
        hospitalVo: {
            id: 0,
            name: ''
        },
        weight: 0
    } as DmsDeviceInfo,
    rabbitmqList: [] as RabbitmqInfo[]
});
const props = defineProps(['deviceId']);
let addId = -1;
const emit = defineEmits(['update:deviceId']);
const getDmsDeviceInfo = async (id?: number) => {
    // 获取设备信息
    const res = await getDmsDevice(parseInt(id ? id : props.deviceId));
    if (res.code === 200) {
        state.deviceInfo = (res.data || {}) as DmsDeviceInfo;
        state.deviceInfo.hospitalId = (res.data as DmsDeviceInfo)?.hospitalVo?.name;
        if ((res.data as DmsDeviceInfo)?.rabbitmqConfigVo?.id) {
            state.deviceInfo.rabbitmqConfigId = (res.data as DmsDeviceInfo)?.rabbitmqConfigVo?.id!;
        }
    }
};
// 获取服务器列表
const getRabbitmqConfigList = async () => {
    // 获取服务器列表
    const res = await listDmsRabbitMQ();
    if (res.code === 200) {
        state.rabbitmqList = (res.data as RabbitmqInfo[]) || [];
    }
};
getRabbitmqConfigList();
if (props.deviceId) {
    // 获取设备信息
    getDmsDeviceInfo();
}

// 返回
const router = useRouter();
const cancel = () => {
    console.log('返回');
    router.back();
};

const formRef = ref<FormInstance>();
// 保存
const save = async (fn?: () => void) => {
    console.log('保存');
    if (addId > -1) {
        if (fn) {
            fn();
            return;
        } else {
            proxy!.$modal.msgWarning('已经保存了');
            return;
        }
    }
    if (!formRef.value) return;

    formRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            if (typeof state.deviceInfo.hospitalId === 'string') {
                // 如果是字符串, 表明是初始化的时候, 传过来的是医院名称 没有进行修改
                state.deviceInfo.hospitalId = state.deviceInfo.hospitalVo?.id;
            }
            delete state.deviceInfo.hospitalVo;
            delete state.deviceInfo.status;
            delete state.deviceInfo.rabbitmqConfigVo;
            // 删除时间
            delete state.deviceInfo.createTime;
            delete state.deviceInfo.updateTime;
            delete state.deviceInfo.installationTime;
            if (props.deviceId) {
                // 编辑
                try {
                    const res = await updateDmsDevice(state.deviceInfo);
                    if (res.code === 200) {
                        proxy!.$modal.msgSuccess('保存成功');
                        if (fn) {
                            fn();
                            return;
                        }
                        // state.deviceInfo = (res.data || {}) as DmsDeviceInfo
                        // state.deviceInfo.hospitalId = (res.data as DmsDeviceInfo)?.hospitalVo?.id
                        // if ((res.data as DmsDeviceInfo)?.rabbitmqConfigVo?.id) {
                        //     state.deviceInfo.rabbitmqConfigId = (res.data as DmsDeviceInfo)?.rabbitmqConfigVo?.id!
                        // }
                        getDmsDeviceInfo();
                    }
                } catch (error) {
                }
            } else {
                // 新增
                try {
                    const res = await addDmsDevice(state.deviceInfo);
                    if (res.code === 200) {
                        proxy!.$modal.msgSuccess('保存成功');
                        emit('update:deviceId', (res.data as DmsDeviceInfo)?.id!);

                        if (fn) {
                            fn();
                            return;
                        }
                        // state.deviceInfo = (res.data || {}) as DmsDeviceInfo
                        // state.deviceInfo.hospitalId = (res.data as DmsDeviceInfo)?.hospitalVo?.id
                        addId = (res.data as DmsDeviceInfo)?.hospitalVo?.id || -1;
                        // if ((res.data as DmsDeviceInfo)?.rabbitmqConfigVo?.id) {
                        //     state.deviceInfo.rabbitmqConfigId = (res.data as DmsDeviceInfo)?.rabbitmqConfigVo?.id!
                        // }
                        getDmsDeviceInfo((res.data as DmsDeviceInfo).id);
                    }
                } catch (error) {
                }
            }
        } else {
            return false;
        }
    });
};
// 保存并返回
const saveAndBack = () => {
    save(cancel);
};

// guid是否可编辑
const guidStatus = ref(!!props.deviceId);
// guid编辑
const guidEdit = () => {
    proxy!.$modal
        .confirm('是否确认编辑GUID?')
        .then(() => {
            guidStatus.value = false;
        })
        .catch(() => {
        });
};
// guid失去焦点
const guidBlur = () => {
    guidStatus.value = true;
};

// 医院列表
const hospitalList = ref<any>([]);
const loading = ref(false);
const searchHospital = async (name: string) => {
    loading.value = true;
    getHospitalListByKeywords({ keywords: name })
        .then(response => {
            hospitalList.value = response.data;
        })
        .finally(() => {
            loading.value = false;
        });
};
</script>

<template>
    <div class="dms_device_info_base_container">
        <el-form ref="formRef" :model="state.deviceInfo" :rules="rules" label-position="top">
            <el-row class="form-con" justify="space-between">
                <el-col :span="24">
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="设备名称" prop="name">
                                <el-input
                                    v-model="state.deviceInfo.name"
                                    placeholder="请输入设备名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="设备英文名称" prop="enname">
                                <el-input
                                    v-model="state.deviceInfo.enName"
                                    placeholder="请输入设备英文名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="所在医院" prop="hospitalId">
                                <el-select
                                    v-model="state.deviceInfo.hospitalId"
                                    :loading="loading"
                                    :remote-method="searchHospital"
                                    filterable
                                    placeholder="请输入所在医院"
                                    remote
                                    remote-show-suffix
                                    reserve-keyword
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in hospitalList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="类型" prop="type">
                                <el-select v-model="state.deviceInfo.type" clearable placeholder="请选择类型">
                                    <el-option
                                        v-for="dict in evms_dms_device_type"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="parseInt(dict.value)"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="设备所在院区" prop="courtyard">
                                <el-input
                                    v-model="state.deviceInfo.courtyard"
                                    placeholder="请输入设备所在院区"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="GUID" prop="nativeId">
                                <el-input
                                    v-model="state.deviceInfo.nativeId"
                                    :disabled="guidStatus"
                                    placeholder="请输入设备编号"
                                    @blur="guidBlur"
                                >
                                    <template v-if="guidStatus" #suffix>
                                        <el-link :underline="false" type="primary" @click="guidEdit"
                                        >编辑
                                        </el-link
                                        >
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="设备所在地址" prop="address">
                                <el-input
                                    v-model="state.deviceInfo.address"
                                    placeholder="请输入设备所在地址"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="所连服务器" prop="rabbitmqConfigId">
                                <el-select
                                    v-model="state.deviceInfo.rabbitmqConfigId"
                                    placeholder="请选择所连服务器"
                                    size="large"
                                >
                                    <el-option
                                        v-for="item in state.rabbitmqList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row v-if="props.deviceId && state.deviceInfo.status" :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="状态">
                                <el-radio-group v-model="state.deviceInfo.status!.status" disabled>
                                    <el-radio label="T" size="large">开</el-radio>
                                    <el-radio label="F" size="large">关</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="设备最近开机/关机时间">
                                <el-date-picker
                                    v-model="state.deviceInfo.status.onofflineTime"
                                    disabled
                                    placeholder="请选择时间"
                                    type="datetime"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="NVR IP" prop="nvrIp">
                                <el-input
                                    v-model="state.deviceInfo.nvrIp"
                                    placeholder="请输入NVR IP"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="NVR TCP访问端口" prop="nvrPort">
                                <el-input
                                    v-model="state.deviceInfo.nvrPort"
                                    placeholder="请输入NVR TCP访问端口"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="NVR 访问密码" prop="nvrPwd">
                                <el-input
                                    v-model="state.deviceInfo.nvrPwd"
                                    placeholder="请输入NVR 访问密码"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="权重" prop="weight">
                                <el-input
                                    v-model="state.deviceInfo.weight"
                                    placeholder="请输入设备权重"
                                    type="number"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="props.deviceId" :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="注册时间">
                                <el-date-picker
                                    v-model="state.deviceInfo.createTime"
                                    disabled
                                    placeholder="请选择时间"
                                    type="datetime"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="18">
                            <el-form-item label="描述">
                                <el-input
                                    v-model="state.deviceInfo.description"
                                    placeholder="请输入描述"
                                    type="textarea"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="24" style="height: 100px">
                    <el-row justify="end">
                        <el-button @click="cancel">返回</el-button>
                        <el-button type="primary" @click="() => save()">保存</el-button>
                        <el-button type="primary" @click="() => saveAndBack()">保存并返回</el-button>
                    </el-row>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
.dms_device_info_base_container {
    :deep(.el-card__body) {
        padding: 0 !important;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0 0 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        height: 40px !important;
    }

    :deep(.el-input__wrapper) {
        width: 100% !important;
    }

    :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
        width: 100% !important;
    }
}
</style>
