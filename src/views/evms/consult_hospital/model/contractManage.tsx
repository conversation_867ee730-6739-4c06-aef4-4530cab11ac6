import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn,
    FormRules,
    ElUpload,
    UploadRawFile,
    genFileId,
    UploadFiles,
    UploadFile,
    UploadUserFile
} from 'element-plus';
import { RefsKes } from '@/type/common';
import {
    hospitalContract,
    hospitalContractUpdate,
    HospitalContract,
    DeviceVos
} from '@/api/evms/consult_hospital';
import { ContractListType, getContractDetail, ContractDetail, UploadDetail } from '@/api/evms/expert';
import ContractList from '@/views/evms/expert/model/contractList';
import moment from 'moment';

export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const state = reactive({
            visible: false,
            form: {} as HospitalContract,
            rules: {
                daterange: [
                    {
                        required: true,
                        message: '请选择签约起止时间',
                        trigger: 'change'
                    },
                ] as any[],
                contractCount: [{ required: true, message: '合同次数不能为空', trigger: 'change' }] as any[]
            },
            record: null as DeviceVos | null,
            uploadAction: import.meta.env.VITE_APP_BASE_API + '/evms/contract/uploadFile',
            uploadLoading: false
        });

        let instance: RefsKes;
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });
        function handleOk() {
            if (state.uploadLoading) {
                return proxy?.$modal.msgWarning('文件上传中,请稍等.....');
            }
            instance.refs.formRef.validate((valid: any, fields: any) => {
                if (valid) {
                    state.form.signingStartTimeStamp = moment(state.form.daterange[0]).valueOf();
                    state.form.signingEndTimeStamp = moment(state.form.daterange[1]).valueOf();
                    if (!state.form.sysFileId) {
                        state.form.removeSysFileId = true;
                    } else {
                        state.form.removeSysFileId = false;
                    }
                    if (state.record?.contractId) {
                        // 已签约
                        hospitalContractUpdate(state.form).then(res => {
                            proxy?.$modal.msgSuccess('操作成功');
                            close();
                            emit('ok');
                        });
                    } else {
                        // 未签约
                        hospitalContract(state.form).then(res => {
                            proxy?.$modal.msgSuccess('操作成功');
                            close();
                            emit('ok');
                        });
                    }
                }
            });
        }
        function close() {
            state.visible = false;
        }

        function show(record: DeviceVos) {
            state.visible = true;
            instance.refs.formRef && instance.refs.formRef.resetFields();
            state.record = record;

            if (state.record.contractId) {
                getContractDetail({ contractId: state.record.contractId }).then(res => {
                    state.form = {
                        contractAmount: res.data.contractAmount,
                        contractCount: res.data.contractCount,
                        contractId: res.data.contractId,
                        contractTotalAmount: res.data.contractTotalAmount,
                        contractUrl: res.data.contractUrl,
                        daterange: [
                            moment(res.data.signingStartTime).format('YYYY-MM-DD HH:mm:ss'),
                            moment(res.data.signingEndTime).format('YYYY-MM-DD HH:mm:ss')
                        ],
                        sysFileId: res.data.sysFileId,
                        fileOriginalName: res.data.fileOriginalName,
                        deviceId: res.data.deviceId
                    } as HospitalContract;
                });
            } else {
                state.form = {
                    deviceId: state.record.deviceId
                } as HospitalContract;
            }
        }
        function handleContractShow() {
            instance.refs.ContractListRef.show();
        }
        function contractOk(value: ContractListType) {
            state.form.contractUrl = value.fileUrl;
            state.form.sysFileId = value.sysFileId;
            state.form.fileOriginalName = value.fileOriginalName;
        }

        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                },
            },
            upload() {
                return (
                    <ElButton loading={state.uploadLoading} type="primary" icon="Upload">
                        {state.uploadLoading ? '上传中' : '上传合同'}
                    </ElButton>
                );
            },
            append() {
                return (
                    // <ElButton type="primary" onClick={handleContractShow}>
                    //     合同选择
                    // </ElButton>
                    <ElUpload
                        ref="uploadRef"
                        class="upload-demo"
                        action={state.uploadAction}
                        limit={1}
                        on-exceed={handleExceed}
                        auto-upload={true}
                        v-slots={{ trigger: slots.upload }}
                        show-file-list={false}
                        on-success={uploadSuccess}
                        on-progress={uploading}
                        on-error={uploadError}
                    ></ElUpload>
                );
            },
        };

        function handleExceed(files: File[], uploadFiles: UploadUserFile[]) {
            instance.refs.uploadRef!.clearFiles();
            const file = files[0] as UploadRawFile;
            file.uid = genFileId();
            instance.refs.uploadRef!.handleStart(file);
            instance.refs.uploadRef!.submit();
        }

        function uploadSuccess(
            response: UploadDetail | any,
            uploadFile?: UploadFile,
            uploadFiles?: UploadFiles
        ) {
            state.form.sysFileId = response.data.sysFileId;
            state.form.fileOriginalName = response.data.sysFile.original;
            state.form.contractUrl = response.data.sysFile.url;
            state.uploadLoading = false;
        }
        function uploading() {
            state.uploadLoading = true;
        }

        function uploadError() {
            state.uploadLoading = false;
        }
        function delContact() {
            state.form.sysFileId = null as any;
            state.form.fileOriginalName = '';
            state.form.contractUrl = '';
        }
        expose({
            show
        });
        return () => (
            <ElDialog
                title="签约管理"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                <ElForm ref="formRef" model={state.form} rules={state.rules} labelWidth="150px">
                    <ElRow>
                        <ElCol span={12}>
                            <ElFormItem label="签约起止时间" prop="daterange">
                                <ElDatePicker
                                    v-model={state.form.daterange}
                                    value-format="YYYY-MM-DD"
                                    type="daterange"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                />
                            </ElFormItem>
                        </ElCol>
                        {/* <ElCol span={12}>
                            <ElFormItem label="合同金额(元)" prop="contractTotalAmount">
                                <ElInputNumber precision={2} v-model={state.form.contractTotalAmount}
                                    placeholder="合同金额"></ElInputNumber>
                            </ElFormItem>
                        </ElCol> */}
                        <ElCol span={12}>
                            <ElFormItem label="合同次数" prop="contractCount">
                                <ElInputNumber
                                    v-model={state.form.contractCount}
                                    placeholder="合同次数"
                                ></ElInputNumber>
                            </ElFormItem>
                        </ElCol>
                        <ElCol span={12}>
                            <ElFormItem label="合同">
                                <div style={'display: flex;'}>
                                    {/* <ElInput
                                        readonly
                                        v-model={state.form.fileOriginalName}
                                        placeholder="合同链接"
                                        v-slots={{ append: slots.append }}
                                    /> */}
                                    <a
                                        style={'margin-right: 10px;color:#0581ce;text-decoration: underline;'}
                                        target="_blank"
                                        href={state.form.contractUrl}
                                    >
                                        {state.form.fileOriginalName}
                                    </a>

                                    {slots.append()}
                                    <ElButton onClick={delContact} style={'margin-left: 10px;'} link>
                                        删除
                                    </ElButton>
                                </div>
                            </ElFormItem>
                        </ElCol>
                        <ElCol span={12}>
                            <ElFormItem label="单次金额(元)" prop="contractAmount">
                                <ElInputNumber
                                    precision={2}
                                    v-model={state.form.contractAmount}
                                    placeholder="单次金额"
                                ></ElInputNumber>
                            </ElFormItem>
                        </ElCol>
                    </ElRow>
                </ElForm>
                {/* <ContractList onOk={contractOk} ref="ContractListRef"></ContractList> */}
            </ElDialog>
        );
    },
});
