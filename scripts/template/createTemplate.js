import { ElCheckbox, ElCheckboxGroup, ElRadio, ElRadioGroup } from 'element-plus';

/**
 * 防止列名出现以下格式
 *      更新时间(xxx)
 */
export const getColumnName = (function () {
    const cache = {};
    return column => {
        if (cache[column.columnComment]) {
            return cache[column.columnComment];
        }
        if (column.columnComment) {
            let index = column.columnComment.indexOf('(');
            if (index === -1) {
                index = column.columnComment.indexOf('，');
            }
            if (index !== -1) {
                let columnName = column.columnComment.slice(0, index);
                cache[column.columnComment] = columnName;
                return columnName;
            }
        }
        cache[column.columnComment] = column.columnComment;
        return column.columnComment;
    };
})();
export const typeEnum = {
    checkbox: 'checkbox',
    datetime: 'datetime',
    imageUpload: 'imageUpload',
    input: 'input',
    textarea: 'textarea',
    select: 'select',
    radio: 'radio',
    editor: 'editor',
    fileUpload: 'fileUpload'
};
/**
 *
 * @param {*} column 列数据
 * @param {*} type 枚举类型 当前创建类型  1 搜索 2 编辑 3 列数据
 */
export function createInput(column, type) {
    let comment = getColumnName(column);
    if (type === 1 && column.htmlType === typeEnum.input) {
        return `<ElInput
                    v-model={state.queryParams.${column.javaField}}
                    placeholder="请输入${comment}"
                    clearable
                    style="width: 240px"
                    onKeydown={triggerQuery}/>`;
    } else if (type === 2) {
        return `<ElInput
                    v-model={state.form.${column.javaField}}
                    placeholder="请输入${comment}"
                    ${column.htmlType === typeEnum.textarea ? 'type="textarea"' : ''}/>`;
    }
}

export function createFormItem(column, type, content = '') {
    let comment = getColumnName(column);
    if (type === 1) {
        return `<ElFormItem
        label="${comment}"
        prop="${column.javaField}">
        ${content}
    </ElFormItem>`;
    } else if (type === 2) {
        return `<ElCol span={12}>
            <ElFormItem
            labelWidth="150px"
            label="${comment}"
            prop="${column.javaField}">
            ${content}
        </ElFormItem>
        </ElCol>`;
    }
}

export function createTableColumn(column, slots, content = '') {
    let comment = getColumnName(column);
    let width = 0;
    if (column.htmlType === typeEnum.datetime) {
        width = 180;
    } else if (column.htmlType === typeEnum.imageUpload) {
        width = 100;
    }
    return `<ElTableColumn
                label="${comment}"
                align="center"
                prop="${column.javaField}"
                ${width ? 'width="' + width + '"' : ''}
                ${slots ? `v-slots={slots.column.${column.javaField}}` : ''}
            />`;
}
export function createUploadFile(column, type) {
    if (column.htmlType === typeEnum.imageUpload) {
        return `<ImageUpload v-model="state.form.${column.javaField}" />`;
    } else if (column.htmlType === typeEnum.fileUpload) {
        return `<FileUpload v-model="state.form.${column.javaField}" />`;
    }
}
export function createSelect(column, type) {
    let comment = getColumnName(column);
    let dictType = column.dictType;
    let bindKey = type === 1 ? 'queryParams' : 'form';
    if (dictType) {
        return `<ElSelect
                    v-model={state.${bindKey}.${column.javaField}}
                    placeholder="请选择${comment}"
                    ${type === 2 ? '' : 'clearable'}>
                        {
                            ${dictType}.value.map(dict=>(
                                <ElOption
                                    key={dict.value}
                                    label={dict.label}
                                    value={dict.value}
                                >
                                </ElOption>
                            ))
                        }
                </ElSelect>`;
    } else {
        return `<ElSelect
                    v-model={state.${bindKey}.${column.javaField}}
                    placeHolder="请选择${comment}"
                    ${type === 2 ? '' : 'clearable'}>
                    <ElOption label="请选择字典生成" value=""></ElOption>
                </ElSelect>`;
    }
}

export function createRadio(column, type) {
    let dictType = column.dictType;
    let bindKey = type === 1 ? 'queryParams' : 'form';
    if (dictType) {
        return `<ElRadioGroup
                v-model={state.${bindKey}.${column.javaField}}>
                {
                    ${dictType}.value.map(dict=>(
                        <ElRadio
                            key={dict.value}
                            label={dict.value}
                        >
                            {dict.label}
                        </ElRadio>
                    ))
                }
            </ElRadioGroup>`;
    } else {
        return `<ElRadioGroup
                    v-model={state.${bindKey}.${column.javaField}}
                    ${type === 2 ? '' : 'clearable'}>
                    <ElRadio>
                        请选择字典生成
                    </ElRadio>
                </ElRadioGroup>`;
    }
}
export function createCheckbox(column, type) {
    let dictType = column.dictType;
    let bindKey = type === 1 ? 'queryParams' : 'form';
    if (dictType) {
        return `<ElCheckboxGroup
                    v-model={state.${bindKey}.${column.javaField}}>
                    {
                        ${dictType}.value.map(dict=>(
                            <ElCheckbox
                                key={dict.value}
                                label={dict.value}
                            >
                                {dict.label}
                            </ElCheckbox>
                        ))
                    }
                </ElCheckboxGroup>`;
    } else {
        return `<ElCheckboxGroup
                    v-model={state.${bindKey}.${column.javaField}}
                    ${type === 2 ? '' : 'clearable'}>
                    <ElCheckbox>
                        请选择字典生成
                    </ElCheckbox>
                </ElCheckboxGroup>`;
    }
}
export function createDateTime(column, type) {
    let comment = getColumnName(column);
    if (type === 1) {
        if (column.queryType === 'BETWEEN') {
            return `<ElDatePicker
                        v-model={state.daterange}
                        value-format="YYYY-MM-DD"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期" />`;
        } else {
            return `<ElDatePicker
                            clearable
                            v-model={state.queryParams.${column.javaField}}
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="请选择${comment}" />`;
        }
    } else if (type === 2) {
        return `<ElDatePicker
                    clearable
                    v-model={state.form.${column.javaField}}
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择${comment}" />`;
    }
}
export function createRichText(column, type) {
    return `<Editor v-model="state.form.${column.javaField}" min-height={192} />`;
}
export function createSearch(columns) {
    return columns
        .map(column => {
            if (column.isQuery != 1) return '';
            let content = '';
            if (column.htmlType === typeEnum.input || column.htmlType === typeEnum.textarea) {
                content = createInput(column, 1);
            } else if (column.htmlType === typeEnum.datetime) {
                content = createDateTime(column, 1);
            } else if (column.htmlType === typeEnum.radio) {
                content = createSelect(column, 1);
            } else if (column.htmlType === typeEnum.select) {
                content = createSelect(column, 1);
            }
            if (content) content = createFormItem(column, 1, content);
            return content;
        })
        .join('');
}

export function createEdit(columns) {
    let ignoresKey = ['createBy', 'createTime', 'updateBy', 'updateTime'];
    let start = false;
    let end = false;
    let index = 0;
    let str = columns
        .map(column => {
            if (column.isInsert != '1' || ignoresKey.includes(column.javaField)) return '';
            let content = '';

            if (column.htmlType === typeEnum.input || column.htmlType === typeEnum.textarea) {
                content += createInput(column, 2);
            } else if (column.htmlType === typeEnum.imageUpload) {
                content += createUploadFile(column, 2);
            } else if (column.htmlType === typeEnum.editor) {
                content += createRichText(column, 2);
            } else if (column.htmlType === typeEnum.radio) {
                content += createRadio(column, 2);
            } else if (column.htmlType === typeEnum.checkbox) {
                content += createCheckbox(column, 2);
            } else if (column.htmlType === typeEnum.select) {
                content += createSelect(column, 2);
            } else if (column.htmlType === typeEnum.datetime) {
                content += createDateTime(column, 2);
            }
            if (content) {
                content = createFormItem(column, 2, content);
                if (index % 2 === 0) {
                    content = '<ElRow>' + content;
                    start = true;
                    end = false;
                }
                if (index % 2 === 1) {
                    content += '</ElRow>';
                    end = true;
                }
                index += 1;
            }

            return content;
        })
        .join('');
    if (start && !end) {
        str += '</ElRow>';
    }
    return str;
}
export function createList(columns) {
    return columns
        .map(column => {
            if (column.isList != 1 || !column.javaField) return;
            let content = '';
            let slots = false;
            if (column.htmlType === typeEnum.imageUpload) {
                slots = true;
            } else if (column.htmlType === typeEnum.datetime) {
                slots = true;
            } else if (column.dictType) {
                slots = true;
            }
            return createTableColumn(column, slots, content);
        })
        .join('');
}

export function slotsContent(slots, column) {
    if (slots === 1) {
        return `<ImagePreview src={record.${column.javaField} || ""} width={50} height={50} />`;
    } else if (slots === 2) {
        return `<span>{record.${column.javaField}?moment(record.${column.javaField}).format("YYYY-MM-DD"):"--"}</span>`;
    } else if (slots === 3) {
        if (column.htmlType === typeEnum.checkbox) {
            return `<DictTag options={${column.dictType}.value} value="record.${column.javaField} ? record.${column.javaField}.split(',') : []"/>`;
        } else {
            return ` <DictTag options={${column.dictType}.value} value={record.${column.javaField}}/>`;
        }
    }
}

export function createSlots(columns, name) {
    return (
        columns.reduce(
            (sub, column, index) => {
                if (column.isList != 1 || !column.javaField) return sub;
                let content = '';
                let slots = false;
                if (column.htmlType === typeEnum.imageUpload) {
                    slots = 1;
                } else if (column.htmlType === typeEnum.datetime) {
                    slots = 2;
                } else if (column.dictType) {
                    slots = 3;
                }
                if (slots) {
                    if (index !== 0) {
                        sub += '    ';
                    }
                    sub += `${column.javaField}({row:record}:{row:${name}}){
                    return (${slotsContent(slots, column)})
                },
            `;
                }
                return sub || '';
            },
            `column:{
                `
        ) + '}'
    );
}

export function createFile(columns) {
}

export const javaTypeEnum = {
    LocalDateTime: 'string',
    String: 'string',
    Integer: 'number',
    Long: 'number'
};

export function createDaoType(columns) {
    return columns.reduce((sub, column) => {
        let res = {};
        sub += `${column.javaField}: ${javaTypeEnum[column.javaType]}
    `;
        return sub;
    }, '');
}
