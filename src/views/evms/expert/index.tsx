import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn,
    ElProgress,
    ElCard
} from 'element-plus';
import { defineComponent, getCurrentInstance, ref, reactive, toRefs, onMounted, watch } from 'vue';
import RightToolar from '@/components/RightToolbar/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import DictTag from '@/components/DictTag/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import FileUpload from '@/components/FileUpload/index.vue';
import {
    listExpert,
    EvmsConsultExpert,
    getStatisticsData,
    StatisticsData,
    activateOrClose
} from '@/api/evms/expert';
import moment from 'moment';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import AddDoc from './model/addDoc';
import { RefsKes } from '@/type/common';
import * as echarts from 'echarts';
import '@/views/evms/expert/css/index.css';
import EditExpert from './model/editExpert';
import useCommonStore from '@/store/modules/common';
import router from '@/router';

interface QueryParams extends Partial<EvmsConsultExpert> {
    pageNum: number;
    pageSize: number;
}
export default defineComponent({
    setup() {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;
        const state = reactive({
            expertList: [] as EvmsConsultExpert[],
            loading: true,
            showSearch: true,
            ids: [] as Array<string | number>,
            single: true,
            total: 0,
            multiple: false,
            daterange: [] as string[],
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                expertName: '',
                signingStatus: null as unknown as number
            } as QueryParams,
            statistics: {
                dyingPeriodCount: 0,
                mocurrentMonth: {
                    serviceCount: 0,
                    signingCount: 0
                },
                lastMonth: {
                    serviceCount: 0,
                    signingCount: 0
                },
                signingCount: 0,
                unsignedCount: 0
            } as StatisticsData
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function getList() {
            state.loading = true;
            if (state.daterange.length === 2) {
                state.queryParams.startTimeStamp = moment(state.daterange[0]).valueOf();
                state.queryParams.endTimeStamp = moment(state.daterange[1]).valueOf();
            }
            listExpert(state.queryParams).then(response => {
                state.expertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function statisticsData() {
            getStatisticsData({
                type: 'EXPERT'
            }).then(res => {
                state.statistics = res.data;
            });
        }

        statisticsData();

        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }

        watch(
            () => state.daterange,
            val => {
                if (!val) {
                    state.daterange = [];
                    state.queryParams.startTimeStamp = null;
                    state.queryParams.endTimeStamp = null;
                }
            }
        );

        function resetQuery() {
            state.daterange = [];
            state.queryParams.startTimeStamp = null;
            state.queryParams.endTimeStamp = null;
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleAdd() {
            instance.refs.AddDocRef.show();
        }

        function handleUpdate(record: EvmsConsultExpert) {
            instance.refs.EditExpertRef.show(record);
        }

        function handleConsultationStatus(record: EvmsConsultExpert) {
            activateOrClose({ exprtId: record.id as number }).then(res => {
                getList();
                proxy?.$modal.msgSuccess(record.consultationStatus ? '关闭成功' : '开通成功');
            });
        }

        function handleContract(record: EvmsConsultExpert) {
            proxy?.$modal
                .confirm('是否确认发送签约？')
                .then(() => {
                    // return delConsultHospital(_ids);
                })
                .then(() => {
                    proxy?.$modal.msgSuccess('发送成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        function serviceCountClick(record: EvmsConsultExpert) {
            if (record.serviceCount > 0) {
                router.push({
                    path: '/consultation/ordert'
                });
                useCommonStore().setLocal({
                    name: 'evmsOrdertQuery',
                    status: {
                        orderStatus: 'OK',
                        realName: record.realName,
                        expertId: record.id
                    }
                });
            }
        }

        const slots = {
            column: {
                serviceCount({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <p onClick={() => serviceCountClick(record)} style="color:#0581ce;cursor: pointer;">
                            {record.serviceCount}
                        </p>
                    );
                },
                signingStartTimeStamp({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingStartTimeStamp
                                ? moment(record.signingStartTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingEndTimeStamp({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingEndTimeStamp
                                ? moment(record.signingEndTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingStatus({ row: record }: { row: EvmsConsultExpert }) {
                    return <p>{record.signingStatus ? '已签约' : '未签约'}</p>;
                }
            },
            actionSlots({ row: record }: { row: EvmsConsultExpert }) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:expert:edit']]}
                            link
                            type="primary"
                            onClick={() => handleUpdate(record)}
                        >
                            编辑
                        </ElButton>
                        <ElButton
                            link
                            v-hasPermi={[['evms:expert:edit']]}
                            type="primary"
                            onClick={() => handleConsultationStatus(record)}
                        >
                            {record.consultationStatus ? '关闭' : '开通'}
                        </ElButton>
                        {/* <ElButton
              link
              type="primary"
              onClick={() => handleContract(record)}
            >
              发送签约
            </ElButton> */}
                    </>
                );
            },
        };
        getList();
        return () => (
            <div class="app-container">
                {state.statistics ? (
                    <ElCard class="progress-card">
                        <div class="progress-div">
                            <ElProgress
                                color="red"
                                type="circle"
                                percentage={
                                    state.statistics.lastMonth.serviceCount > 0
                                        ? parseInt(
                                            (state.statistics.mocurrentMonth.serviceCount /
                                                state.statistics.lastMonth.serviceCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">本月服务次数/上月服务次数</p>
                                <p class="progress-text2">
                                    {state.statistics.mocurrentMonth.serviceCount}/
                                    {state.statistics.lastMonth.serviceCount}
                                </p>
                            </div>
                        </div>
                        <div class="progress-div">
                            <ElProgress
                                color="blue"
                                type="circle"
                                percentage={
                                    state.statistics.lastMonth.signingCount > 0
                                        ? parseInt(
                                            (state.statistics.mocurrentMonth.signingCount /
                                                state.statistics.lastMonth.signingCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">本月签约专家/上月签约专家</p>
                                <p class="progress-text2">
                                    {state.statistics.mocurrentMonth.signingCount}/
                                    {state.statistics.lastMonth.signingCount}
                                </p>
                            </div>
                        </div>
                        <div class="progress-div">
                            <ElProgress
                                color="purple"
                                type="circle"
                                percentage={
                                    state.statistics.signingCount > 0
                                        ? parseInt(
                                            (state.statistics.dyingPeriodCount /
                                                state.statistics.signingCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">临期/已签约</p>
                                <p class="progress-text2">
                                    {state.statistics.dyingPeriodCount}/{state.statistics.signingCount}
                                </p>
                            </div>
                        </div>
                        <div class="progress-div">
                            <ElProgress
                                color="green"
                                type="circle"
                                percentage={
                                    state.statistics.unsignedCount > 0
                                        ? parseInt(
                                            (state.statistics.signingCount /
                                                state.statistics.unsignedCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">已签约/未签约</p>
                                <p class="progress-text2">
                                    {state.statistics.signingCount}/{state.statistics.unsignedCount}
                                </p>
                            </div>
                        </div>
                    </ElCard>
                ) : (
                    ''
                )}

                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="专家名称" prop="expertName">
                        <ElInput
                            v-model={state.queryParams.expertName}
                            placeholder="请输入专家名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="签约状态" prop="signingStatus">
                        <ElSelect
                            v-model={state.queryParams.signingStatus}
                            placeholder="请选择签约状态"
                            clearable
                        >
                            <ElOption key={0} label="未签约" value={0}></ElOption>
                            <ElOption key={1} label="已签约" value={1}></ElOption>
                        </ElSelect>
                    </ElFormItem>
                    <ElFormItem label="签约起止时间" prop="daterange">
                        <ElDatePicker
                            v-model={state.daterange}
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                        />
                    </ElFormItem>

                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}

                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['evms:expert:add']]}
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={handleAdd}
                        >
                            添加专家
                        </ElButton>
                    </ElCol>
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable v-loading={state.loading} data={state.expertList}>
                    <ElTableColumn label="专家名称" align="center" prop="realName" />
                    <ElTableColumn
                        label="签约状态"
                        align="center"
                        prop="signingStatus"
                        v-slots={slots.column.signingStatus}
                    />
                    <ElTableColumn
                        label="服务总次数"
                        align="center"
                        v-slots={slots.column.serviceCount}
                        prop=""
                    />
                    <ElTableColumn
                        label="签约时间"
                        align="center"
                        prop="signingStartTimeStamp"
                        width="180"
                        v-slots={slots.column.signingStartTimeStamp}
                    />
                    <ElTableColumn
                        label="到期时间"
                        align="center"
                        prop="signingEndTimeStamp"
                        width="180"
                        v-slots={slots.column.signingEndTimeStamp}
                    />
                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    ></ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
                <AddDoc onOk={() => handleQuery()} ref="AddDocRef"></AddDoc>
                <EditExpert onOk={() => handleQuery()} ref="EditExpertRef"></EditExpert>
            </div>
        );
    },
});
