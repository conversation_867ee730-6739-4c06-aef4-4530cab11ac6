<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
            <el-form-item label="文件名" prop="name">
                <el-input
                    v-model="queryParams.name"
                    clearable
                    placeholder="请输入文件名"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="桶" prop="bucket">
                <el-input
                    v-model="queryParams.bucket"
                    clearable
                    placeholder="请输入桶"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="原文件名" prop="original">
                <el-input
                    v-model="queryParams.original"
                    clearable
                    placeholder="请输入原文件名"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="访问地址" prop="url">
                <el-input
                    v-model="queryParams.url"
                    clearable
                    placeholder="请输入访问地址"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="文件类型" prop="type">
                <el-select v-model="queryParams.type" clearable placeholder="请输入文件类型">
                    <el-input
                        v-model="queryParams.type"
                        clearable
                        placeholder="请输入文件类型"
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="文件大小" prop="size">
                <el-input
                    v-model="queryParams.size"
                    clearable
                    placeholder="请输入文件大小"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="创建人" prop="createBy">
                <el-input
                    v-model="queryParams.createBy"
                    clearable
                    placeholder="请输入创建人"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:file:add']" icon="Plus" plain type="primary" @click="handleAdd"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:file:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:file:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:file:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="FileList" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column align="center" label="文件名" prop="name" />
            <el-table-column align="center" label="桶" prop="bucket" />
            <el-table-column align="center" label="原文件名" prop="original" />
            <el-table-column align="center" label="访问地址" prop="url" />
            <el-table-column align="center" label="文件类型" prop="type" />
            <el-table-column align="center" label="文件大小" prop="size" />
            <el-table-column align="center" label="创建人" prop="createBy" />
            <el-table-column align="center" label="创建时间" prop="createTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['sys:file:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button
                    >
                    <el-button
                        v-hasPermi="['sys:file:remove']"
                        icon="Delete"
                        link
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改文件管理对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="FileRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="文件名" prop="name">
                    <el-input v-model="form.name" placeholder="请输入文件名" />
                </el-form-item>
                <el-form-item label="桶" prop="bucket">
                    <el-input v-model="form.bucket" placeholder="请输入桶" />
                </el-form-item>
                <el-form-item label="原文件名" prop="original">
                    <el-input v-model="form.original" placeholder="请输入原文件名" />
                </el-form-item>
                <el-form-item label="访问地址" prop="url">
                    <el-input v-model="form.url" placeholder="请输入访问地址" />
                </el-form-item>
                <el-form-item label="文件类型" prop="type">
                    <el-input v-model="form.type" placeholder="请输入文件类型" />
                </el-form-item>
                <el-form-item label="文件大小" prop="size">
                    <el-input v-model="form.size" placeholder="请输入文件大小" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="File" setup>
import { listFile, getFile, delFile, addFile, updateFile } from '@/api/oss/file';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const FileList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        bucket: undefined,
        original: undefined,
        url: undefined,
        type: undefined,
        size: undefined,
        createBy: undefined
    },
    rules: {
        name: [{ required: true, message: '文件名不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询文件管理列表 */
function getList() {
    loading.value = true;

    listFile(queryParams.value).then((response: any) => {
        FileList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        name: undefined,
        bucket: undefined,
        original: undefined,
        url: undefined,
        type: undefined,
        size: undefined,
        createBy: undefined,
        createTime: undefined
    };
    proxy!.resetForm('FileRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加文件管理';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getFile(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改文件管理';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['FileRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateFile(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addFile(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除文件管理编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delFile(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'sys/File/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `File_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
