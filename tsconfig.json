{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "baseUrl": "./", "paths": {"@/*": ["src/*"], "~/*": ["/*"]}, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "scripts/**/*.vue", "scripts/**/*.tsx", "test.ts"], "references": [{"path": "./tsconfig.node.json"}], "ts-node": {"compilerOptions": {"module": "CommonJS", "esModuleInterop": true}}}