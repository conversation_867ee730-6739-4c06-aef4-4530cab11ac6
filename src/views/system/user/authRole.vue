<template>
    <div class="app-container">
        <h4 class="form-header h4">基本信息</h4>
        <el-form :model="form" label-width="80px">
            <el-row>
                <el-col :span="8" :offset="2">
                    <el-form-item label="用户昵称" prop="nickname">
                        <el-input v-model="form.nickname" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="8" :offset="2">
                    <el-form-item label="登录账号" prop="username">
                        <el-input v-model="form.username" disabled />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <h4 class="form-header h4">角色信息</h4>
        <el-table
            ref="roleRef"
            v-loading="loading"
            :row-key="getRowKey"
            :data="roles.slice((pageNum - 1) * pageSize, pageNum * pageSize)"
            @rowClick="clickRow"
            @selectionChange="handleSelectionChange"
        >
            <el-table-column label="序号" width="55" type="index" align="center">
                <template #default="scope">
                    <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
            <el-table-column label="角色编号" align="center" prop="id" />
            <el-table-column label="角色名称" align="center" prop="name" />
            <el-table-column label="权限字符" align="center" prop="code" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" v-model:page="pageNum" v-model:limit="pageSize" :total="total" />

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button type="primary" @click="submitForm()">提交</el-button>
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="AuthRole" lang="ts">
import { getAuthRole, updateAuthRole } from '@/api/system/user';
import { parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const loading = ref(true);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const roleIds = ref<any[]>([]);
const roles = ref<any[]>([]);
const form = ref({
    nickname: undefined,
    username: undefined,
    id: undefined,
});

/** 单击选中行数据 */
function clickRow(row: any) {
    (proxy?.$refs['roleRef'] as any).toggleRowSelection(row);
}
/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    roleIds.value = selection.map(item => item.id);
}
/** 保存选中的数据编号 */
function getRowKey(row: any) {
    return row.id;
}
/** 关闭按钮 */
function close() {
    const obj = { path: '/system/user' };
    proxy!.$tab.closeOpenPage(obj);
}
/** 提交按钮 */
function submitForm() {
    const id = form.value.id;
    const rIds = roleIds.value.join(',');
    updateAuthRole({ userId: id, roleIds: rIds }).then(response => {
        proxy!.$modal.msgSuccess('授权成功');
        close();
    });
}

(() => {
    const userId = route.params && route.params.userId;
    if (userId) {
        loading.value = true;
        getAuthRole(userId).then((response: any) => {
            let data = response.data;
            let userRoles: any[] = data.user.roles;
            let userRoleIds = userRoles.map(r => r.id);
            form.value = data.user;
            roles.value = data.roles;
            total.value = roles.value.length;
            nextTick(() => {
                roles.value.forEach(row => {
                    if (userRoleIds.includes(row.id)) {
                        (proxy?.$refs['roleRef'] as any).toggleRowSelection(row);
                    }
                });
            });
            loading.value = false;
        });
    }
})();
</script>
