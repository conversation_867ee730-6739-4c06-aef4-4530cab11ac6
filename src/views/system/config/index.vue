<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
            <el-form-item label="参数名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    clearable
                    placeholder="请输入参数名称"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="参数键名" prop="paramKey">
                <el-input
                    v-model="queryParams.paramKey"
                    clearable
                    placeholder="请输入参数键名"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="系统内置" prop="builtin">
                <el-select v-model="queryParams.builtin" clearable placeholder="系统内置">
                    <el-option
                        v-for="dict in sys_yes_no"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
                <el-date-picker
                    v-model="dateRange"
                    end-placeholder="结束日期"
                    range-separator="-"
                    start-placeholder="开始日期"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:config:add']" icon="Plus" plain type="primary" @click="handleAdd"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:config:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:config:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:config:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:config:remove']"
                    icon="Refresh"
                    plain
                    type="danger"
                    @click="handleRefreshCache"
                >刷新缓存
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="configList" @selectionChange="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column align="center" label="参数主键" prop="id" />
            <el-table-column :show-overflow-tooltip="true" align="center" label="参数名称" prop="name" />
            <el-table-column :show-overflow-tooltip="true" align="center" label="参数键名" prop="paramKey" />
            <el-table-column align="center" label="参数键值" prop="paramValue" />
            <el-table-column align="center" label="系统内置" prop="builtin">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.builtin" />
                </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="备注" prop="remark" />
            <el-table-column align="center" label="创建时间" prop="createTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="150">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['sys:config:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button
                    >
                    <el-button
                        v-hasPermi="['sys:config:remove']"
                        icon="Delete"
                        link
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改参数配置对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="configRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="参数名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入参数名称" />
                </el-form-item>
                <el-form-item label="参数键名" prop="paramKey">
                    <el-input v-model="form.paramKey" placeholder="请输入参数键名" />
                </el-form-item>
                <el-form-item label="参数键值" prop="paramValue">
                    <el-input
                        v-model="form.paramValue"
                        autosize
                        placeholder="请输入参数键值"
                        type="textarea"
                    />
                </el-form-item>
                <el-form-item label="系统内置" prop="builtin">
                    <el-radio-group v-model="form.builtin">
                        <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
                            dict.label
                            }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="Config" setup>
/* eslint-disable camelcase */
import { listConfig, getConfig, delConfig, addConfig, updateConfig, refreshCache } from '@/api/system/config';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = proxy!.useDict('sys_yes_no');

const configList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const dateRange = ref<any[]>([]);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        paramKey: undefined,
        builtin: undefined
    },
    rules: {
        name: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }],
        paramKey: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }],
        paramValue: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询参数列表 */
function getList() {
    loading.value = true;
    listConfig(proxy?.addDateRange(queryParams.value, dateRange.value)).then((response: any) => {
        configList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        name: undefined,
        paramKey: undefined,
        paramValue: undefined,
        builtin: '1',
        remark: undefined
    };
    proxy?.resetForm('configRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = [];
    proxy?.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加参数';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const id = row.id || ids.value;
    getConfig(id).then(response => {
        form.value = response.data;
        form.value.paramValue = JSON.stringify(JSON.parse(form.value.paramValue), null, 2);
        open.value = true;
        title.value = '修改参数';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['configRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id) {
                updateConfig(form.value).then(response => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addConfig(form.value).then(response => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const configIds = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除参数编号为"' + configIds + '"的数据项？')
        .then(function() {
            return delConfig(configIds);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch((e: any) => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy?.download(
        'sys/config/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `config_${new Date().getTime()}.xlsx`
    );
}

/** 刷新缓存按钮操作 */
function handleRefreshCache() {
    refreshCache().then(() => {
        proxy?.$modal.msgSuccess('刷新缓存成功');
    });
}

getList();
</script>
