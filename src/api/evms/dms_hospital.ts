import request, { Http } from '@/utils/request';
import { DmsHospitalInfo, DmsHospitalListResponse, HospitalListParams } from '@/model/dms_hospital';
import { AxiosPromise } from 'axios';
import { BaseApi } from '@/model/api';

// 查询DMS医院列表
export function listDmsHospital(query: any) {
    return request({
        url: '/evms/dms_hospital/list',
        method: 'get',
        params: query,
    });
}

// 关键字查询DMS医院列表
export async function listHospital(data: HospitalListParams) {
    return await Http.get<DmsHospitalInfo[]>('/v1/evms/getHospitalListByKeywords.jspx', data, true);
    // return request({
    //     url: '/dev-medtion/v1/evms/getHospitalListByKeywords.jspx',
    //     method: 'get',
    //     params: data,
    // });
}

// 查询DMS医院详细
export function getDmsHospital(id: number) {
    return Http.get<DmsHospitalInfo>('/evms/dms_hospital/' + id);
}

// 新增DMS医院
export async function addDmsHospital(data: DmsHospitalInfo) {
    return await Http.post<DmsHospitalInfo>('/evms/dms_hospital', data);
}

// 修改DMS医院
export async function updateDmsHospital(data: DmsHospitalInfo) {
    return await Http.put<DmsHospitalInfo>('/evms/dms_hospital', data);
}

// 删除DMS医院
export async function delDmsHospital(id: number) {
    return await Http.delete<null>('/evms/dms_hospital/' + id);
}
