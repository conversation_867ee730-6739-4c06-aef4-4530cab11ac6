import useDictStore from '@/store/modules/dict';
import { getDicts } from '@/api/system/dict/data';
import { ref, toRefs, Ref, ToRefs } from 'vue';

/**
 * 获取字典数据
 */
type Dict = Array<{
    label: string;
    value: string;
}>;
export function useDict(...args: any[]) {
    const res: Ref<Record<string, Dict>> = ref({});
    return (function(): ToRefs<Record<string, Dict>> {
        args.forEach((dictType, index) => {
            res.value[dictType] = [];
            const dicts = useDictStore().getDict(dictType);
            if (dicts) {
                res.value[dictType] = dicts;
            } else {
                getDicts(dictType).then(resp => {
                    res.value[dictType] = resp.data.map((p: any) => ({
                        label: p.label,
                        value: p.value,
                        elTagType: p.listClass
                            ? p.listClass
                            : p.value === 'T'
                                ? 'success'
                                : p.value === 'F'
                                    ? 'danger'
                                    : undefined,
                        elTagClass: p.cssClass,
                    }));
                    useDictStore().setDict(dictType, res.value[dictType]);
                });
            }
        });
        return toRefs(res.value);
    })();
}
