<script setup lang="ts">
import { getDmsCamera, delDmsCamera, addDmsCamera, updateDmsCamera } from '@/api/evms/dms_camera';

import { getTableInfo } from '@/minix/tables';
import { CameraInfo, hiddenSwitchType, HiddenTime } from '@/model/dms_device';
import { useRoute } from 'vue-router';
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { FormInstance } from 'element-plus';
import { switchType } from '@/assets/constant/switchType';
import { timestampToTime } from '../../../../utils/ruoyi';
import useUserStore from '@/store/modules/user';
import dayjs, { Dayjs } from 'dayjs';
import A from '~/scripts/a';
import type { ValidateFieldsError } from 'async-validator';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_camera_type } = proxy!.useDict('evms_dms_camera_type');
const props = defineProps(['deviceId']);
// const deviceId: number = defineProps(['deviceId']).deviceId;
const tableInfo = getTableInfo<CameraInfo>(
    '/evms/dms_camera/getPageByDeviceId',
    {
        deviceId: props.deviceId
    },
    true,
    false
);
tableInfo.load();
const ids = ref<any[]>([]);
const title = ref('');
const open = ref(false);
const form = ref<CameraInfo>({
    deviceId: props.deviceId as number,
    deviceName: '',
    name: '',
    enName: '',
    channel: '',
    control: 0,
    realTimeSwitch: 1,
    playbackSwitch: 1,
});
const resetForm = () => {
    form.value = {
        deviceId: props.deviceId,
        deviceName: '',
        name: '',
        enName: '',
        channel: '',
        control: 0,
        weight: 0,
        realTimeSwitch: 1,
        playbackSwitch: 1,
    };
};
const realValidate = (rule, value, callback) => {
    const permissions = useUserStore().permissions;
    if (!permissions.includes('evms:dms_camera:switch')) {
        callback();
    } else {
        if (form.value.realTimeSwitch && !value && form.value.type) {
            callback(new Error('请选择实时摄像头可见截止时间'));
        } else {
            callback();
        }
    }
};
const playValidate = (rule, value, callback) => {
    const permissions = useUserStore().permissions;
    if (!permissions.includes('evms:dms_camera:switch')) {
        callback();
    } else {
        if (form.value.playbackSwitch && !value && form.value.type) {
            callback(new Error('请选择实时摄像头可见截止时间'));
        } else {
            callback();
        }
    }
};
const rules = {
    name: [{ required: true, message: '摄像头名称不能为空', trigger: 'blur' }],
    channel: [{ required: true, message: '摄像头通道编号不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '摄像头类型不能为空', trigger: 'change' }],
    realTimeHiddenTime: [
        // { required: true, message: "请选择实时摄像头可见截止时间", trigger: "change" }
        { validator: realValidate, trigger: 'change' }
    ],
    playbackHiddenTime: [
        { validator: playValidate, trigger: 'change' }
        // { required: true, message: "请选择回放摄像头可见截止时间", trigger: "change" }
    ],
};

// 校验视频通道名称
const handleInput = (value: string) => {
    form.value.name = value.replace(/[|\/\\'":*?<>]/g, '');
};
/** 修改按钮操作 */
const deviceList = ref<any[]>([]);

const submitForm = () => {
    console.log(11);
    if (!DmsCameraRef.value) return;
    console.log(22);
    DmsCameraRef.value.validate((valid: boolean, fieled: ValidateFieldsError) => {
        console.log(33);
        if (valid) {
            form.value.realTimeHiddenTimestamp = new Date(
                form.value.realTimeHiddenTime || dayjs().add(12, 'hours').format('YYYY-MM-DD hh:mm:ss')
            ).getTime();
            form.value.playbackHiddenTimestamp = new Date(
                form.value.playbackHiddenTime || dayjs().add(12, 'hours').format('YYYY-MM-DD hh:mm:ss')
            ).getTime();
            if (!form.value.realTimeSwitch) {
                delete form.value.realTimeHiddenTimestamp;
            } else {
                if (dayjs(form.value.realTimeHiddenTime).isBefore(dayjs())) {
                    proxy!.$modal.msgError('请确保实时摄像头可见时间有效（需要在当前时间之后）');
                    return;
                }
            }
            if (!form.value.playbackSwitch) {
                delete form.value.playbackHiddenTimestamp;
            } else {
                if (dayjs(form.value.playbackHiddenTime).isBefore(dayjs())) {
                    proxy!.$modal.msgError('请确保回放摄像头可见时间有效（需要在当前时间之后）');
                    return;
                }
            }
            if (form.value.type) {
                delete form.value.playbackHiddenTime;
                delete form.value.realTimeHiddenTime;
                delete form.value.realTimeHiddenTimestamp;
                delete form.value.playbackHiddenTimestamp;
            }

            // 判断权限
            // const permissions = useUserStore().permissions;
            // if (!permissions.includes('evms:dms_camera:switch')) {
            //     delete form.value.realTimeHours
            //     // delete form.value.realTimeSwitch
            //     // delete form.value.playbackSwitch
            //     delete form.value.playbackHours
            // }
            if (!form.value.id) {
                // const data = {
                //     name: form.value.name,
                //     channel: form.value.channel,
                //     type: form.value.type,
                //     resource: form.value.resource,
                // }

                addDmsCamera(form.value).then(response => {
                    if (response.code === 200) {
                        open.value = false;
                        tableInfo.reset();
                        resetForm();
                        proxy!.$modal.msgSuccess('新增成功');
                    }
                });
            } else {
                updateDmsCamera(form.value).then((response: any) => {
                    if (response.code === 200) {
                        open.value = false;
                        tableInfo.reset();
                        resetForm();
                        proxy!.$modal.msgSuccess('修改成功');
                    }
                });
            }
        } else {
            console.log(fieled);
            return false;
        }
    });
};
const cancel = () => {
    resetForm();
    open.value = false;
    DmsCameraRef.value?.resetFields();
};
const DmsCameraRef = ref<FormInstance>();

// 新增
const handleAdd = () => {
    title.value = '新增视频通道';
    resetForm();
    const t = dayjs().add(12, 'hour').format('YYYY-MM-DD HH:mm:ss');
    form.value.realTimeHiddenTime = t;
    form.value.playbackHiddenTime = t;
    open.value = true;
    // tableInfo!.reset()
    // form.value.deviceId = deviceId;
    // form.value.deviceName = tableInfo.from.deviceName;
    // open.value = true;
    // title.value = "新增DMS摄像头";
};

const handleUpdate = (row: any) => {
    tableInfo!.reset();
    const _id = row.id || ids.value;
    deviceList.value = [row.deviceVo];
    getDmsCamera(_id).then((response: any) => {
        form.value = response.data;
        form.value.deviceId = response.data.deviceVo.id;
        form.value.deviceName = response.data.deviceVo.name;
        form.value.realTimeSwitch = response.data.realTimeSwitch || 0;
        form.value.playbackSwitch = response.data.playbackSwitch || 0;
        if (!form.value.realTimeSwitch) {
            form.value.realTimeHiddenTime = dayjs().add(12, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }
        if (!form.value.playbackSwitch) {
            form.value.playbackHiddenTime = dayjs().add(12, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }

        if (
            form.value.realTimeSwitch &&
            form.value.realTimeHiddenTime &&
            dayjs(form.value.realTimeHiddenTime).isBefore(dayjs())
        ) {
            form.value.realTimeHiddenTime = dayjs().add(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }
        if (
            form.value.playbackSwitch &&
            form.value.playbackHiddenTime &&
            dayjs(form.value.playbackHiddenTime).isBefore(dayjs())
        ) {
            form.value.playbackHiddenTime = dayjs().add(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }
        // 开启 + 无数据 + 非ipc类型
        if (form.value.realTimeSwitch && !form.value.realTimeHiddenTime && form.value.type) {
            form.value.realTimeHiddenTime = dayjs().add(12, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }
        // 开启 + 无数据 + 非ipc类型
        if (form.value.playbackSwitch && !form.value.playbackHiddenTime && form.value.type) {
            form.value.playbackHiddenTime = dayjs().add(12, 'hour').format('YYYY-MM-DD HH:mm:ss');
        }
        open.value = true;
        title.value = '修改视频通道';
    });
};
// 删除
const handleDelete = (row: any) => {
    const _id = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除视频通道编号为"' + _id + '"的数据项？')
        .then(function() {
            return delDmsCamera(_id);
        })
        .then(res => {
            if (res.code === 200) {
                tableInfo.reset();
                proxy!.$modal.msgSuccess('删除成功');
            } else {
                proxy!.$modal.msgError(res.msg);
            }
        })
        .catch(() => {
        });
};

const disAbleTime = (time: Date) => {
    return time.getTime() < Date.now() - 8.64e7;
};
</script>

<template>
    <div class="dms_hospital_info_video">
        <el-row :gutter="10" class="mb8" style="flex-shrink: 0">
            <el-col :span="1.5">
                <el-button
                    color="#009dff"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:dms_hospital:add']"
                >新增
                </el-button>
            </el-col>
        </el-row>
        <el-table
            row-key="id"
            v-loading="tableInfo.TableLoading.value"
            :data="tableInfo.list.value"
            style="margin-top: 20px; flex: 1; width: 100%"
        >
            <el-table-column fixed type="selection" width="55" align="center" />
            <el-table-column fixed label="ID" align="center" prop="id" />
            <el-table-column fixed label="摄像头名称" align="center" prop="name" />
            <el-table-column fixed label="摄像头英文名称" align="center" prop="enName" />
            <el-table-column label="所属设备" align="center" prop="deviceVo.name" />
            <el-table-column label="类型" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="evms_dms_camera_type" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="权重" align="center" prop="weight" />
            <el-table-column label="远程控制状态" align="center" prop="control" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="switchType" :value="scope.row.control ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="编号通道" align="center" prop="channel" />
            <el-table-column label="实时摄像头可见性" align="center" prop="realTimeSwitch">
                <template #default="scope">
                    <dict-tag :options="hiddenSwitchType" :value="scope.row.realTimeSwitch ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="实时摄像头可见截止时间" align="center" prop="realTimeHiddenTime">
                <template #default="scope">
                    <span>{{
                            scope.row.realTimeHiddenTime && scope.row.realTimeSwitch
                                ? timestampToTime(scope.row.realTimeHiddenTime)
                                : '--'
                        }}</span>
                </template>
            </el-table-column>
            <el-table-column label="回放摄像头可见性" align="center" prop="playbackSwitch">
                <template #default="scope">
                    <dict-tag :options="hiddenSwitchType" :value="scope.row.playbackSwitch ? '1' : '0'" />
                </template>
            </el-table-column>
            <el-table-column label="回放摄像头可见截止时间" align="center" prop="playbackHiddenTime">
                <template #default="scope">
                    <span>{{
                            scope.row.playbackHiddenTime && scope.row.playbackSwitch
                                ? timestampToTime(scope.row.playbackHiddenTime)
                                : '--'
                        }}</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:dms_hospital:remove']"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-row justify="space-between" align="bottom" style="flex-shrink: 0">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                :total="tableInfo.total.value"
                v-model:page="tableInfo.pageParam.pageNum"
                v-model:limit="tableInfo.pageParam.pageSize"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
    <!-- 添加或修改DMS摄像头对话框 -->
    <el-dialog :title="title" v-model="open" append-to-body draggable>
        <el-form ref="DmsCameraRef" :model="form" :rules="rules" label-width="200px">
            <!--        <el-form-item label="所属设备" prop="deviceId">-->
            <!--          <el-input v-model="form.deviceName" placeholder="请输入所属设备" />-->
            <!--        </el-form-item>-->

            <el-form-item label="视频通道名称" prop="name">
                <el-input
                    v-model="form.name"
                    @input="handleInput"
                    placeholder="请输入摄像头名称"
                    style="width: 260px"
                />
            </el-form-item>
            <el-form-item label="视频通道英文名称" prop="enName">
                <el-input v-model="form.enName" placeholder="请输入摄像头英文名称" style="width: 260px" />
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择摄像头类型" style="width: 260px">
                    <el-option
                        v-for="dict in evms_dms_camera_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="parseInt(dict.value)"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="摄像头通道编号" prop="channel">
                <el-input v-model="form.channel" placeholder="请输入摄像头通道编号" style="width: 260px" />
            </el-form-item>
            <el-form-item label="权重" prop="weight">
                <el-input v-model="form.weight" type="number" placeholder="请输入权重" style="width: 260px" />
            </el-form-item>
            <el-form-item label="远程控制" prop="control">
                <el-radio-group v-model="form.control">
                    <el-radio :label="1">开</el-radio>
                    <el-radio :label="0">关</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-hasPermi="['evms:dms_camera:switch']" label="实时视频可见性" prop="control">
                <el-radio-group v-model="form.realTimeSwitch">
                    <el-radio :label="1">可见</el-radio>
                    <el-radio :label="0">隐藏</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-show="form.realTimeSwitch"
                v-hasPermi="['evms:dms_camera:switch']"
                label="实时摄像头可见截止时间"
                prop="realTimeHiddenTime"
            >
                <el-date-picker
                    v-model="form.realTimeHiddenTime"
                    type="datetime"
                    :disabled-date="disAbleTime"
                    :disabled="!!form.type"
                    :editable="true"
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择实时摄像头可见截止时间"
                />
            </el-form-item>
            <el-form-item v-hasPermi="['evms:dms_camera:switch']" label="回放视频可见性" prop="control">
                <el-radio-group v-model="form.playbackSwitch">
                    <el-radio :label="1">可见</el-radio>
                    <el-radio :label="0">隐藏</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-show="form.playbackSwitch"
                v-hasPermi="['evms:dms_camera:switch']"
                label="回放摄像头可见截止时间"
                prop="playbackHiddenTime"
            >
                <el-date-picker
                    v-model="form.playbackHiddenTime"
                    type="datetime"
                    :editable="true"
                    :disabled="!!form.type"
                    :disabled-date="disAbleTime"
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择回放摄像头可见截止时间"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss"></style>
