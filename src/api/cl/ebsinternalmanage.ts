import request from '@/utils/request';

// 查询EBS病例内部管理表列表
export function listEbsinternalmanage(query: any) {
    return request({
        url: '/cl/ebs/internal/manage/list',
        method: 'get',
        params: query,
    });
}

// 查询EBS病例内部管理表详细
export function getEbsinternalmanage(id: any) {
    return request({
        url: '/cl/ebs/internal/manage/' + id,
        method: 'get',
    });
}

// 新增EBS病例内部管理表
export function addEbsinternalmanage(data: any) {
    return request({
        url: '/cl/ebs/internal/manage',
        method: 'post',
        data: data,
    });
}

// 修改EBS病例内部管理表
export function updateEbsinternalmanage(data: any) {
    return request({
        url: '/cl/ebs/internal/manage',
        method: 'put',
        data: data,
    });
}

// 删除EBS病例内部管理表
export function delEbsinternalmanage(id: any) {
    return request({
        url: '/cl/ebs/internal/manage/' + id,
        method: 'delete',
    });
}
