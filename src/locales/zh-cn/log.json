{"shortMessage.type": "类型", "shortMessage.type.1": "手机短信", "shortMessage.type.2": "邮件短信", "shortMessage.receiver": "接收人", "shortMessage.code": "验证码", "shortMessage.sendDate": "发送时间", "shortMessage.ip": "IP", "shortMessage.attempts": "尝试次数", "shortMessage.usage": "用途", "shortMessage.usage.0": "测试", "shortMessage.usage.1": "注册", "shortMessage.usage.2": "登录", "shortMessage.usage.3": "双因子登录", "shortMessage.usage.4": "重置密码", "shortMessage.usage.5": "修改手机号码", "shortMessage.usage.6": "修改邮箱地址", "shortMessage.usage.7": "aaa", "shortMessage.status": "状态", "shortMessage.status.0": "未使用", "shortMessage.status.1": "验证正确", "shortMessage.status.2": "验证错误", "shortMessage.status.3": "尝试次数过多", "shortMessage.status.4": "过期", "loginLog.ip": "IP地址", "loginLog.user": "用户", "loginLog.loginName": "登录名", "loginLog.created": "日期", "loginLog.type": "类型", "loginLog.type.1": "登录", "loginLog.type.2": "修改密码", "loginLog.type.9": "退出", "loginLog.status": "状态", "loginLog.status.0": "成功", "loginLog.status.1": "用户名不存在", "loginLog.status.2": "密码错误", "loginLog.status.3": "验证码错误", "loginLog.status.4": "短信错误", "loginLog.status.11": "IP超过尝试次数", "loginLog.status.12": "用户超过尝试次数", "loginLog.status.13": "密码已过期", "loginLog.status.14": "用户未激活", "loginLog.status.15": "用户已锁定", "loginLog.status.16": "用户已注销", "loginLog.status.17": "用户已禁用", "operationLog.name": "名称", "operationLog.module": "模块", "operationLog.user": "用户", "operationLog.ip": "IP地址", "operationLog.created": "日期", "operationLog.type": "类型", "operationLog.type.0": "其它", "operationLog.type.1": "新增", "operationLog.type.2": "修改", "operationLog.type.3": "删除", "operationLog.status": "状态", "operationLog.status.0": "失败", "operationLog.status.1": "成功", "operationLog.requestMethod": "请求方法", "operationLog.requestUrl": "请求URL", "operationLog.requestBody": "请求体", "operationLog.responseEntity": "响应体", "operationLog.exceptionStack": "异常堆栈", "log.article": "文章管理", "log.article.create": "新增文章", "log.article.update": "修改文章", "log.article.submit": "提交文章", "log.article.pass": "审核通过文章", "log.article.reject": "审核驳回文章", "log.article.completelyDelete": "彻底删除文章", "log.article.delete": "删除文章", "log.article.internalPush": "站内推送", "log.article.externalPush": "站群推送", "log.article.sticky": "置顶文章", "log.channel": "栏目管理", "log.channel.create": "新增栏目", "log.channel.update": "修改栏目", "log.channel.updateOrder": "修改栏目顺序", "log.channel.delete": "删除栏目", "log.blockItem": "区块项管理", "log.blockItem.create": "新增区块项", "log.blockItem.update": "修改区块项", "log.blockItem.updateOrder": "修改区块项顺序", "log.blockItem.delete": "删除区块项", "log.dict": "字典管理", "log.dict.create": "新增字典", "log.dict.update": "修改字典", "log.dict.updateOrder": "修改字典顺序", "log.dict.delete": "删除字典", "log.tag": "TAG管理", "log.tag.create": "新增TAG", "log.tag.update": "修改TAG", "log.tag.delete": "删除TAG", "log.attachment": "附件管理", "log.attachment.delete": "删除附件", "log.fulltext": "全文索引", "log.fulltext.reindexAll": "更新全部全文索引", "log.fulltext.reindexSite": "更新本站全文索引", "log.html": "静态页", "log.html.updateAll": "更新全部静态页", "log.html.updateHome": "更新首页静态页", "log.html.updateChannel": "更新栏目静态页", "log.html.updateArticle": "更新文章静态页", "log.messageBoard": "留言管理", "log.messageBoard.create": "新增留言", "log.messageBoard.update": "修改留言", "log.messageBoard.updateStatus": "修改留言状态", "log.messageBoard.delete": "删除留言", "log.webFileTemplate": "模板文件", "log.webFileTemplate.create": "新建文件", "log.webFileTemplate.update": "修改文件", "log.webFileTemplate.mkdir": "新建文件夹", "log.webFileTemplate.rename": "重命名", "log.webFileTemplate.copy": "复制文件", "log.webFileTemplate.move": "移动文件", "log.webFileTemplate.upload": "上传文件", "log.webFileTemplate.uploadZip": "上传ZIP文件", "log.webFileTemplate.delete": "删除文件", "log.webFileUpload": "上传文件", "log.webFileUpload.create": "新建文件", "log.webFileUpload.update": "修改文件", "log.webFileUpload.mkdir": "新建文件夹", "log.webFileUpload.rename": "重命名", "log.webFileUpload.copy": "复制文件", "log.webFileUpload.move": "移动文件", "log.webFileUpload.upload": "上传文件", "log.webFileUpload.uploadZip": "上传ZIP文件", "log.webFileUpload.delete": "删除文件", "log.webFileHtml": "HTML文件", "log.webFileHtml.create": "新建文件", "log.webFileHtml.update": "修改文件", "log.webFileHtml.mkdir": "新建文件夹", "log.webFileHtml.rename": "重命名", "log.webFileHtml.copy": "复制文件", "log.webFileHtml.move": "移动文件", "log.webFileHtml.upload": "上传文件", "log.webFileHtml.uploadZip": "上传ZIP文件", "log.webFileHtml.delete": "删除文件", "log.config": "全局设置", "log.config.updateBase": "修改全局基础设置", "log.config.updateUpload": "修改全局上传设置", "log.config.updateRegister": "修改全局注册设置", "log.config.updateSecurity": "修改全局安全设置", "log.config.updateSms": "修改全局短信设置", "log.config.sendSms": "全局设置发送测试短信", "log.config.updateEmail": "修改全局邮件设置", "log.config.sendEmail": "全局设置发送测试邮件", "log.config.updateUploadStorage": "修改全局附件储存点", "log.config.updateHtmlStorage": "修改全局静态页储存点", "log.config.updateTemplateStorage": "修改全局模板储存点", "log.config.updateCustoms": "修改全局自定义设置", "log.siteSettings": "站点设置", "log.siteSettings.updateBase": "修改站点基础设置", "log.siteSettings.updateWatermark": "修改站点啊水印设置", "log.siteSettings.updateMessageBoard": "修改站点留言板设置", "log.siteSettings.updateCustoms": "修改站点自定义设置", "log.siteSettings.updateHtml": "修改站点静态页设置", "log.model": "模型管理", "log.model.create": "新增模型", "log.model.update": "修改模型", "log.model.updateOrder": "修改模型顺序", "log.model.delete": "删除模型", "log.block": "区块管理", "log.block.create": "新增区块", "log.block.update": "修改区块", "log.block.updateOrder": "修改区块顺序", "log.block.enable": "启用区块", "log.block.disable": "禁用区块", "log.block.delete": "删除区块", "log.dictType": "字典类型管理", "log.dictType.create": "新增字典类型", "log.dictType.update": "修改字典类型", "log.dictType.updateOrder": "修改字典类型顺序", "log.dictType.delete": "删除字典类型", "log.user": "用户管理", "log.user.create": "新增用户", "log.user.update": "修改用户", "log.user.updatePermission": "修改用户权限", "log.user.updateStatus": "修改用户状态", "log.user.updatePassword": "修改用户密码", "log.user.delete": "删除用户", "log.role": "角色管理", "log.role.create": "新增角色", "log.role.update": "修改角色", "log.role.updateOrder": "修改角色顺序", "log.role.delete": "删除角色", "log.org": "组织管理", "log.org.create": "新增组织", "log.org.update": "修改组织", "log.org.updateOrder": "修改组织顺序", "log.org.delete": "删除组织", "log.group": "用户组管理", "log.group.create": "新增用户组", "log.group.update": "修改用户组", "log.group.updatePermission": "修改用户组权限", "log.group.updateOrder": "修改用户组顺序", "log.group.delete": "删除用户组", "log.shortMessage": "短信息管理", "log.shortMessage.delete": "删除短信息", "log.loginLog": "登录日志管理", "log.loginLog.delete": "删除登录日志", "log.operationLog": "操作日志管理", "log.operationLog.delete": "删除操作日志", "log.site": "站点管理", "log.site.create": "新增站点", "log.site.update": "修改站点", "log.site.updateOrder": "修改站点顺序", "log.site.delete": "删除站点", "log.process": "流程管理", "log.process.createProcessModel": "新增流程模型", "log.process.updateProcessModel": "修改流程定义", "log.process.updateProcessXml": "修改流程设计", "log.process.deployProcessModel": "部署流程模型", "log.process.deleteProcessModel": "删除流程设置", "log.process.deleteProcessDefinition": "删除流程定义", "log.process.deleteProcessHistory": "删除历史流程", "log.process.deleteProcessInstance": "删除流程实例", "log.task": "任务管理", "log.task.delete": "删除任务", "log.personal": "个人管理", "log.personal.updatePassword": "修改密码", "": ""}