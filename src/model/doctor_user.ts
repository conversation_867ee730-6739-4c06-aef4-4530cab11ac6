/*
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-03-17 11:39:12
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-03-17 11:39:15
 * @FilePath: /evms-web/src/model/doctor_user.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

export interface DoctorUser {
    id: string;
    username: string;
    realname: string;
    avatar: string;
    title: string;
    company: string;
    department: string;
    identity: number;
    statusForPatient: number;
    gender: string;
    role: number;
    auth: string;
    type: number;
    mobile: string;
    status: number;
    multiDepartment: string;
    userid: number;
    nickname: string;
    wxOpenId: string;
    videoExportPermission: number;
    wbUserId: string;
    rtcUserId: string;
    permission: DoctorUserPermission;
}

export interface DoctorUserPermission {
    elab: boolean;
    ebs: boolean;
    outpatient: boolean;
    videoExportPermission: boolean;
}
