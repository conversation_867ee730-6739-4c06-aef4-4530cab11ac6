import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { RefsKes } from '@/type/common';
import { EvmsConsultExpert } from '@/api/evms/expert';

export default defineComponent({
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const state = reactive({
            visible: false,
            form: {} as unknown as EvmsConsultExpert,
            rules: {
                realName: [{ required: true, message: '专家名称不能为空', trigger: 'change' }],
                createTime: [{ required: true, message: '创建时间不能为空', trigger: 'change' }] as any[]
            }
        });

        let instance: RefsKes;
        onMounted(() => {
            instance = getCurrentInstance() as RefsK<PERSON>;
        });

        function handleOk() {
            instance.refs.formRef.validate((valid: any, fields: any) => {
                if (valid) {
                    close();
                }
            });
        }

        function close() {
            state.visible = false;
        }

        function show() {
            state.visible = true;
        }

        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };

        expose({
            show
        });
        return () => (
            <ElDialog
                title="病历详情"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                <ElForm ref="formRef" model={state.form} rules={state.rules} labelWidth="80px">
                    <ElRow>
                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="医生id" prop="doctorId">
                                <ElInput v-model={state.form.doctorId} placeholder="请输入医生id" />
                            </ElFormItem>
                        </ElCol>
                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="专家名称" prop="realName">
                                <ElInput v-model={state.form.realName} placeholder="请输入专家名称" />
                            </ElFormItem>
                        </ElCol>
                    </ElRow>
                </ElForm>
            </ElDialog>
        );
    }
});
