<template>
    <router-view />
</template>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings';
import useCommonStore from '@/store/modules/common';
import { handleThemeStyle } from '@/utils/theme';
import { nextTick, onMounted } from 'vue';
import { comparsion } from '@/utils/ruoyi';
import { ls, ss } from '@/vue-ls';
import { commonLocal, commonSession, LOCAL_CONFIG, SESSION_CONFIG } from '@/store/mutation_type';

onMounted(() => {
    nextTick(() => {
        // 初始化主题样式
        handleThemeStyle(useSettingsStore().theme);
        useCommonStore().setLocalConfig(comparsion(ls.get(LOCAL_CONFIG, {}), commonLocal));
        useCommonStore().setSessionConfig(comparsion(ss.get(SESSION_CONFIG, {}), commonSession));
    });
});
</script>
