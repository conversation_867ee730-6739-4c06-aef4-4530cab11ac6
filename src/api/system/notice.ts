import request from '@/utils/request';

// 查询通知公告列表
export function listNotice(query: any) {
    return request({
        url: '/sys/notice/list',
        method: 'get',
        params: query,
    });
}

// 查询通知公告详细
export function getNotice(id: any) {
    return request({
        url: '/sys/notice/' + id,
        method: 'get',
    });
}

// 新增通知公告
export function addNotice(data: any) {
    return request({
        url: '/sys/notice',
        method: 'post',
        data: data,
    });
}

// 修改通知公告
export function updateNotice(data: any) {
    return request({
        url: '/sys/notice',
        method: 'put',
        data: data,
    });
}

// 删除通知公告
export function delNotice(id: any) {
    return request({
        url: '/sys/notice/' + id,
        method: 'delete',
    });
}
