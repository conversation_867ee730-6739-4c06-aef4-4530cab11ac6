<template>
    <el-dialog
        v-model="dialogVisible"
        append-to-body
        draggable
        modal-class="consultation_order_experts"
        width="1156px"
        @closed="handleClose"
    >
        <template #header>
            <div class="my-header">
                <div class="dialog_header">查看参会专家</div>
            </div>
        </template>
        <div>
            <el-form :inline="true" :model="props.tableInfo.from" label-width="100">
                <el-row justify="space-between">
                    <el-col :span="8">
                        <el-form-item label="ID" prop="id">
                            <el-input
                                v-model="props.tableInfo.from.no_query_userId"
                                clearable
                                placeholder="请输入ID"
                                style="width: 240px"
                                @keyup.enter="props.tableInfo.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="姓名" prop="userId">
                            <el-input
                                v-model="props.tableInfo.from.no_query_realname"
                                clearable
                                placeholder="请输入"
                                style="width: 240px"
                                @keyup.enter="props.tableInfo.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-button icon="Search" type="primary" @click="props.tableInfo.search"
                        >搜索
                        </el-button>
                        <el-button icon="Refresh" @click="props.tableInfo.reset">重置</el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-row>
                <el-col :span="24">
                    <span class="title">主题：</span>
                    <span class="container">{{ props.consultationData?.theme }}</span>
                </el-col>
                <el-col :span="24">
                    <span class="title">发起人：</span>
                    <span class="container" style="margin-right: 20px"
                    >{{ props.consultationData?.organizer?.realname }}
                    </span>
                    <span class="title">导管室：</span>
                    <span class="container">{{ props.consultationData?.device.name }}</span>
                </el-col>
            </el-row>
            <el-table :data="props.tableInfo.list.value" height="350" style="width: 100%; margin-top: 20px">
                <el-table-column label="参会人ID" prop="user.userid" width="140"></el-table-column>
                <el-table-column label="参会人姓名" prop="user.realname" width="120"></el-table-column>
                <el-table-column label="所属群组" prop="groups">
                    <template #default="scope">
                        <span>{{
                                scope.row.groups ? scope.row.groups.map((i: Group) => i.name).join('、') : ''
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="首次进入时间" prop="firstJoinTime"></el-table-column>
                <el-table-column label="最后退出时间" prop="lastLeaveTime"></el-table-column>
                <el-table-column label="参会时长" prop="totalDuration">
                    <template #default="scope">
                        <span>{{
                                scope.row.totalDuration ? formatDuration(scope.row.totalDuration) : '00:00'
                            }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <el-row align="bottom" justify="space-between">
                <div>共 {{ props.tableInfo.total }} 项数据</div>
                <pagination
                    v-show="props.tableInfo.total.value > 0"
                    v-model:limit="props.tableInfo.pageParam.pageSize"
                    v-model:page="props.tableInfo.pageParam.pageNum"
                    :total="props.tableInfo.total.value"
                    @pagination="props.tableInfo.pagination"
                />
            </el-row>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import type { ExtendedConsultationOrder, Group } from '@/types/consultation';

const props = defineProps<{
    modelValue: boolean;
    consultationData?: ExtendedConsultationOrder;
    tableInfo: any;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void;
    (e: 'closed'): void;
}>();

const dialogVisible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
});

const handleClose = () => {
    emit('closed');
};
</script>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'ExpertListDialog'
});
const formatDuration = (totalDuration: number): string => {
    const hours = Math.floor(totalDuration / 3600);
    const minutes = Math.floor((totalDuration % 3600) / 60);
    const seconds = totalDuration % 60;

    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    if (hours > 0) {
        const hoursStr = hours.toString().padStart(2, '0');
        return `${hoursStr}:${minutesStr}:${secondsStr}`;
    } else {
        return `${minutesStr}:${secondsStr}`;
    }
};
</script>

<style lang="scss">
.consultation_order_experts {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%;
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }

    .title {
        color: #666;
    }
}
</style>
