<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2025-03-06 16:59:44
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2025-03-06 17:43:41
-->
<template>
    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button
                v-hasPermi="['cns:cns_consultation:add']"
                color="#009dff"
                icon="Plus"
                plain
                @click="emit('add')"
            >新增
            </el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button
                v-hasPermi="['cns:cns_consultation:edit']"
                :disabled="single"
                color="#01c064"
                icon="Edit"
                plain
                @click="emit('update')"
            >修改
            </el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button
                v-hasPermi="['cns:cns_consultation:remove']"
                :disabled="multiple"
                color="#ff5c00"
                icon="Delete"
                plain
                @click="emit('delete')"
            >删除
            </el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button v-hasPermi="['cns:cns_consultation:cancel']" :disabled="multiple" color="#ff9900" icon="Close"
                       plain @click="emit('cancel')"
            >取消会诊
            </el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button v-hasPermi="['cns:cns_consultation:close']" :disabled="multiple" color="#409eff" icon="Check"
                       plain @click="emit('close')"
            >结束会诊
            </el-button>
        </el-col>
    </el-row>
</template>

<script lang="ts" setup>
const props = defineProps<{
    single: boolean;
    multiple: boolean;
}>();

const emit = defineEmits<{
    (e: 'add'): void;
    (e: 'update'): void;
    (e: 'delete'): void;
    (e: 'cancel'): void;
    (e: 'close'): void;
}>();
</script>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'ConsultationActions'
});
</script>
