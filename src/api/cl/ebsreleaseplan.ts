import request from '@/utils/request';

// 查询EBS公众号发布计划列表
export function listEbsreleaseplan(query: any) {
    return request({
        url: '/cl/ebs/release/plan/list',
        method: 'get',
        params: query,
    });
}

// 查询EBS公众号发布计划列表(不分页)
export function allEbsreleaseplan(query: any) {
    return request({
        url: '/cl/ebs/release/plan/all',
        method: 'get',
        params: query,
    });
}

// 查询EBS公众号发布计划详细
export function getEbsreleaseplan(id: any) {
    return request({
        url: '/cl/ebs/release/plan/' + id,
        method: 'get',
    });
}

// 新增EBS公众号发布计划
export function addEbsreleaseplan(data: any) {
    return request({
        url: '/cl/ebs/release/plan',
        method: 'post',
        data: data,
    });
}

// 修改EBS公众号发布计划
export function updateEbsreleaseplan(data: any) {
    return request({
        url: '/cl/ebs/release/plan',
        method: 'put',
        data: data,
    });
}

// 删除EBS公众号发布计划
export function delEbsreleaseplan(id: any) {
    return request({
        url: '/cl/ebs/release/plan/' + id,
        method: 'delete',
    });
}
