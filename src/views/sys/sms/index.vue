<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
            <el-form-item label="厂商" prop="vendor">
                <el-input
                    v-model="queryParams.vendor"
                    clearable
                    placeholder="请输入厂商"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="模板" prop="template">
                <el-input
                    v-model="queryParams.template"
                    clearable
                    placeholder="请输入模板"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" clearable placeholder="请选择状态">
                    <el-option
                        v-for="dict in sys_common_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:sms:add']" icon="Plus" plain type="primary" @click="handleAdd"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:sms:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:sms:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:sms:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="SmsList" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column align="center" label="厂商" prop="vendor" />
            <el-table-column align="center" label="手机号" prop="phones" />
            <el-table-column align="center" label="模板" prop="template" />
            <el-table-column align="center" label="参数" prop="param" />
            <el-table-column align="center" label="返回" prop="result" />
            <el-table-column align="center" label="状态" prop="status">
                <template #default="scope">
                    <dict-tag :options="sys_common_status" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['sys:sms:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button
                    >
                    <el-button
                        v-hasPermi="['sys:sms:remove']"
                        icon="Delete"
                        link
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改短信记录对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="SmsRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="厂商" prop="vendor">
                    <el-input v-model="form.vendor" placeholder="请输入厂商" />
                </el-form-item>
                <el-form-item label="手机号" prop="phones">
                    <el-input v-model="form.phones" placeholder="请输入内容" type="textarea" />
                </el-form-item>
                <el-form-item label="模板" prop="template">
                    <el-input v-model="form.template" placeholder="请输入模板" />
                </el-form-item>
                <el-form-item label="参数" prop="param">
                    <el-input v-model="form.param" placeholder="请输入内容" type="textarea" />
                </el-form-item>
                <el-form-item label="返回" prop="result">
                    <el-input v-model="form.result" placeholder="请输入内容" type="textarea" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in sys_common_status" :key="dict.value" :label="dict.value">{{
                            dict.label
                            }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="Sms" setup>
import { listSms, getSms, delSms, addSms, updateSms } from '@/api/sys/sms';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_common_status } = proxy!.useDict('sys_common_status');

const SmsList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        vendor: undefined,
        phones: undefined,
        template: undefined,
        param: undefined,
        result: undefined,
        status: undefined
    },
    rules: {
        vendor: [{ required: true, message: '厂商不能为空', trigger: 'blur' }],
        template: [{ required: true, message: '模板不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询短信记录列表 */
function getList() {
    loading.value = true;

    listSms(queryParams.value).then((response: any) => {
        SmsList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        vendor: undefined,
        phones: undefined,
        template: undefined,
        param: undefined,
        result: undefined,
        status: undefined,
        createBy: undefined,
        createTime: undefined
    };
    proxy!.resetForm('SmsRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加短信记录';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getSms(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改短信记录';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['SmsRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateSms(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addSms(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除短信记录编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delSms(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'sys/Sms/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `Sms_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
