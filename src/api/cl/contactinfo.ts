import request from '@/utils/request';

// 查询导管室联系信息列表
export function listContactinfo(query: any) {
    return request({
        url: '/cl/contact/info/list',
        method: 'get',
        params: query,
    });
}

// 查询导管室联系信息详细
export function getContactinfo(id: any) {
    return request({
        url: '/cl/contact/info/' + id,
        method: 'get',
    });
}

// 新增导管室联系信息
export function addContactinfo(data: any) {
    return request({
        url: '/cl/contact/info',
        method: 'post',
        data: data,
    });
}

// 修改导管室联系信息
export function updateContactinfo(data: any) {
    return request({
        url: '/cl/contact/info',
        method: 'put',
        data: data,
    });
}

// 删除导管室联系信息
export function delContactinfo(id: any) {
    return request({
        url: '/cl/contact/info/' + id,
        method: 'delete',
    });
}

export function getByDeviceId(deviceId: number) {
    const data = {
        deviceId: deviceId
    };
    return request({
        url: '/cl/contact/info/getCooperateByDeviceId',
        method: 'get',
        params: data,
    });
}
