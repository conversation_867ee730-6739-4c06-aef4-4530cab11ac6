<template>
    <div class="app-container">
        <el-row :gutter="20">
            <!--部门数据-->
            <el-col :span="4" :xs="24">
                <div class="head-container">
                    <el-input
                        v-model="deptName"
                        clearable
                        placeholder="请输入部门名称"
                        prefix-icon="Search"
                        style="margin-bottom: 20px"
                    />
                </div>
                <div class="head-container">
                    <el-tree
                        ref="deptTreeRef"
                        :data="deptOptions"
                        :expand-on-click-node="false"
                        :filter-node-method="filterNode"
                        :props="{ label: 'label', children: 'children' }"
                        default-expand-all
                        highlight-current
                        node-key="id"
                        @nodeClick="handleNodeClick"
                    />
                </div>
            </el-col>
            <!--用户数据-->
            <el-col :span="20" :xs="24">
                <el-form
                    v-show="showSearch"
                    ref="queryRef"
                    :inline="true"
                    :model="queryParams"
                    label-width="68px"
                >
                    <el-form-item label="用户名称" prop="username">
                        <el-input
                            v-model="queryParams.username"
                            clearable
                            placeholder="请输入用户名称"
                            style="width: 240px"
                            @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="手机号码" prop="phone">
                        <el-input
                            v-model="queryParams.like_phone"
                            clearable
                            placeholder="请输入手机号码"
                            style="width: 240px"
                            @keyup.enter="handleQuery"
                        />
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select
                            v-model="queryParams.status"
                            clearable
                            placeholder="用户状态"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in sys_normal_disable"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建时间" style="width: 308px">
                        <el-date-picker
                            v-model="dateRange"
                            end-placeholder="结束日期"
                            range-separator="-"
                            start-placeholder="开始日期"
                            type="daterange"
                            value-format="YYYY-MM-DD"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>

                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['sys:user:add']"
                            icon="Plus"
                            plain
                            type="primary"
                            @click="handleAdd"
                        >新增
                        </el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['sys:user:edit']"
                            :disabled="single"
                            icon="Edit"
                            plain
                            type="success"
                            @click="handleUpdate"
                        >修改
                        </el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['sys:user:remove']"
                            :disabled="multiple"
                            icon="Delete"
                            plain
                            type="danger"
                            @click="handleDelete"
                        >删除
                        </el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['sys:user:import']"
                            icon="Upload"
                            plain
                            type="info"
                            @click="handleImport"
                        >导入
                        </el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['sys:user:export']"
                            icon="Download"
                            plain
                            type="warning"
                            @click="handleExport"
                        >导出
                        </el-button
                        >
                    </el-col>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        :columns="columns"
                        @queryTable="getList"
                    ></right-toolbar>
                </el-row>

                <el-table v-loading="loading" :data="userList" @selectionChange="handleSelectionChange">
                    <el-table-column align="center" type="selection" width="50" />
                    <el-table-column
                        v-if="columns[0].visible"
                        key="id"
                        align="center"
                        label="用户编号"
                        prop="id"
                    />
                    <el-table-column
                        v-if="columns[1].visible"
                        key="username"
                        :show-overflow-tooltip="true"
                        align="center"
                        label="用户名称"
                        prop="username"
                    />
                    <el-table-column
                        v-if="columns[2].visible"
                        key="nickname"
                        :show-overflow-tooltip="true"
                        align="center"
                        label="用户昵称"
                        prop="nickname"
                    />
                    <el-table-column
                        v-if="columns[3].visible"
                        key="deptName"
                        :show-overflow-tooltip="true"
                        align="center"
                        label="部门"
                        prop="deptVo.name"
                    />
                    <el-table-column
                        v-if="columns[4].visible"
                        key="phone"
                        align="center"
                        label="手机号码"
                        prop="phone"
                        width="120"
                    />
                    <el-table-column v-if="columns[5].visible" key="status" align="center" label="状态">
                        <template #default="scope">
                            <el-switch
                                v-model="scope.row.status"
                                active-value="0"
                                inactive-value="1"
                                @change="handleStatusChange(scope.row)"
                            ></el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-if="columns[6].visible"
                        align="center"
                        label="创建时间"
                        prop="createTime"
                        width="160"
                    >
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.createTime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        class-name="small-padding fixed-width"
                        label="操作"
                        width="150"
                    >
                        <template #default="scope">
                            <el-tooltip v-if="scope.row.id !== '1'" content="修改" placement="top">
                                <el-button
                                    v-hasPermi="['sys:user:edit']"
                                    icon="Edit"
                                    link
                                    type="primary"
                                    @click="handleUpdate(scope.row)"
                                ></el-button>
                            </el-tooltip>
                            <el-tooltip v-if="scope.row.id !== '1'" content="删除" placement="top">
                                <el-button
                                    v-hasPermi="['sys:user:remove']"
                                    icon="Delete"
                                    link
                                    type="primary"
                                    @click="handleDelete(scope.row)"
                                ></el-button>
                            </el-tooltip>
                            <el-tooltip v-if="scope.row.id !== '1'" content="重置密码" placement="top">
                                <el-button
                                    v-hasPermi="['sys:user:resetPwd']"
                                    icon="Key"
                                    link
                                    type="primary"
                                    @click="handleResetPwd(scope.row)"
                                ></el-button>
                            </el-tooltip>
                            <el-tooltip v-if="scope.row.id !== '1'" content="分配角色" placement="top">
                                <el-button
                                    v-hasPermi="['sys:user:edit']"
                                    icon="CircleCheck"
                                    link
                                    type="primary"
                                    @click="handleAuthRole(scope.row)"
                                ></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    v-show="total > 0"
                    v-model:limit="queryParams.pageSize"
                    v-model:page="queryParams.pageNum"
                    :total="total"
                    @pagination="getList"
                />
            </el-col>
        </el-row>

        <!-- 添加或修改用户配置对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="600px">
            <el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="用户昵称" prop="nickname">
                            <el-input v-model="form.nickname" maxlength="30" placeholder="请输入用户昵称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="归属部门" prop="deptId">
                            <el-tree-select
                                v-model="form.deptId"
                                :data="deptOptions"
                                :props="{ value: 'id', label: 'label', children: 'children' }"
                                check-strictly
                                placeholder="请选择归属部门"
                                value-key="id"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="手机号码" prop="phone">
                            <el-input v-model="form.phone" maxlength="11" placeholder="请输入手机号码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="邮箱" prop="email">
                            <el-input v-model="form.email" maxlength="50" placeholder="请输入邮箱" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item v-if="form.id == undefined" label="用户名称" prop="username">
                            <el-input v-model="form.username" maxlength="30" placeholder="请输入用户名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item v-if="form.id == undefined" label="用户密码" prop="password">
                            <el-input
                                v-model="form.password"
                                maxlength="20"
                                placeholder="请输入用户密码"
                                show-password
                                type="password"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="用户性别">
                            <el-select v-model="form.gender" placeholder="请选择">
                                <el-option
                                    v-for="dict in sys_user_sex"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态">
                            <el-radio-group v-model="form.status">
                                <el-radio
                                    v-for="dict in sys_normal_disable"
                                    :key="dict.value"
                                    :label="dict.value"
                                >{{ dict.label }}
                                </el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="岗位">
                            <el-select v-model="form.postId" placeholder="请选择">
                                <el-option
                                    v-for="item in postOptions"
                                    :key="item.id"
                                    :disabled="item.status == 1"
                                    :label="item.name"
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="角色" prop="roleIds">
                            <el-select v-model="form.roleIds" multiple placeholder="请选择">
                                <el-option
                                    v-for="item in roleOptions"
                                    :key="item.id"
                                    :disabled="item.status == 1"
                                    :label="item.name"
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input
                                v-model="form.remark"
                                placeholder="请输入内容"
                                type="textarea"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 用户导入对话框 -->
        <el-dialog v-model="upload.open" :title="upload.title" append-to-body width="400px">
            <el-upload
                ref="uploadRef"
                :action="upload.url + '?updateSupport=' + upload.updateSupport"
                :auto-upload="false"
                :disabled="upload.isUploading"
                :headers="upload.headers"
                :limit="1"
                :on-progress="handleFileUploadProgress"
                :on-success="handleFileSuccess"
                accept=".xlsx, .xls"
                drag
            >
                <el-icon class="el-icon--upload">
                    <upload-filled />
                </el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <template #tip>
                    <div class="el-upload__tip text-center">
                        <div class="el-upload__tip">
                            <el-checkbox v-model="upload.updateSupport" />
                            是否更新已经存在的用户数据
                        </div>
                        <span>仅允许导入xls、xlsx格式文件。</span>
                        <el-link
                            :underline="false"
                            style="font-size: 12px; vertical-align: baseline"
                            type="primary"
                            @click="importTemplate"
                        >下载模板
                        </el-link
                        >
                    </div>
                </template>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="User" setup>
/* eslint-disable camelcase */
import { getToken } from '@/utils/auth';
import {
    changeUserStatus,
    listUser,
    resetUserPwd,
    delUser,
    getUser,
    updateUser,
    addUser,
    deptTreeSelect,
    roleList,
    postList
} from '@/api/system/user';
import { parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElTree } from 'element-plus';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
console.log(getCurrentInstance());
const { sys_normal_disable, sys_user_sex } = proxy!.useDict('sys_normal_disable', 'sys_user_sex');
const deptTreeRef = ref<InstanceType<typeof ElTree>>();
const userList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const dateRange = ref<any[]>([]);
const deptName = ref('');
const deptOptions = ref(undefined);
const initPassword = ref(undefined);
const postOptions = ref<any[]>([]);
const roleOptions = ref<any[]>([]);
/*** 用户导入参数 */
const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/sys/user/importExcel'
});
// 列显隐信息
const columns = ref([
    { key: 0, label: `用户编号`, visible: true },
    { key: 1, label: `用户名称`, visible: true },
    { key: 2, label: `用户昵称`, visible: true },
    { key: 3, label: `部门`, visible: true },
    { key: 4, label: `手机号码`, visible: true },
    { key: 5, label: `状态`, visible: true },
    { key: 6, label: `创建时间`, visible: true }
]);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: undefined,
        like_phone: undefined,
        status: undefined,
        dept_id: undefined
    },
    rules: {
        username: [
            { required: true, message: '用户名称不能为空', trigger: 'blur' },
            { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        nickname: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
        password: [
            { required: true, message: '用户密码不能为空', trigger: 'blur' },
            { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
        ],
        email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
        phone: [
            { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 通过条件过滤节点  */
const filterNode = (value: any, data: any) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};
/** 根据名称筛选部门树 */
watch(deptName, val => {
    (proxy?.$refs['deptTreeRef'] as any).filter(val);
});

/** 查询部门下拉树结构 */
function getDeptTree() {
    deptTreeSelect().then(response => {
        deptOptions.value = response.data;
    });
}

/** 查询用户列表 */
function getList() {
    loading.value = true;
    listUser(proxy!.addDateRange(queryParams.value, dateRange.value)).then((res: any) => {
        loading.value = false;
        userList.value = res.data.rows;
        total.value = res.data.total;
    });
}

function getRoleList() {
    roleList().then(response => {
        roleOptions.value = response.data;
    });
}

function getPostList() {
    postList().then(response => {
        postOptions.value = response.data;
    });
}

/** 节点单击事件 */
function handleNodeClick(data: any) {
    queryParams.value.dept_id = data.id;
    handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = [];
    proxy!.resetForm('queryRef');
    queryParams.value.dept_id = undefined;
    deptTreeRef.value?.setCurrentKey(null as any);
    handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const userIds = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除用户编号为"' + userIds + '"的数据项？')
        .then(function() {
            return delUser(userIds);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch((e: any) => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'sys/user/exportExcel',
        {
            ...queryParams.value
        },
        `user_${new Date().getTime()}.xlsx`
    );
}

/** 用户状态修改  */
function handleStatusChange(row: any) {
    let text = row.status === '0' ? '启用' : '停用';
    proxy!.$modal
        .confirm('确认要"' + text + '""' + row.username + '"用户吗?')
        .then(function() {
            return changeUserStatus(row.id, row.status);
        })
        .then(() => {
            proxy!.$modal.msgSuccess(text + '成功');
        })
        .catch(function() {
            row.status = row.status === '0' ? '1' : '0';
        });
}

/** 更多操作 */
function handleCommand(command: any, row: any) {
    switch (command) {
        case 'handleResetPwd':
            handleResetPwd(row);
            break;
        case 'handleAuthRole':
            handleAuthRole(row);
            break;
        default:
            break;
    }
}

/** 跳转角色分配 */
function handleAuthRole(row: any) {
    const id = row.id;
    router.push('/system/user-auth/role/' + id);
}

/** 重置密码按钮操作 */
function handleResetPwd(row: any) {
    (proxy as any)
        .$prompt('请输入"' + row.username + '"的新密码', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            inputPattern: /^.{5,20}$/,
            inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
        })
        .then(({ value }: any) => {
            resetUserPwd(row.id, value).then(response => {
                proxy!.$modal.msgSuccess('修改成功，新密码是：' + value);
            });
        })
        .catch((e: any) => {
            console.log(e);
        });
}

/** 选择条数  */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 导入按钮操作 */
function handleImport() {
    upload.title = '用户导入';
    upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
    proxy!.download('sys/user/exportExcelTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event?: any, file?: any, fileList?: any) => {
    upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: any, fileList: any) => {
    upload.open = false;
    upload.isUploading = false;
    (proxy?.$refs['uploadRef'] as any).handleRemove(file);
    (proxy as any).$alert(
        '<div style=\'overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;\'>' +
        response.msg +
        '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
    );
    getList();
};

/** 提交上传文件 */
function submitFileForm() {
    (proxy?.$refs['uploadRef'] as any).submit();
}

/** 重置操作表单 */
function reset() {
    form.value = {
        id: undefined,
        deptId: undefined,
        username: undefined,
        nickname: undefined,
        password: undefined,
        phone: undefined,
        email: undefined,
        gender: undefined,
        status: '0',
        remark: undefined,
        postId: undefined,
        roleIds: []
    };
    proxy!.resetForm('userRef');
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加用户';
    form.value.password = initPassword.value;
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const id = row.id || ids.value;
    getUser(id).then((response: any) => {
        let user = response.data;
        let roles: any[] = user.roles;
        form.value = user;
        form.value.deptId = user.deptVo ? user.deptVo.id : null;
        form.value.postId = user.postVo ? user.postVo.id : null;
        form.value.roleIds = roles ? roles.map(item => item.id) : [];
        open.value = true;
        title.value = '修改用户';
        form.value.password = '';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['userRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id) {
                updateUser(form.value).then(response => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addUser(form.value).then(response => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

getDeptTree();
getList();
getRoleList();
getPostList();
</script>
