import request from '@/utils/request';

// 查询版本升级列表
export function listClientVersion(query: any) {
    return request({
        url: '/ota/client_version/list',
        method: 'get',
        params: query,
    });
}

// 查询版本升级详细
export function getClientVersion(id: any) {
    return request({
        url: '/ota/client_version/' + id,
        method: 'get',
    });
}

// 新增版本升级
export function addClientVersion(data: any) {
    return request({
        url: '/ota/client_version',
        method: 'post',
        data: data,
    });
}

// 修改版本升级
export function updateClientVersion(data: any) {
    return request({
        url: '/ota/client_version',
        method: 'put',
        data: data,
    });
}

// 删除版本升级
export function delClientVersion(id: any) {
    return request({
        url: '/ota/client_version/' + id,
        method: 'delete',
    });
}

export function getVersionCodePlaceHolder(params: { platform: string; applicationProgram: string }) {
    return request({
        url: '/ota/client_version/getLastVersion',
        params
    });
}
