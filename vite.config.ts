import { defineConfig, loadEnv } from 'vite';
import createVitePlugins from './vite/plugins';
import path from 'path';

export default defineConfig(({ mode, command }) => {
    const env = loadEnv(mode, process.cwd());
    const { VITE_APP_ENV } = env;
    return {
        plugins: createVitePlugins(env, command === 'build'),
        // 部署生产环境和开发环境下的URL。
        // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
        // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
        base: VITE_APP_ENV === 'production' ? '/' : '/',
        server: {
            host: true,
            open: false,
            proxy: {
                '^/dev-medtion': {
                    target: 'https://dev.medtion.com', //后端本地调试
                    changeOrigin: true,
                    rewrite: p => p.replace(/^\/dev-medtion/, '')
                },
                // https://cn.vitejs.dev/config/#server-proxy
                '^/dev-api': {
                    target: 'https://evmsdevadmin.brainmed.com', //后端本地调试
                    // target: 'http://127.0.0.1:18000', //后端本地调试
                    //  target: 'http://************:8080',
                    changeOrigin: true,
                    rewrite: p => {
                        return p.replace(/^\/dev-api/, '/prod-api');
                        // return  p.replace(/^\/dev-api/, '/prod-api')
                    }
                },
                '^/prod-api': {
                    target: 'https://evmsadmin.brainmed.com', //后端本地调试
                    //  target: 'http://************:8080',
                    changeOrigin: true,
                    rewrite: p => {
                        console.log(p);
                        return p.replace(/^\/dev-api/, '/prod-api');
                    }
                },
                '/v3': {
                    //target: 'http://localhost:18088',
                    target: 'http://************:18080',
                    changeOrigin: true
                    // rewrite: p => p.replace(/^\/dev-api/, ''),
                }
            }
        },
        resolve: {
            alias: {
                // 设置路径
                '~': path.resolve(__dirname, './'),
                // 设置别名
                '@': path.resolve(__dirname, './src'),
                // 使用i18n
                'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
            },
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
        },

        build: {
            sourcemap: false,
            rollupOptions: {
                output: {
                    manualChunks(id) {
                        if (id.includes('element-plus/theme')) {
                            return 'ele';
                        }
                    }
                }
            }
        }
    };
});
