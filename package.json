{"name": "ruo-yi-vue3-ts", "private": true, "version": "3.8.5", "type": "module", "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build:test": "vite build --mode test", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "upload:dev": "node build.js", "upload:prod": "node build.js prod"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vueup/vue-quill": "1.0.0", "@vueuse/core": "9.5.0", "@zeronejs/utils": "^1.4.0", "ali-oss": "^6.23.0", "axios": "0.27.2", "body-parser": "^1.20.2", "dayjs": "^1.11.11", "echarts": "5.4.0", "element-plus": "2.2.27", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "lodash": "^4.17.21", "moment": "^2.30.1", "nprogress": "0.2.0", "pinia": "2.0.22", "prettier": "^3.3.2", "qs": "^6.11.2", "tinymce": "5.9.2", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-i18n": "9.2.2", "vue-router": "4.1.4", "vuedraggable": "4.1.0", "xgplayer": "^3.0.18"}, "devDependencies": {"@types/file-saver": "^2.0.5", "@types/js-cookie": "^3.0.2", "@types/node": "^18.7.15", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.12", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "@vitejs/plugin-vue": "3.1.0", "archiver": "^6.0.1", "autoprefixer": "^10.4.8", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^9.3.0", "postcss": "^8.4.16", "sass": "1.56.1", "ssh2": "1.12.0", "tailwindcss": "^3.1.8", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.0", "typescript": "^4.8.2", "unplugin-auto-import": "0.11.4", "unplugin-vue-components": "^0.22.4", "vite": "3.2.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}