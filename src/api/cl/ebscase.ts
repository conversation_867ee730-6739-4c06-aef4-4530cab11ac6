import request from '@/utils/request';

// 查询EBS病例列表
export function listEbscase(query: any) {
    return request({
        url: '/cl/ebs/case/list',
        method: 'get',
        params: query,
    });
}

// 查询EBS病例详细
export function getEbscase(id: any) {
    return request({
        url: '/cl/ebs/case/' + id,
        method: 'get',
    });
}

// 新增EBS病例
export function addEbscase(data: any) {
    return request({
        url: '/cl/ebs/case',
        method: 'post',
        data: data,
    });
}

// 修改EBS病例
export function updateEbscase(data: any) {
    return request({
        url: '/cl/ebs/case',
        method: 'put',
        data: data,
    });
}

// 删除EBS病例
export function delEbscase(id: any) {
    return request({
        url: '/cl/ebs/case/' + id,
        method: 'delete',
    });
}

export function getElabCaseByName(name: any) {
    const data = {
        name: name
    };
    return request({
        url: '/cl/ebs/case/getElabCaseByName',
        method: 'get',
        params: data,
    });
}

/*
 根据Elab手术病例ID查询Elab手术信息
*/
export function getElabCaseById(id: any) {
    const data = {
        id: id
    };
    return request({
        url: '/cl/ebs/case/getElabCaseById',
        method: 'get',
        params: data,
    });
}

/*
 根据术者ID查询术者信息
*/
export function getAuthorById(id: any) {
    const data = {
        id: id
    };
    return request({
        url: '/cl/ebs/case/getAuthorById',
        method: 'get',
        params: data,
    });
}

/*
 根据术者姓名查询术者列表
*/
export function getAuthorListByName(name: any) {
    const data = {
        name: name
    };
    return request({
        url: '/cl/ebs/case/getAuthorListByName',
        method: 'get',
        params: data,
    });
}
