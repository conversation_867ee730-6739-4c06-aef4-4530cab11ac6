<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="标题" prop="title">
                <el-input
                    v-model="queryParams.title"
                    placeholder="请输入标题"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item label="术者" prop="speaker_name">
                <el-input
                    v-model="queryParams.speaker_name"
                    placeholder="请输入术者"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <!--            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                    <el-option
                        v-for="dict in "
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>-->

            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsreleaseplan:add']"
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsreleaseplan:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsreleaseplan:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <!--            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsreleaseplan:export']"
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                >导出
                </el-button>
            </el-col>-->

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="ebsreleaseplanList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" prop="id" />

            <el-table-column label="公众号发布日期" align="center" prop="publishDate" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.publishDate, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="App上架日期" align="center" prop="onlineDate" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.onlineDate, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="标题" align="center" prop="title" />

            <el-table-column label="术者" align="center" prop="authorName" />

            <el-table-column label="单位" align="center" prop="authorCompany" />

            <el-table-column label="状态" align="center" prop="status" />

            <el-table-column label="微信观看量" align="center" prop="views" />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:ebsreleaseplan:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:ebsreleaseplan:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改EBS公众号发布计划对话框 -->
        <el-dialog v-model="open" :title="title" width="800px" append-to-body>
            <el-form ref="ebsreleaseplanRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="标题" prop="title">
                            <el-input v-model="form.title" placeholder="请输入标题" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="E-lab手术" prop="elabId">
                            <el-select
                                style="width: 100%"
                                v-model="form.elabId"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入手术名称"
                                remote-show-suffix
                                :remote-method="searchElabList"
                                :loading="load"
                            >
                                <el-option
                                    v-for="item in elabCaseList"
                                    :key="item.id"
                                    :label="item.caseName"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="发布日期" prop="publishDate">
                            <el-date-picker
                                clearable
                                v-model="form.publishDate"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择公众号发布日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="术者" prop="authorId">
                            <el-select
                                style="width: 100%"
                                v-model="form.authorId"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入术者姓名"
                                remote-show-suffix
                                @change="selectChange"
                                :remote-method="searchAuthorList"
                                :loading="load"
                            >
                                <el-option
                                    v-for="item in authorList"
                                    :key="item.id"
                                    :label="item.authorName"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="App上架日期" prop="onlineDate">
                            <el-date-picker
                                clearable
                                v-model="form.onlineDate"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择App上架日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="单位" prop="authorCompany">
                            <el-input v-model="form.authorCompany" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="病例来源" prop="resource">
                            <el-input v-model="form.resource" placeholder="请输入病例来源" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="职称" prop="authorTitle">
                            <el-select v-model="form.authorTitle" placeholder=" " multiple disabled>
                                <el-option
                                    v-for="dict in cl_author_title_type"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="平台文章地址" prop="url">
                            <el-input v-model="form.url" placeholder="请输入平台文章地址" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="商务" prop="business">
                            <el-input v-model="form.business" placeholder="请输入商务" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="厂家冠名" prop="naming">
                            <el-input v-model="form.naming" placeholder="请输入厂家冠名" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="状态" prop="status">
                            <el-input v-model="form.status" placeholder="请输入状态" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="微信观看量" prop="views">
                            <el-input-number v-model="form.views" :min="0" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Ebsreleaseplan" lang="ts">
import {
    listEbsreleaseplan,
    getEbsreleaseplan,
    delEbsreleaseplan,
    addEbsreleaseplan,
    updateEbsreleaseplan
} from '@/api/cl/ebsreleaseplan';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { getAuthorById, getAuthorListByName, getElabCaseById, getElabCaseByName } from '@/api/cl/ebscase';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cl_author_title_type } = proxy!.useDict('cl_author_title_type');

const ebsreleaseplanList = ref<any[]>([]);
const authorList = ref<any[]>([]);
const elabCaseList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const load = ref(true);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        publishDate: undefined,
        onlineDate: undefined,
        title: undefined,
        speaker_name: undefined,
        resource: undefined,
        url: undefined,
        naming: undefined,
        business: undefined,
        status: undefined,
        views: undefined,
        sort: 'views desc'
    },
    rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询EBS公众号发布计划列表 */
function getList() {
    loading.value = true;
    listEbsreleaseplan(queryFormat(queryParams.value)).then((response: any) => {
        ebsreleaseplanList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/**
 * 获取术者列表
 */
function searchAuthorList(name?: String) {
    load.value = true;
    authorList.value = [];
    getAuthorListByName(name).then(response => {
        authorList.value = response.data;
    });
    load.value = false;
}

/**
 * 获取E-lab手术列表
 */
function searchElabList(name?: String) {
    load.value = true;
    elabCaseList.value = [];
    getElabCaseByName(name).then(response => {
        elabCaseList.value = response.data;
    });
    load.value = false;
}

function selectChange(value: any) {
    getAuthorById(value).then(response => {
        if (response.data[0].title) {
            form.value.authorTitle = response.data[0].title.split(',');
        } else {
            form.value.authorTitle = [];
        }
        if (response.data[0].company) {
            form.value.authorCompany = response.data[0].company;
        } else {
            form.value.authorCompany = '';
        }
    });
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        elabId: undefined,
        publishDate: undefined,
        onlineDate: undefined,
        title: undefined,
        authorId: undefined,
        authorName: undefined,
        authorCompany: undefined,
        authorTitle: [],
        resource: undefined,
        url: undefined,
        naming: undefined,
        business: undefined,
        status: undefined,
        views: 0,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('ebsreleaseplanRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加EBS公众号发布计划';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getEbsreleaseplan(_id).then((response: any) => {
        form.value = response.data;
        if (form.value.authorId) {
            authorList.value = [];
            getAuthorById(form.value.authorId).then(response => {
                authorList.value = response.data;
                if (response.data[0].title) {
                    form.value.authorTitle = response.data[0].title.split(',');
                }
                if (response.data[0].company) {
                    form.value.authorCompany = response.data[0].company;
                }
            });
        }
        //E-lab 手术信息
        if (form.value.elabId) {
            elabCaseList.value = [];
            getElabCaseById(form.value.elabId).then(response => {
                elabCaseList.value = response.data;
            });
        }
        load.value = false;
        open.value = true;
        title.value = '修改EBS公众号发布计划';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['ebsreleaseplanRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateEbsreleaseplan(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addEbsreleaseplan(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除EBS公众号发布计划编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delEbsreleaseplan(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy?.download(
        'cl/ebsreleaseplan/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `ebsreleaseplan_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
