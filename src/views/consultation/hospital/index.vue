<script lang="ts">
export default {
    name: 'ConsultationHospital'
};
</script>
<script lang="ts" setup>
import { ref } from 'vue';
import router from '@/router';
import { ComponentInternalInstance, getCurrentInstance } from 'vue';
import { getTableInfo } from '@/minix/tables';
import { ConsultationHospital } from '@/model/consultation';
import { deleteGroup } from '@/api/evms/consultation';
import { BaseApi } from '@/model/api';

interface ApiResponse<T = any> {
    code: number;
    msg?: string;
    data: T;
}

interface Leader {
    realname: string;
    id: number;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const tableInfo = getTableInfo<ConsultationHospital>('/cns/teaching_group/list', {
    sort: 'id desc'
});
tableInfo.load();

// 新增
const handleAdd = () => {
    router.push({ path: '/consultation/hospital_group_info' });
};
const ids = ref<number[]>([]);
const single = ref(true);
const multiple = ref(true);

// 多选框选中数据
function handleSelectionChange(selection: ConsultationHospital[]) {
    ids.value = selection.map(item => Number(item.id) || 0);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

// 修改
const handleUpdate = (row?: ConsultationHospital) => {
    router.push({ path: '/consultation/hospital_group_info/' + (row ? row.id : ids.value.join(',')) });
};

// 删除
const handleDelete = async (row?: ConsultationHospital) => {
    const _ids: string = row?.id?.toString() || ids.value.join(',');
    try {
        await proxy!.$modal.confirm('是否确认群组ID为"' + _ids + '"的数据项？');
        const response = await deleteGroup(_ids);
        if (response.code === 200) {
            proxy!.$modal.msgSuccess('删除成功');
            await tableInfo.search();
        } else {
            proxy!.$modal.msgError(response.msg || '删除失败');
        }
    } catch (error) {
        proxy!.$modal.msgError('操作失败');
    }
};

// 查看群组成员
const handleLook = (row: ConsultationHospital) => {
    router.push({
        path: '/consultation/hospital_group_info/' + row.id,
        query: { look: '1' }
    });
};
</script>

<template>
    <div class="app-container">
        <el-card shadow="never" style="padding-right: 50px; border-radius: 12px">
            <el-form :inline="true" :model="tableInfo.from" label-width="100" size="default">
                <el-form-item label="群组名称" prop="id">
                    <el-input
                        v-model="tableInfo.from.like_name"
                        clearable
                        placeholder="请输入"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="成员姓名" prop="type">
                    <el-input
                        v-model="tableInfo.from.like_Jmembers_Juser_realname"
                        clearable
                        placeholder="请输入姓名"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="成员身份" prop="type" style="width: 100%; background-color: #fff">
                    <el-radio-group v-model="tableInfo.from.q_eq_Jmembers_leader_Boolean">
                        <!-- works when >=2.6.0, recommended ✔️ not work when <2.6.0 ❌ -->
                        <el-radio :label="true">专家</el-radio>
                        <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
                        <el-radio :label="false">成员</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-row :gutter="20" justify="start" style="padding-left: 50px">
                    <el-button v-hasPermi="['cns:teaching_group:query']" icon="Search" type="primary"
                               @click="tableInfo.search">搜索
                    </el-button>
                    <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                </el-row>

                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button v-hasPermi="['cns:teaching_group:add']" color="#009dff" icon="Plus" plain
                               @click="handleAdd">新增
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:teaching_group:edit']"
                        :disabled="single"
                        color="#01c064"
                        icon="Edit"
                        plain
                        @click="() => handleUpdate()"
                    >修改
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:teaching_group:remove']"
                        :disabled="multiple"
                        color="#ff5c00"
                        icon="Delete"
                        plain
                        @click="() => handleDelete()"
                    >删除
                    </el-button>
                </el-col>
                <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
            </el-row>

            <el-table
                :data="tableInfo.list.value"
                height="450"
                row-key="id"
                style="margin-top: 20px"
                @selection-change="handleSelectionChange"
            >
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="群组ID" prop="id" />
                <el-table-column align="center" label="群组名称" prop="name" />

                <el-table-column align="center" label="群组所属专家" prop="leaders">
                    <template #default="scope">
                        <span>{{
                                scope.row.leaders
                                    ? scope.row.leaders.map((i: Leader) => i.realname).join('，')
                                    : '--'
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="群组人数" prop="memberCount" />

                <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                    <template #default="scope">
                        <el-button
                            v-hasPermi="['cns:teaching_group:edit']"
                            icon="Edit"
                            link
                            type="primary"
                            @click="handleUpdate(scope.row)"
                        >修改
                        </el-button>
                        <el-button
                            v-hasPermi="['cns:teaching_group:remove']"
                            icon="Delete"
                            link
                            type="danger"
                            @click="handleDelete(scope.row)"
                        >删除
                        </el-button>
                        <el-button
                            icon="User"
                            link
                            type="primary"
                            @click="handleLook(scope.row)"
                        >查看群组成员
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    v-model:page="tableInfo.pageParam.pageNum"
                    :total="tableInfo.total.value"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
    </div>
</template>

<style lang="scss"></style>
