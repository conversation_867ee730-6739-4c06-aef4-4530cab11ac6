import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn,
    ElUpload,
    UploadRawFile,
    genFileId,
    UploadFiles,
    UploadFile,
    UploadUserFile
} from 'element-plus';
import { RefsKes } from '@/type/common';
import { EvmsConsultExpert } from '@/api/evms/expert';
import ContractList from './contractList';
import { ContractListType, UpdateExpert, updateExpert, UploadDetail } from '@/api/evms/expert';
import moment from 'moment';

export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const state = reactive({
            visible: false,
            form: {} as UpdateExpert,
            rules: {
                // fileOriginalName: [
                //     { required: true, message: '合同链接不能为空', trigger: 'change' }
                // ],
                daterange: [{ required: true, message: '签约起止时间不能为空', trigger: 'change' }] as any[]
            },
            uploadAction: import.meta.env.VITE_APP_BASE_API + '/evms/contract/uploadFile',
            uploadLoading: false
        });

        let instance: RefsKes;
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });
        function handleOk() {
            if (state.uploadLoading) {
                return proxy?.$modal.msgWarning('文件上传中,请稍等.....');
            }
            instance.refs.formRef.validate((valid: any, fields: any) => {
                if (!state.form.sysFileId) {
                    state.form.removeSysFileId = true;
                } else {
                    state.form.removeSysFileId = false;
                }
                state.form.signingStartTimeStamp = moment(state.form.daterange[0]).valueOf();
                state.form.signingEndTimeStamp = moment(state.form.daterange[1]).valueOf();
                updateExpert(state.form).then(res => {
                    close();
                    emit('ok');
                });
            });
        }
        function close() {
            state.visible = false;
        }

        function show(record: EvmsConsultExpert) {
            state.visible = true;
            state.form = {} as UpdateExpert;
            state.form.doctorId = record.doctorId;
            state.form.id = record.id;
            state.form.realName = record.realName;
            state.form.consultationStatus = record.consultationStatus;
            state.form.sysFileId = record.sysFileId;
            state.form.fileOriginalName = record.fileOriginalName;
            state.form.contractUrl = record.contractUrl;
            state.form.daterange = [
                moment(record.signingStartTimeStamp).format('YYYY-MM-DD HH:mm:ss'),
                moment(record.signingEndTimeStamp).format('YYYY-MM-DD HH:mm:ss')
            ];
            instance.refs.formRef && instance.refs.formRef.resetFields();
        }

        function handleContractShow() {
            instance.refs.ContractListRef.show();
        }

        function contractOk(value: ContractListType) {
            state.form.contractUrl = value.fileUrl;
            state.form.sysFileId = value.sysFileId;
            state.form.fileOriginalName = value.fileOriginalName;
        }

        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                },
            },
            upload() {
                return (
                    <ElButton loading={state.uploadLoading} type="primary" icon="Upload">
                        {state.uploadLoading ? '上传中' : '上传合同'}
                    </ElButton>
                );
            },
            append() {
                return (
                    // <ElButton type="primary" onClick={handleContractShow}>
                    //     合同选择
                    // </ElButton>
                    <div>
                        <ElUpload
                            ref="uploadRef"
                            class="upload-demo"
                            action={state.uploadAction}
                            limit={1}
                            on-exceed={handleExceed}
                            auto-upload={true}
                            v-slots={{ trigger: slots.upload }}
                            show-file-list={false}
                            on-success={uploadSuccess}
                            on-progress={uploading}
                            on-error={uploadError}
                        ></ElUpload>
                    </div>
                );
            },
        };
        function handleExceed(files: File[], uploadFiles: UploadUserFile[]) {
            instance.refs.uploadRef!.clearFiles();
            const file = files[0] as UploadRawFile;
            file.uid = genFileId();
            instance.refs.uploadRef!.handleStart(file);
            instance.refs.uploadRef!.submit();
        }

        function uploadSuccess(
            response: UploadDetail | any,
            uploadFile?: UploadFile,
            uploadFiles?: UploadFiles
        ) {
            state.form.sysFileId = response.data.sysFileId;
            state.form.fileOriginalName = response.data.sysFile.original;
            state.form.contractUrl = response.data.sysFile.url;
            state.uploadLoading = false;
        }
        function uploading() {
            state.uploadLoading = true;
        }

        function uploadError() {
            state.uploadLoading = false;
        }
        function delContact() {
            state.form.sysFileId = null as any;
            state.form.fileOriginalName = '';
            state.form.contractUrl = '';
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="编辑专家签约状态"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                <ElForm ref="formRef" model={state.form} rules={state.rules} inline={true}>
                    <ElRow>
                        <ElCol span={24}>
                            <ElFormItem label="签约起止时间" prop="daterange">
                                <ElDatePicker
                                    v-model={state.form.daterange}
                                    value-format="YYYY-MM-DD"
                                    type="daterange"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                />
                            </ElFormItem>
                        </ElCol>

                        <ElCol span={12}>
                            <ElFormItem label="合同" prop="fileOriginalName">
                                <div style={'display: flex;'}>
                                    {/* <ElInput
                                        readonly
                                        v-model={state.form.fileOriginalName}
                                        placeholder="合同链接"
                                        v-slots={{ append: slots.append }}
                                    /> */}
                                    <a
                                        style={'margin-right: 10px;color:#0581ce;text-decoration: underline;'}
                                        target="_blank"
                                        href={state.form.contractUrl}
                                    >
                                        {state.form.fileOriginalName}
                                    </a>

                                    {slots.append()}

                                    <ElButton onClick={delContact} style={'margin-left: 10px;'} link>
                                        删除
                                    </ElButton>
                                </div>
                            </ElFormItem>
                        </ElCol>
                    </ElRow>
                </ElForm>
                {/* <ContractList onOk={contractOk} ref="ContractListRef"></ContractList> */}
            </ElDialog>
        );
    },
});
