/*
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-06-25 15:22:20
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-06-25 16:35:46
 * @FilePath: /evms-web/src/api/oss/sts.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request';

// STS临时凭证响应接口（与您的后台接口保持一致）
export interface STSTokenResponse {
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    bucket: string; // 注意：您的接口返回的是 bucket 而不是 bucketName
    endpoint: string;
    expiration: string;
    requestId?: string;
}


/**
 * 获取OSS STS临时凭证
 */
export function getOSSSTSToken() {
    return request({
        url: '/sys/oss/getAssumeRoleResponse', // 使用您的实际接口路径
        method: 'get'
    });
}

/**
 * 刷新OSS STS临时凭证
 */
export function refreshOSSSTSToken() {
    return request({
        url: '/api/oss/sts-token/refresh',
        method: 'post'
    });
}

/**
 * 获取OSS配置信息
 */
export function getOSSConfig() {
    return request({
        url: '/api/oss/config',
        method: 'get'
    });
}
