import request from '@/utils/request';

// 查询文件管理列表
export function listFile(query: any) {
    return request({
        url: '/sys/file/list',
        method: 'get',
        params: query,
    });
}

// 查询文件管理详细
export function getFile(id: any) {
    return request({
        url: '/sys/file/' + id,
        method: 'get',
    });
}

// 新增文件管理
export function addFile(data: any) {
    return request({
        url: '/sys/file',
        method: 'post',
        data: data,
    });
}

// 修改文件管理
export function updateFile(data: any) {
    return request({
        url: '/sys/file',
        method: 'put',
        data: data,
    });
}

// 删除文件管理
export function delFile(id: any) {
    return request({
        url: '/sys/file/' + id,
        method: 'delete',
    });
}
