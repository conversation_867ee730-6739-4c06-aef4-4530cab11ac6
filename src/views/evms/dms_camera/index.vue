<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="所属设备ID" prop="deviceId">
                <el-input
                    v-model="queryParams.deviceId"
                    placeholder="请输入所属设备ID"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="摄像头名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入摄像头名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="摄像头通道编号" prop="channel">
                <el-input
                    v-model="queryParams.channel"
                    placeholder="请输入摄像头通道编号"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="摄像头类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择摄像头类型" clearable>
                    <el-option
                        v-for="dict in evms_dms_camera_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:dms_camera:add']"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['evms:dms_camera:edit']"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['evms:dms_camera:remove']"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['evms:dms_camera:export']"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="DmsCameraList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="所属设备ID" align="center" prop="deviceVo.name" />
            <el-table-column label="摄像头名称" align="center" prop="name" />
            <el-table-column label="摄像头通道编号" align="center" prop="channel" />
            <el-table-column
                label="摄像头类型，0代表类型未知的摄像头，1代表IP Camera，2代表视频编码器"
                align="center"
                prop="type"
            >
                <template #default="scope">
                    <dict-tag :options="evms_dms_camera_type" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:dms_camera:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:dms_camera:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改DMS摄像头对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="DmsCameraRef" :model="form" :rules="rules" label-width="80px">
                <!--        <el-form-item label="所属设备" prop="deviceId">-->
                <!--          <el-input v-model="form.deviceName" placeholder="请输入所属设备" />-->
                <!--        </el-form-item>-->

                <el-form-item label="所属设备" prop="deviceId">
                    <el-select
                        style="width: 100%"
                        v-model="form.deviceId"
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入设备名称"
                        remote-show-suffix
                        :remote-method="searchDevice"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in deviceList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="摄像头名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入摄像头名称" />
                </el-form-item>
                <el-form-item label="摄像头通道编号" prop="channel">
                    <el-input v-model="form.channel" placeholder="请输入摄像头通道编号" />
                </el-form-item>
                <el-form-item label="摄像头类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择摄像头类型">
                        <el-option
                            v-for="dict in evms_dms_camera_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="DmsCamera" lang="ts">
import {
    listDmsCamera,
    getDmsCamera,
    delDmsCamera,
    addDmsCamera,
    updateDmsCamera,
    getDeviceListByKeywords
} from '@/api/evms/dms_camera';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_camera_type } = proxy!.useDict('evms_dms_camera_type');

const DmsCameraList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const deviceList = ref<any[]>([]);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: undefined,
        name: undefined,
        channel: undefined,
        type: undefined
    },
    rules: {
        deviceId: [{ required: true, message: '所属设备ID不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '摄像头名称不能为空', trigger: 'blur' }],
        channel: [{ required: true, message: '摄像头通道编号不能为空', trigger: 'blur' }],
        type: [
            {
                required: true,
                message: '摄像头类型，0代表类型未知的摄像头，1代表IP Camera，2代表视频编码器不能为空',
                trigger: 'change'
            }
        ],
        createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询DMS摄像头列表 */
function getList() {
    loading.value = true;

    listDmsCamera(queryFormat(queryParams.value)).then((response: any) => {
        DmsCameraList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        deviceId: undefined,
        name: undefined,
        channel: undefined,
        type: undefined,
        createBy: undefined,
        updateBy: undefined,
        createTime: undefined,
        updateTime: undefined,
        deviceName: undefined
    };
    proxy!.resetForm('DmsCameraRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

function searchDevice(name?: String) {
    getDeviceListByKeywords({ keywords: name }).then(response => {
        deviceList.value = response.data;
    });
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加DMS摄像头';
    deviceList.value = [];
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    deviceList.value = [row.deviceVo];
    getDmsCamera(_id).then((response: any) => {
        form.value = response.data;
        form.value.deviceId = response.data.deviceVo.id;
        form.value.deviceName = response.data.deviceVo.name;
        open.value = true;
        title.value = '修改DMS摄像头';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['DmsCameraRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateDmsCamera(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addDmsCamera(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除DMS摄像头编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delDmsCamera(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'evms/DmsCamera/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `DmsCamera_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
