<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="字典名称" prop="dict_code">
                <el-select v-model="queryParams.dict_code" style="width: 200px">
                    <el-option
                        v-for="item in typeOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.code"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="字典标签" prop="label">
                <el-input
                    v-model="queryParams.label"
                    placeholder="请输入字典标签"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <!-- <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="数据状态" clearable style="width: 200px">
                    <el-option
                        v-for="dict in sys_normal_disable"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:dict:add']" type="primary" plain icon="Plus" @click="handleAdd"
                    >新增</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:dict:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    >修改</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:dict:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    >删除</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:dict:export']"
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    >导出</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Close" @click="handleClose">关闭</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="dataList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="字典ID" align="center" prop="id" />
            <el-table-column label="字典标签" align="center" prop="label">
                <template #default="scope">
                    <span v-if="scope.row.listClass == '' || scope.row.listClass == 'default'">{{
                        scope.row.label
                    }}</span>
                    <el-tag v-else :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass">{{
                        scope.row.label
                    }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="字典键值" align="center" prop="value" />
            <el-table-column label="字典排序" align="center" prop="sort" />
            <!-- <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
                </template>
            </el-table-column> -->
            <!-- <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" /> -->
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['sys:dict:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        >修改</el-button
                    >
                    <el-button
                        v-hasPermi="['sys:dict:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改参数配置对话框 -->
        <el-dialog v-model="open" :title="title" width="500px" append-to-body>
            <el-form ref="dataRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="字典类型">
                    <el-input v-model="form.dictCode" :disabled="true" />
                </el-form-item>
                <el-form-item label="数据标签" prop="label">
                    <el-input v-model="form.label" placeholder="请输入数据标签" />
                </el-form-item>
                <el-form-item label="数据键值" prop="value">
                    <el-input v-model="form.value" placeholder="请输入数据键值" />
                </el-form-item>
                <!-- <el-form-item label="样式属性" prop="cssClass">
                    <el-input v-model="form.cssClass" placeholder="请输入样式属性" />
                </el-form-item> -->
                <el-form-item label="显示排序" prop="sort">
                    <el-input-number v-model="form.sort" controls-position="right" :min="0" />
                </el-form-item>
                <!-- <el-form-item label="回显样式" prop="listClass">
                    <el-select v-model="form.listClass">
                        <el-option
                            v-for="item in listClassOptions"
                            :key="item.value"
                            :label="item.label + '(' + item.value + ')'"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item> -->
                <!-- <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
                            dict.label
                        }}</el-radio>
                    </el-radio-group>
                </el-form-item> -->
                <!-- <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
                </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Data" lang="ts">
/* eslint-disable camelcase */
import useDictStore from '@/store/modules/dict';
import { all as getDictOptionselect, getType } from '@/api/system/dict/type';
import { listData, getData, delData, addData, updateData } from '@/api/system/dict/data';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_normal_disable } = proxy!.useDict('sys_normal_disable');

const dataList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const defaultDictType = ref('');
const typeOptions = ref<any[]>([]);
const route = useRoute();
// 数据标签回显样式
const listClassOptions = ref([
    { value: 'default', label: '默认' },
    { value: 'primary', label: '主要' },
    { value: 'success', label: '成功' },
    { value: 'info', label: '信息' },
    { value: 'warning', label: '警告' },
    { value: 'danger', label: '危险' },
]);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        code: undefined,
        // status: undefined,
    },
    rules: {
        label: [{ required: true, message: '数据标签不能为空', trigger: 'blur' }],
        value: [{ required: true, message: '数据键值不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '数据顺序不能为空', trigger: 'blur' }],
    },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询字典类型详细 */
function getTypes(dictId: any) {
    getType(dictId).then(response => {
        queryParams.value.dict_code = response.data.code;
        defaultDictType.value = response.data.code;
        getList();
    });
}

/** 查询字典类型列表 */
function getTypeList() {
    getDictOptionselect().then(response => {
        typeOptions.value = response.data;
    });
}
/** 查询字典数据列表 */
function getList() {
    loading.value = true;
    listData(queryFormat(queryParams.value)).then((response: any) => {
        dataList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}
/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}
/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        label: undefined,
        value: undefined,
        // cssClass: undefined,
        // listClass: 'default',
        sort: 0,
        // status: '0',
        // remark: undefined,
    };
    proxy!.resetForm('dataRef');
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}
/** 返回按钮操作 */
function handleClose() {
    const obj = { path: '/system/dict' };
    proxy!.$tab.closeOpenPage(obj);
}
/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    queryParams.value.dict_code = defaultDictType;
    handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加字典数据';
    form.value.dictCode = queryParams.value.dict_code;
}
/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}
/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const id = row.id || ids.value;
    getData(id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = '修改字典数据';
    });
}
/** 提交按钮 */
function submitForm() {
    console.log('before check: ' + form.value.dictCode);
    (proxy?.$refs['dataRef'] as any).validate((valid: any) => {
        if (valid) {
            console.log('after check: ' + form.value.dictCode);
            if (form.value.id !== undefined) {
                console.log(form.value);
                updateData(form.value).then(response => {
                    useDictStore().removeDict(queryParams.value.dict_code);
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addData(form.value).then(response => {
                    useDictStore().removeDict(queryParams.value.dict_code);
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row: any) {
    const dictCodes = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除字典编码为"' + dictCodes + '"的数据项？')
        .then(function () {
            return delData(dictCodes);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
            useDictStore().removeDict(queryParams.value.dict_code);
        })
        .catch((e: any) => {
            console.log(e);
        });
}
/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'sys/dict/data/exportExcel',
        {
            ...queryFormat(queryParams.value),
        },
        `dict_data_${new Date().getTime()}.xlsx`
    );
}

getTypes(route.params && route.params.dictId);
getTypeList();
</script>
