import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { defineComponent, getCurrentInstance, ref, reactive, toRefs, onMounted, watch } from 'vue';
import RightToolar from '@/components/RightToolbar/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import DictTag from '@/components/DictTag/index.vue';
import FileUpload from '@/components/FileUpload/index.vue';
import { listOrdert, EvmsCousultOrderDetail, cancelOrdert, updateOrdert } from '@/api/evms/ordert';
import moment from 'moment';
import { queryFormat, parseTime, instanceOfObj } from '@/utils/ruoyi';
import ConsultOrder from './model/consultOrder';
import { RefsKes } from '@/type/common';
import VideoList from './model/videoList';
import Pagination from '@/components/Pagination/index.vue';
import ParticipatingExpert from './model/participatingExpert';
import useCommonStore from '@/store/modules/common';

interface QueryParams extends Partial<EvmsCousultOrderDetail> {
    pageNum: number;
    pageSize: number;
}
export default defineComponent({
    setup() {
        const { proxy } = getCurrentInstance()!;
        let instance: RefsKes;
        const state = reactive({
            ordertList: [] as EvmsCousultOrderDetail[],
            loading: true,
            showSearch: true,
            total: 0,
            multiple: false,
            queryParams: {
                pageNum: 1,
                pageSize: 10
            } as QueryParams,
            daterange: [] as string[],
            form: {} as {
                orderStatus: string;
                orderNumber: string;
                deviceId: number;
                surgicalTimeStamp: number;
            }
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function getList() {
            const evmsOrdertQuery = instanceOfObj(useCommonStore().getLocalKey('evmsOrdertQuery'));

            if (evmsOrdertQuery) {
                state.queryParams.orderStatus = evmsOrdertQuery.orderStatus;
                state.queryParams.realName = evmsOrdertQuery.realName;
                state.queryParams.expertId = evmsOrdertQuery.expertId;
            }

            state.loading = true;
            if (state.daterange.length === 2) {
                state.queryParams.start = moment(state.daterange[0]).valueOf();
                state.queryParams.end = moment(state.daterange[1]).valueOf();
            }
            listOrdert(state.queryParams).then(response => {
                state.ordertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function handleQuery() {
            state.queryParams.pageNum = 1;
            useCommonStore().setLocal({
                name: 'evmsOrdertQuery',
                status: {
                    orderStatus: state.queryParams.orderStatus,
                    realName: state.queryParams.realName,
                    expertId: state.queryParams.expertId
                }
            });
            getList();
        }

        watch(
            () => state.daterange,
            val => {
                if (!val) {
                    state.daterange = [];
                    state.queryParams.start = null;
                    state.queryParams.end = null;
                }
            }
        );

        function resetQuery() {
            proxy?.resetForm('queryRef');
            state.daterange = [];
            state.queryParams.start = null;
            state.queryParams.end = null;
            state.queryParams.orderStatus = '';
            state.queryParams.realName = '';
            state.queryParams.expertId = null;
            handleQuery();
            useCommonStore().setLocal({
                name: 'evmsOrdertQuery',
                status: null
            });
        }

        function handleAdd() {
            instance.refs.ConsultOrderRef.add();
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        function handleEdit(record: EvmsCousultOrderDetail) {
            instance.refs.ConsultOrderRef.edit(record);
        }

        function viewVideo(record: EvmsCousultOrderDetail) {
            instance.refs.VideoListRef.show(record);
        }

        function cancelOrder(record: EvmsCousultOrderDetail) {
            proxy?.$modal
                .confirm('是否确认取消订单？')
                .then(() => {
                    return cancelOrdert({ cancelReason: '', orderNumber: record.orderNumber });
                })
                .then(() => {
                    getList();
                    proxy?.$modal.msgSuccess('取消成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        function showExperts(record: EvmsCousultOrderDetail) {
            instance.refs.ParticipatingExpertRef.show(record);
        }

        function editStatus(record: EvmsCousultOrderDetail) {
            state.form.orderStatus = 'OK';
            state.form.orderNumber = record.orderNumber;
            state.form.deviceId = record.deviceId;
            state.form.surgicalTimeStamp = record.surgicalTimeStamp;
            // 取消 已完成 不可以修改
            proxy?.$modal
                .confirm('是否确认修改订单状态为已完成？')
                .then(() => {
                    return updateOrdert(state.form as any);
                })
                .then(() => {
                    getList();
                    proxy?.$modal.msgSuccess('修改成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        const slots = {
            column: {
                cousultType({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return <p>{record.cousultType === 1 ? '急诊' : '预约'}</p>;
                },
                createTimeStamp({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <span>
                            {record.createTimeStamp
                                ? moment(record.createTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                surgicalTimeStamp({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <span>
                            {record.surgicalTimeStamp
                                ? moment(record.surgicalTimeStamp).format('YYYY-MM-DD HH:mm')
                                : '--'}
                        </span>
                    );
                },
                duration({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <span>
                            {moment.duration(record.duration).hours().toString().padStart(2, '0') +
                                ':' +
                                moment.duration(record.duration).minutes().toString().padStart(2, '0') +
                                ':' +
                                moment.duration(record.duration).seconds().toString().padStart(2, '0')}
                        </span>
                    );
                },
                deviceName({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return <div>{record.deviceName}</div>;
                },
                orderStatus({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <div>
                            <p v-show={record.orderStatus === 'MATCHING'}>待接单</p>
                            <p v-show={record.orderStatus === 'WAIT'}>待开始</p>
                            <p v-show={record.orderStatus === 'HAVE_IN_HAND'}>进行中</p>
                            <p v-show={record.orderStatus === 'OK'}>完成</p>
                            <p v-show={record.orderStatus === 'CANCEL'}>取消</p>
                        </div>
                    );
                },
                experts({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <div>
                            {record.experts.map((item, index) => {
                                return (
                                    <div>
                                        {item.expertName + (index + 1 < record.experts.length ? ',' : '')}
                                    </div>
                                );
                            })}
                        </div>
                    );
                },
                userAndPatient({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <div>
                            {record.userName}/{record.patientName}
                        </div>
                    );
                }
            },
            actionSlots({ row: record }: { row: EvmsCousultOrderDetail }) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:ordert:edit']]}
                            link
                            type="primary"
                            onClick={() => handleEdit(record)}
                        >
                            编辑
                        </ElButton>
                        <ElButton
                            v-hasPermi={[['evms:ordert:query']]}
                            link
                            type="primary"
                            onClick={() => viewVideo(record)}
                        >
                            查看录像
                        </ElButton>
                        <ElButton
                            v-show={record.orderStatus === 'WAIT' || record.orderStatus === 'MATCHING'}
                            link
                            v-hasPermi={[['evms:ordert:edit']]}
                            type="primary"
                            onClick={() => cancelOrder(record)}
                        >
                            取消订单
                        </ElButton>
                        <ElButton
                            link
                            type="primary"
                            v-hasPermi={[['evms:ordert:edit']]}
                            onClick={() => showExperts(record)}
                        >
                            参会专家
                        </ElButton>
                        {record.orderStatus !== 'OK' && record.orderStatus !== 'CANCEL' ? (
                            <ElButton
                                link
                                type="primary"
                                v-hasPermi={[['evms:ordert:edit']]}
                                onClick={() => editStatus(record)}
                            >
                                完成订单
                            </ElButton>
                        ) : (
                            ''
                        )}
                    </>
                );
            }
        };

        getList();
        return () => (
            <div class="app-container">
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="医院名称" prop="hospitalName">
                        <ElInput
                            v-model={state.queryParams.hospitalName}
                            placeholder="请输入医院名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>

                    <ElFormItem label="专家名称" prop="realName">
                        <ElInput
                            v-model={state.queryParams.realName}
                            placeholder="请输入专家名称"
                            clearable
                            style="width: 240px"
                        />
                    </ElFormItem>

                    <ElFormItem label="专家ID" prop="expertId">
                        <ElInput
                            v-model={state.queryParams.expertId}
                            placeholder="请输入专家ID"
                            clearable
                            style="width: 240px"
                        />
                    </ElFormItem>

                    <ElFormItem label="会诊类型" prop="cousultType">
                        <ElSelect
                            placeholder="请选择会诊类型"
                            v-model={state.queryParams.cousultType}
                            clearable
                        >
                            <ElOption key={0} label="全部" value={''}></ElOption>
                            <ElOption key={1} label="急诊" value={1}></ElOption>
                            <ElOption key={2} label="预约" value={2}></ElOption>
                        </ElSelect>
                    </ElFormItem>

                    <ElFormItem label="订单状态" prop="cousultType">
                        <ElSelect
                            placeholder="请选择订单状态"
                            v-model={state.queryParams.orderStatus}
                            clearable
                        >
                            <ElOption key={0} label="全部" value={''}></ElOption>
                            <ElOption key={1} label="待接单" value={'MATCHING'}></ElOption>
                            <ElOption key={2} label="待开始" value={'WAIT'}></ElOption>
                            <ElOption key={2} label="进行中" value={'HAVE_IN_HAND'}></ElOption>
                            <ElOption key={3} label="完成" value={'OK'}></ElOption>
                            <ElOption key={4} label="取消" value={'CANCEL'}></ElOption>
                        </ElSelect>
                    </ElFormItem>

                    <ElFormItem label="订单建立时间" prop="createTimeStamp">
                        <ElDatePicker
                            v-model={state.daterange}
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                        />
                    </ElFormItem>

                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}

                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['sys:test:add']]}
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={handleAdd}
                        >
                            添加会诊订单
                        </ElButton>
                    </ElCol>
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable v-loading={state.loading} data={state.ordertList}>
                    <ElTableColumn
                        label="订单建立时间"
                        align="center"
                        prop="createTimeStamp"
                        v-slots={slots.column.createTimeStamp}
                    />
                    <ElTableColumn
                        label="会诊类型"
                        align="center"
                        prop="cousultType"
                        v-slots={slots.column.cousultType}
                    />
                    <ElTableColumn label="医院名称" align="center" prop="hospitalName" />
                    <ElTableColumn label="发起人" align="center" v-slots={slots.column.deviceName} />
                    <ElTableColumn
                        label="订单状态"
                        align="center"
                        v-slots={slots.column.orderStatus}
                    ></ElTableColumn>

                    <ElTableColumn
                        label="邀请专家名称"
                        align="center"
                        prop="experts"
                        v-slots={slots.column.experts}
                    ></ElTableColumn>

                    <ElTableColumn label="接单专家名" align="center" prop="expertName"></ElTableColumn>

                    <ElTableColumn
                        label="用户/患者信息"
                        align="center"
                        v-slots={slots.column.userAndPatient}
                    ></ElTableColumn>

                    <ElTableColumn
                        label="预定手术时间"
                        align="center"
                        prop="surgicalTimeStamp"
                        v-slots={slots.column.surgicalTimeStamp}
                    />
                    <ElTableColumn
                        label="手术会议时长"
                        align="center"
                        v-slots={slots.column.duration}
                        prop="duration"
                    />

                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    ></ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
                <ConsultOrder onOk={handleQuery} ref="ConsultOrderRef"></ConsultOrder>
                <VideoList ref="VideoListRef"></VideoList>
                <ParticipatingExpert onOk={handleQuery} ref="ParticipatingExpertRef"></ParticipatingExpert>
            </div>
        );
    },
});
