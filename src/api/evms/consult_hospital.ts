import request from '@/utils/request';
interface Response<T> {
    msg: string;
    code: number;
    data: T;
    timestamp: number;
    success: boolean;
}
interface PageResponse<T> {
    list: Array<T>;
    total: number;
    pages: number;
}
export interface DeviceVos {
    cycleCount: number;
    cycleSurplusCount: number;
    deviceId: number;
    deviceName: string;
    expertCount: number;
    hospitalId: number;
    hospitalName: string;
    signingEndTime: string;
    signingEndTimeStamp: number;
    signingStartTime: string;
    signingStartTimeStamp: number;
    totalCount: number;
    signingStatus: boolean;
    contractId: number;
}
export interface EvmsConsultHospitalExpert {
    deviceVos: DeviceVos[] | undefined;
    expertCount: number;
    hospitalId: number;
    hospitalName: string;
    totalCount: number;
    totalSurplusCount: number;
    hasChildren: boolean;
    children: DeviceVos[] | undefined;
    signingEndTimeStamp: number | null;
    signingStartTimeStamp: number | null;
    signingStatus: boolean;
}

export interface HospitalContract {
    contractAmount: number;
    contractCount: number;
    contractId?: number; // 修改时必传
    contractTotalAmount: number;
    contractUrl: string;
    signingEndTimeStamp: number;
    signingStartTimeStamp: number;
    sysFileId: number;
    daterange: string[];
    fileOriginalName: string;
    deviceId: number;
    removeSysFileId: boolean;
}
// 查询会诊医院列表
export function listConsultHospital(query: any) {
    return request({
        url: '/evms/consult/hospital/list',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<PageResponse<EvmsConsultHospitalExpert>>>;
}

// 查询会诊医院详细
export function getConsultHospital(id: any) {
    return request({
        url: '/evms/consult/hospital/' + id,
        method: 'get',
    }) as unknown as Promise<Response<EvmsConsultHospitalExpert>>;
}

// 新增会诊医院
export function addConsultHospital(data: EvmsConsultHospitalExpert) {
    return request({
        url: '/evms/consult/hospital',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<EvmsConsultHospitalExpert>>;
}

// 修改会诊医院
export function updateConsultHospital(data: EvmsConsultHospitalExpert) {
    return request({
        url: '/evms/consult/hospital',
        method: 'put',
        data: data,
    }) as unknown as Promise<Response<EvmsConsultHospitalExpert>>;
}

// 删除会诊医院
export function delConsultHospital(id: any) {
    return request({
        url: '/evms/consult/hospital/' + id,
        method: 'delete',
    }) as unknown as Promise<Response<EvmsConsultHospitalExpert>>;
}

// 绑定医生
export function hospitalBindingDoc(data: { expertId: number; hospitalId: number }) {
    return request({
        url: '/evms/consult/hospital/saveExpert',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 解绑医生
export function hospitalUnbindingDoc(data: { expertId: number; hospitalId: number }) {
    return request({
        url: '/evms/consult/hospital/deleteExpert',
        method: 'delete',
        params: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 设备签约
export function hospitalContract(data: HospitalContract) {
    return request({
        url: '/evms/consult/hospital/saveContract',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 设备签约修改
export function hospitalContractUpdate(data: HospitalContract) {
    return request({
        url: '/evms/consult/hospital/updateContract',
        method: 'put',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}
