<template>
    <div class="app-container">
        <el-card shadow="never" style="border-radius: 12px">
            <el-form
                v-show="showSearch"
                ref="queryRef"
                :inline="true"
                :model="tableInfo.from"
                label-width="100"
                size="default"
            >
                <el-form-item label="ID" prop="id">
                    <el-input
                        v-model.trim="tableInfo.from.id"
                        clearable
                        placeholder="请输入ID"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="设备名称" prop="name">
                    <el-input
                        v-model.trim="tableInfo.from.like_name"
                        clearable
                        placeholder="请输入设备名称"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="GUID" prop="nativeId">
                    <el-input
                        v-model.trim="tableInfo.from.nativeId"
                        clearable
                        placeholder="请输入GUID"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>

                <el-form-item label="所在医院" prop="like_Jhospital_name">
                    <el-autocomplete
                        v-model="tableInfo.from.like_Jhospital_name"
                        :fetch-suggestions="querySearch"
                        clearable
                        placeholder="请输入医院名称"
                        style="width: 240px"
                        @select="handleSelect"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <!-- 可折叠的高级搜索项 -->
                <template v-if="isExpand">
                    <el-form-item label="所连服务器" prop="like_JrabbitmqConfig_name">
                        <el-input
                            v-model.trim="tableInfo.from.like_JrabbitmqConfig_name"
                            clearable
                            placeholder="请输入服务器名称"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="类型" prop="type">
                        <el-select
                            v-model="tableInfo.from.type"
                            clearable
                            placeholder="请选择类型"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in evms_dms_device_type"
                                :key="dict.value"
                                :label="dict.label"
                                :value="parseInt(dict.value)"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="Jstatus_status">
                        <el-select
                            v-model="tableInfo.from.JdeviceStatusList_status"
                            clearable
                            placeholder="请选择状态"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="(dict, index) in evms_dms_device_status"
                                :key="index"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="版本" prop="Jstatus_version">
                        <el-input
                            v-model.trim="tableInfo.from.JdeviceStatusList_version"
                            clearable
                            placeholder="请输入版本"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                </template>

                <!-- 搜索按钮区域 -->
                <el-row :gutter="20" justify="start" style="width: 100%; margin-top: 10px">
                    <el-col :span="12" style="padding-left: 50px">
                        <el-button icon="Search" type="primary" @click="tableInfo.search">搜索</el-button>
                        <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                        <el-button type="text" @click="isExpand = !isExpand">
                            {{ isExpand ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <arrow-up v-if="isExpand" />
                                <arrow-down v-else />
                            </el-icon>
                        </el-button>
                    </el-col>
                </el-row>
                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <el-row :gutter="10">
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['evms:dms_device:add']"
                        color="#009dff"
                        icon="Plus"
                        plain
                        @click="handleAdd"
                    >新增
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['evms:dms_device:remove']"
                        :disabled="multiple"
                        color="#ff5c00"
                        icon="Delete"
                        plain
                        @click="() => handleDelete()"
                    >删除
                    </el-button>
                </el-col>
            </el-row>

            <el-table
                v-loading="tableInfo.TableLoading.value"
                :data="tableInfo.list.value"
                height="600"
                row-key="id"
                style="margin-top: 10px"
                @selection-change="handleSelectionChange"
            >
                <el-table-column align="center" type="selection" />
                <el-table-column align="center" label="ID" prop="id" width="80" />
                <el-table-column align="center" label="设备名称" prop="name" />
                <el-table-column align="center" label="所在医院" prop="hospitalVo.name" />
                <el-table-column align="center" label="所连服务器" prop="rabbitmqConfigVo.name" />
                <el-table-column align="center" label="版本号" prop="status.version" />
                <el-table-column align="center" label="类型" prop="type">
                    <template #default="scope">
                        <dict-tag :options="evms_dms_device_type" :value="scope.row.type" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="权重" prop="weight" />
                <el-table-column align="center" label="运行状态" prop="status" width="80">
                    <template #default="scope">
                        <dict-tag :options="evms_dms_device_status" :value="scope.row.status?.status" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="最近开机/关机时间" prop="onofflineTime" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.status?.onofflineTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    class-name="small-padding fixed-width"
                    label="操作"
                    width="220"
                >
                    <template #default="scope">
                        <el-button
                            v-hasPermi="['evms:dms_device:edit']"
                            icon="Edit"
                            link
                            type="primary"
                            @click="handleUpdate(scope.row)"
                        >修改
                        </el-button>
                        <el-button
                            v-hasPermi="['evms:dms_device:remove']"
                            icon="Delete"
                            link
                            type="danger"
                            @click="handleDelete(scope.row)"
                        >删除
                        </el-button>

                        <el-button
                            v-hasPermi="['cl:cathlab:list']"
                            icon="Search"
                            link
                            type="primary"
                            @click="handleEbsCaseReport(scope.row)"
                        >EBS病例管理
                        </el-button>
                        <el-button
                            v-hasPermi="['cl:cathlab:list']"
                            icon="Search"
                            link
                            type="primary"
                            @click="handleRegisterReport(scope.row)"
                        >注册报告
                        </el-button>
                        <el-button
                            v-hasPermi="['cl:cathlab:list']"
                            icon="Search"
                            link
                            type="primary"
                            @click="handleDeviceElabOnlineReport(scope.row)"
                        >线上巡检报告
                        </el-button>
                        <el-button
                            v-hasPermi="['cl:cathlab:list']"
                            icon="Search"
                            link
                            type="primary"
                            @click="handleDeviceElabOfflineReport(scope.row)"
                        >线下巡检报告
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    v-model:page="tableInfo.pageParam.pageNum"
                    :total="tableInfo.total.value"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
    </div>
</template>
<script lang="ts">
export default {
    name: 'Dms_device'
};
</script>
<script lang="ts" setup>
import { delDmsDevice } from '@/api/evms/dms_device';
import { getCurrentInstance, ComponentInternalInstance, ref } from 'vue';
import { getTableInfo } from '@/minix/tables';
import { DmsDeviceInfo } from '@/model/dms_device';
import { DmsHospitalInfo } from '@/model/dms_hospital';
import router from '@/router';
import { getHospitalListByKeywords } from '@/api/evms/dms_device';
import { BaseApi } from '@/model/api';
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';

// 添加展开折叠控制
const isExpand = ref(false);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_device_type } = proxy!.useDict('evms_dms_device_type');
const { evms_dms_device_status } = proxy!.useDict('evms_dms_device_status');
console.log(evms_dms_device_status, '---');
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const tableInfo = getTableInfo<DmsDeviceInfo>('/evms/dms_device/list');
tableInfo.load();
// 医院名称推荐选择
type HopitalItem = {
    value: string;
    id: number;
};
// 获取医院名称搜索结果
const querySearch = async (queryString: string, cb: any) => {
    const results = await getHospitalListByKeywords({ keywords: queryString });
    // call callback function to return suggestions
    if (results.code === 200) {
        console.log(results.data, '--1');
        // 判断results.data是一个数组并且长度大于0 使用type

        cb(
            results?.data && (results.data as DmsHospitalInfo[])?.length > 0
                ? (results.data as DmsHospitalInfo[]).map((item: any) => {
                    return {
                        value: item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};
const handleSelect = (item: HopitalItem) => {
    console.log(item);
    tableInfo.from.like_Jhospital_name = item.value;
};

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    router.push({ path: '/evms/dms/dms_device_info' });
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    const _id = row.id || ids.value;
    router.push({ path: '/evms/dms/dms_device_info/' + _id });
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
    const _ids = row?.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除DMS设备编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delDmsDevice(_ids);
        })
        .then((res: BaseApi<null>) => {
            if (res.code === 200) {
                tableInfo.search();
                proxy!.$modal.msgSuccess('删除成功');
            } else {
                proxy!.$modal.msgError(res.msg);
            }
        })
        .catch(() => {
        });
}

/** EBS病例管理 */
function handleEbsCaseReport(row: any) {
    const _id = row.id;
    router.push('/evms/dms/dms_device_ebsCase/' + _id);
}

/** 注册报告 */
function handleRegisterReport(row: any) {
    const _id = row.id;
    router.push('/evms/dms/dms_device_register_report/' + _id);
}

/** 线上巡检报告 */
function handleDeviceElabOnlineReport(row: any) {
    const _id = row.id;
    router.push('/evms/dms/dms_device_online_report/' + _id);
}

/** 线下巡检报告 */
function handleDeviceElabOfflineReport(row: any) {
    const _id = row.id;
    router.push('/evms/dms/dms_device_offline_report/' + _id);
}
</script>
<style lang="scss" scoped>
.app-container {
    background: #f8f9fb;
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
