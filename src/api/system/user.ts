import request from '@/utils/request';
import { parseStrEmpty } from '@/utils/ruoyi';

// 查询用户列表
export function listUser(query: any) {
    return request({
        url: '/sys/user/list',
        method: 'get',
        params: query,
    });
}

// 查询用户详细
export function getUser(userId?: any) {
    return request({
        url: '/sys/user/' + parseStrEmpty(userId),
        method: 'get',
    });
}

// 新增用户
export function addUser(data: any) {
    return request({
        url: '/sys/user',
        method: 'post',
        data: data,
    });
}

// 修改用户
export function updateUser(data: any) {
    return request({
        url: '/sys/user',
        method: 'put',
        data: data,
    });
}

// 删除用户
export function delUser(userId: any) {
    return request({
        url: '/sys/user/' + userId,
        method: 'delete',
    });
}

// 用户密码重置
export function resetUserPwd(userId: any, password: any) {
    const data = {
        id: userId,
        password,
    };
    return request({
        url: '/sys/user/resetPassword',
        method: 'put',
        params: data,
    });
}

// 用户状态修改
export function changeUserStatus(userId: any, status: any) {
    const data = {
        id: userId,
        status,
    };
    return request({
        url: '/sys/user/changeStatus',
        method: 'put',
        params: data,
    });
}

// 查询用户个人信息
export function getUserProfile() {
    return request({
        url: '/sys/user/profile',
        method: 'get',
    });
}

// 修改用户个人信息
export function updateUserProfile(data: any) {
    return request({
        url: '/sys/user/profile',
        method: 'put',
        data: data,
    });
}

// 用户密码重置
export function updateUserPwd(newPassword: any) {
    const data = {
        newPassword,
    };
    return request({
        url: '/sys/user/profile/updatePwd',
        method: 'put',
        params: data,
    });
}

// 用户头像上传
export function uploadAvatar(data: any) {
    return request({
        url: '/sys/user/profile/avatar',
        method: 'post',
        data: data,
    });
}

// 查询授权角色
export function getAuthRole(userId: any) {
    return request({
        url: '/sys/user/authRole/' + userId,
        method: 'get',
    });
}

// 保存授权角色
export function updateAuthRole(data: any) {
    return request({
        url: '/sys/user/authRole',
        method: 'put',
        params: data,
    });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
    return request({
        url: '/sys/dept/tree',
        method: 'get',
    });
}

// 查询角色列表
export function roleList() {
    return request({
        url: '/sys/user/roleList',
        method: 'get',
    });
}

// 查询岗位列表
export function postList() {
    return request({
        url: '/sys/post/all',
        method: 'get',
    });
}
