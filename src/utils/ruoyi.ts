/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */

export function comparsion(obj: { [key: string]: any } | null, comparsion: { [key: string]: any }) {
    if (!isObject(obj) || !obj) return comparsion;

    Object.keys(comparsion).forEach(item => {
        if (item in obj) return;
        obj[item] = comparsion[item];
    });

    return obj;
}

// 数组指定key去重
export function uniqueByKey(array: any[], key: string) {
    const seen = new Set();
    return array.filter(item => {
        const keyValue = item[key];
        return seen.has(keyValue) ? false : seen.add(keyValue);
    });
}

// 判断数据是不是对象
export function isObject(val: any): boolean {
    return val !== null && typeof val === 'object';
}

// 判断数据是不是数组
export function isArray(val: any): boolean {
    return toString.call(val) === '[object Array]';
}
// 数组或对象深拷贝
export function instanceOfObj(obj: any, excludes?: string[]): any {
    let returnContext: { [propsname: string]: any } | Array<any>;
    if (isArray(obj)) {
        returnContext = [];
        obj.forEach((item: any) => {
            (returnContext as Array<any>).push(instanceOfObj(item, excludes));
        });
        return returnContext;
    } else if (isObject(obj)) {
        returnContext = {};
        let data: any;
        for (let k in obj) {
            if (excludes && excludes.includes(k)) {
                data = obj[k];
            } else {
                data = instanceOfObj(obj[k], excludes);
            }

            returnContext[k] = data;
        }
        return returnContext;
    } else {
        return obj;
    }
}
// 日期格式化
export function parseTime(time: any, pattern?: string) {
    if (arguments.length === 0 || !time) {
        return null;
    }
    const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
    let date;
    if (typeof time === 'object') {
        date = time;
    } else {
        if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
            time = parseInt(time);
        } else if (typeof time === 'string') {
            time = time
                .replace(new RegExp(/-/gm), '/')
                .replace('T', ' ')
                .replace(new RegExp(/\.[\d]{3}/gm), '');
        }
        if (typeof time === 'number' && time.toString().length === 10) {
            time = time * 1000;
        }
        date = new Date(time);
    }
    const formatObj: Record<string, any> = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay(),
    };
    const timeStr = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key];
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') {
            return ['日', '一', '二', '三', '四', '五', '六'][value];
        }
        if (result.length > 0 && value < 10) {
            value = '0' + value;
        }
        return value || 0;
    });
    return timeStr;
}

// 表单重置
export function resetForm(refName: string) {
    if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
    }
}

// 查询添加前缀
export function queryFormat(params: Record<string, any>) {
    const ret: Record<string, any> = {};
    Object.keys(params).forEach(key => {
        if (key.startsWith('no_query_')) {
            ret[key.split('no_query_')[1]] = params[key];
        } else if (key.startsWith('q_') || key === 'pageSize' || key === 'pageNum' || key === 'sort') {
            ret[key] = params[key];
        } else {
            ret['q_' + key] = params[key];
        }
    });
    return ret;
}

// 添加日期范围
export function addDateRange(params: any, dateRange: any[], propName?: string) {
    const search = queryFormat(params);
    dateRange = Array.isArray(dateRange) ? dateRange : [];
    if (dateRange.length === 2) {
        if (!dateRange[0] || !dateRange[1]) {
            return search;
        }
        if (typeof propName === 'undefined') {
            propName = 'createTime';
        }
        const key = 'q_between_' + propName;
        search[key] = dateRange[0] + ' 00:00:00' + ',' + dateRange[1] + ' 23:59:59';
    }

    return search;
}

// 回显数据字典
export function selectDictLabel(datas: any, value: any) {
    if (value === undefined) {
        return '';
    }
    const actions = [];
    Object.keys(datas).some(key => {
        if (datas[key].value === '' + value) {
            actions.push(datas[key].label);
            return true;
        }
    });
    if (actions.length === 0) {
        actions.push(value);
    }
    return actions.join('');
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas: any, value: any, separator: any) {
    if (value === undefined || value.length === 0) {
        return '';
    }
    if (Array.isArray(value)) {
        value = value.join(',');
    }
    const actions: any[] = [];
    const currentSeparator = undefined === separator ? ',' : separator;
    const temp = value.split(currentSeparator);
    Object.keys(value.split(currentSeparator)).some(val => {
        let match = false;
        Object.keys(datas).some(key => {
            if (datas[key].value === '' + temp[val]) {
                actions.push(datas[key].label + currentSeparator);
                match = true;
            }
        });
        if (!match) {
            actions.push(temp[val] + currentSeparator);
        }
    });
    return actions.join('').substring(0, actions.join('').length - 1);
}

// 字符串格式化(%s )
export function sprintf(str: string) {
    // eslint-disable-next-line prefer-rest-params
    const args = arguments;
    let flag = true,
        i = 1;
    str = str.replace(/%s/g, function () {
        const arg = args[i++];
        if (typeof arg === 'undefined') {
            flag = false;
            return '';
        }
        return arg;
    });
    return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str: string) {
    if (!str || str === 'undefined' || str === 'null') {
        return '';
    }
    return str;
}

// // 数据合并
// export function mergeRecursive(source, target) {
//     for (var p in target) {
//         try {
//             if (target[p].constructor == Object) {
//                 source[p] = mergeRecursive(source[p], target[p]);
//             } else {
//                 source[p] = target[p];
//             }
//         } catch (e) {
//             source[p] = target[p];
//         }
//     }
//     return source;
// }

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data: any, id: any, parentId?: any, children?: any) {
    const config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: children || 'children',
    };

    const childrenListMap: any = {};
    const nodeIds: any = {};
    const tree = [];

    for (const d of data) {
        const parentId = d[config.parentId];
        if (childrenListMap[parentId] == null) {
            childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
    }

    for (const d of data) {
        const parentId = d[config.parentId];
        if (nodeIds[parentId] == null) {
            tree.push(d);
        }
    }

    for (const t of tree) {
        adaptToChildrenList(t);
    }

    function adaptToChildrenList(o: any) {
        if (childrenListMap[o[config.id]] !== null) {
            o[config.childrenList] = childrenListMap[o[config.id]];
        }
        if (o[config.childrenList]) {
            for (const c of o[config.childrenList]) {
                adaptToChildrenList(c);
            }
        }
    }
    return tree;
}

/**
 * 根据时间戳转化为时间 例如 2020-01-01 01:01:01
 */
export function timestampToTime(timestamp: any) {
    const date = new Date(timestamp);
    const Y = date.getFullYear() + '-';
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    const D = add0(date.getDate()) + ' ';
    const h = add0(date.getHours()) + ':';
    const m = add0(date.getMinutes()) + ':';
    const s = add0(date.getSeconds());
    function add0(m: any) {
        return m < 10 ? '0' + m : m;
    }
    return Y + M + D + h + m + s;
}
/**
 * 获取时间差 小时分钟秒 参数是startTime和endTime 时间戳 例如1:23:10 分钟和秒不足两位数补0 不算天数
 *
 * */
export function getTimeDifference(startTime: any, endTime: any) {
    const date = endTime - startTime;
    const hours = Math.floor(date / (3600 * 1000));
    const leave1 = date % (3600 * 1000);
    const minutes = Math.floor(leave1 / (60 * 1000));
    const leave2 = leave1 % (60 * 1000);
    const seconds = Math.floor(leave2 / 1000);
    return (
        (hours > 0 ? hours + ':' : '') +
        (minutes < 10 ? '0' + minutes : minutes) +
        ':' +
        (seconds < 10 ? '0' + seconds : seconds)
    );
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params: any) {
    let result = '';
    for (const propName of Object.keys(params)) {
        const value = params[propName];
        const part = encodeURIComponent(propName) + '=';
        if (value !== null && value !== '' && typeof value !== 'undefined') {
            if (typeof value === 'object') {
                for (const key of Object.keys(value)) {
                    if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
                        const params = propName + '[' + key + ']';
                        const subPart = encodeURIComponent(params) + '=';
                        result += subPart + encodeURIComponent(value[key]) + '&';
                    }
                }
            } else {
                result += part + encodeURIComponent(value) + '&';
            }
        }
    }
    return result;
}

// 返回项目路径
export function getNormalPath(p: any) {
    if (p.length === 0 || !p || p === 'undefined') {
        return p;
    }
    const res = p.replace('//', '/');
    if (res[res.length - 1] === '/') {
        return res.slice(0, res.length - 1);
    }
    return res;
}

// 验证是否为blob格式
export async function blobValidate(data: any) {
    try {
        const text = await data.text();
        JSON.parse(text);
        return false;
    } catch (error) {
        return true;
    }
}
