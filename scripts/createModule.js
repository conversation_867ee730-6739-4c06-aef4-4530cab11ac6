import path, { dirname } from 'path';
import { createPage } from './template/module.js';
import fs from 'fs';
import { fileURLToPath } from 'url';
import prettier from 'prettier';
import { createApi } from './template/createApi.js';
const options = {
    semi: true,
    singleQuote: true,
    tabWidth: 2,
    trailingComma: 'none',
    printWidth: 80,
    bracketSpacing: true,
    parser: 'typescript'
};

export function createModule(genTable, isWrite = true) {
    return new Promise((resolve, reject) => {
        genTable.columns.map(item => (item.queryType = item.queryType.toUpperCase()));
        let __dirname = dirname(fileURLToPath(import.meta.url));
        let result = createPage(genTable);
        let result1 = createApi(genTable);
        let reallyBasePath = path.resolve(__dirname, '../src');
        fs.writeFileSync('./scripts/a.tsx', result, 'utf-8');
        result = prettier
            .format(result, options)
            .then(res => {
                let modulePath = `./views/${genTable.moduleName}/${genTable.businessName}/index.tsx`;
                let apiPath = `./api/${genTable.moduleName}/${genTable.businessName}.ts`;
                if (!isWrite) {
                    modulePath = `../scripts/modules/module.tsx`;
                    apiPath = `../scripts/modules/api.ts`;
                } else {
                    modulePath = path.resolve(__dirname, '../src', modulePath);
                    apiPath = path.resolve(__dirname, '../src', apiPath);
                    let dirModelPath = path.resolve(__dirname, '../src', `./views/${genTable.moduleName}`);
                    let dirApiPath = path.resolve(__dirname, '../src', `./api/${genTable.moduleName}`);
                    let dirModelPathChild = path.resolve(
                        __dirname,
                        '../src',
                        `./views/${genTable.moduleName}/${genTable.businessName}`
                    );
                    if (!fs.existsSync(dirApiPath)) fs.mkdirSync(dirApiPath);
                    if (!fs.existsSync(dirModelPath)) fs.mkdirSync(dirModelPath);
                    if (!fs.existsSync(dirModelPathChild)) fs.mkdirSync(dirModelPathChild);
                    if (fs.existsSync(modulePath) || fs.existsSync(apiPath)) {
                        reject('文件已经存在,生成失败');
                        return;
                    }
                    fs.writeFileSync(modulePath, res, 'utf-8');
                    fs.writeFileSync(apiPath, result1, 'utf-8');
                    console.log(`已生成文件 路径为${modulePath}`);
                    console.log(`已生成文件 路径为${apiPath}`);
                }
                resolve({
                    module: res,
                    api: result1
                });
            })
            .catch(e => {
                reject();
                console.log(e);
            });
    });
}
