import request from '@/utils/request';

// 查询RabbitMQ配置列表
export function listDmsRabbitmq(query: any) {
    return request({
        url: '/evms/dms_rabbitmq/list',
        method: 'get',
        params: query,
    });
}

// 查询RabbitMQ配置详细
export function getDmsRabbitmq(id: any) {
    return request({
        url: '/evms/dms_rabbitmq/' + id,
        method: 'get',
    });
}

// 新增RabbitMQ配置
export function addDmsRabbitmq(data: any) {
    return request({
        url: '/evms/dms_rabbitmq',
        method: 'post',
        data: data,
    });
}

// 修改RabbitMQ配置
export function updateDmsRabbitmq(data: any) {
    return request({
        url: '/evms/dms_rabbitmq',
        method: 'put',
        data: data,
    });
}

// 删除RabbitMQ配置
export function delDmsRabbitmq(id: any) {
    return request({
        url: '/evms/dms_rabbitmq/' + id,
        method: 'delete',
    });
}
