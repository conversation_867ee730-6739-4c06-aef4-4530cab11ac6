export function getModelData(): any {
    return {
        article: {
            mains: [
                { code: 'title', must: true, show: true, double: false, required: true },
                { code: 'subtitle', must: false, show: false, double: false, required: false },
                { code: 'fullTitle', must: false, show: false, double: false, required: false },
                { code: 'linkUrl', must: false, show: true, double: false, required: false },
                { code: 'tags', must: false, show: false, double: false, required: false },
                { code: 'seoKeywords', must: false, show: false, double: false, required: false },
                { code: 'seoDescription', must: false, show: true, double: false, required: false },
                { code: 'author', must: false, show: false, double: true, required: false },
                { code: 'editor', must: false, show: false, double: true, required: false },
                {
                    code: 'image',
                    must: false,
                    show: true,
                    double: false,
                    required: false,
                    type: 'image',
                    imageWidth: 300,
                    imageHeight: 200,
                    imageMode: 'manual'
                },
                { code: 'file', must: false, show: false, double: false, required: false },
                { code: 'video', must: false, show: false, double: false, required: false },
                { code: 'audio', must: false, show: false, double: false, required: false },
                { code: 'doc', must: false, show: false, double: false, required: false },
                {
                    code: 'imageList',
                    must: false,
                    show: false,
                    double: false,
                    required: false,
                    type: 'imageList',
                    imageListType: 'pictureCard',
                    imageMaxWidth: 1920,
                    imageMaxHeight: 1920
                },
                { code: 'fileList', must: false, show: false, double: false, required: false },
                {
                    code: 'text',
                    must: false,
                    show: true,
                    double: false,
                    required: true,
                    type: 'editor',
                    editorType: 1,
                    editorSwitch: true
                },
            ],
            asides: [
                { code: 'channel', must: true, show: true, required: true },
                { code: 'org', must: false, show: true, required: true },
                { code: 'publishDate', must: true, show: true, required: true },
                // { code: 'offlineDate', must: false, show: true, required: false },
                { code: 'source', must: false, show: true, required: false },
                { code: 'articleTemplate', must: false, show: true, required: false },
                { code: 'allowComment', must: false, show: true, required: true },
                { code: 'user', must: false, show: false, required: true },
                { code: 'created', must: false, show: false, required: true },
                { code: 'modifiedUser', must: false, show: false, required: false },
                { code: 'modified', must: false, show: false, required: false }
            ],
        },
        channel: {
            mains: [
                { code: 'name', must: true, show: true, double: true, required: true },
                { code: 'alias', must: true, show: true, double: true, required: true },
                { code: 'linkUrl', must: true, show: true, double: false, required: true },
                { code: 'seoTitle', must: false, show: true, double: true, required: false },
                { code: 'seoKeywords', must: false, show: true, double: true, required: false },
                { code: 'seoDescription', must: false, show: true, double: false, required: false },
                {
                    code: 'image',
                    must: false,
                    show: false,
                    double: false,
                    required: false,
                    type: 'image',
                    imageWidth: 300,
                    imageHeight: 200,
                    imageMode: 'manual'
                },
                { code: 'channelModel', must: true, show: true, double: true, required: true },
                { code: 'articleModel', must: true, show: true, double: true, required: true },
                { code: 'channelTemplate', must: false, show: true, double: true, required: true },
                { code: 'articleTemplate', must: false, show: true, double: true, required: true },
                { code: 'nav', must: false, show: true, double: true, required: true },
                { code: 'allowComment', must: false, show: true, double: true, required: true },
                { code: 'allowContribute', must: false, show: true, double: true, required: true },
                { code: 'allowSearch', must: false, show: true, double: true, required: true },
                { code: 'text', must: false, show: false, double: false, required: false }
            ],
            asides: [
                { code: 'parent', must: true, show: true, required: false },
                { code: 'type', must: true, show: true, required: true },
                { code: 'processKey', must: false, show: true, required: false },
                { code: 'pageSize', must: true, show: true, required: true }
            ],
        },
    };
}

export function mergeModelFields(defaultFields: any[], str: string | null | undefined, type: string): any[] {
    const fields = JSON.parse(str || '[]');
    const defaults = defaultFields.map((item: any) => ({ ...item, label: `${type}.${item.code}` }));
    // 去除默认字段中不存在的字段
    fields.filter((field: any) => defaults.findIndex(item => item.code === field.code) !== -1);
    defaults.forEach(item => {
        const index = fields.findIndex((it: any) => it.code === item.code);
        if (index !== -1) {
            // 加上缺失属性，覆盖不可改属性
            fields[index] = {
                ...item,
                ...fields[index],
                must: item.must,
                label: item.label,
                type: item.type
            };
        } else {
            // 加上没有的字段
            fields.push(item);
        }
    });
    return fields;
}
