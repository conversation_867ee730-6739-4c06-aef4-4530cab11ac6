import { DeviceUserPermission } from '@/model/dms_device';

export interface DmsUserInfo {
    id: number;
    username: string;
    realname: string;
    title: null | string;
    company: null | string;
    department: null | string;
    identity: number;
    statusForPatient: null;
    gender: Gender;
    role: number;
    auth: string;
    type: number;
    userid: number;
    nickname: string;
    videoExportPermission: null;
    permission: Partial<Permission>;
}

export enum Gender {
    M = 'M',
}

export interface Permission {
    elab: boolean;
    ebs: boolean;
    outpatient: boolean;
    videoExportPermission: boolean;
}

// 用户拥有设备
// Generated by https://quicktype.io

export interface UserDevice {
    id: number;
    nativeId: string;
    name: string;
    type: number;
    description: string;
    nvrIp: string;
    nvrPort: number;
    nvrPwd: string;
    permissionJson: string;
    permission?: DeviceUserPermission;
}
