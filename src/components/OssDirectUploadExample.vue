<template>
    <div class="oss-upload-examples">
        <el-card class="example-card">
            <template #header>
                <div class="card-header">
                    <span>阿里云OSS直接上传示例</span>
                </div>
            </template>

            <!-- 基础上传示例 -->
            <div class="example-section">
                <h3>基础文件上传</h3>
                <p>支持多种文件格式，直接上传到阿里云OSS</p>

                <OssDirectUpload
                    v-model="basicFiles"
                    :max-files="5"
                    :max-size="10 * 1024 * 1024"
                    :multiple="true"
                    :upload-options="{ folder: 'basic-uploads' }"
                    accept="image/*,video/*,application/pdf"
                    @upload-success="handleUploadSuccess"
                    @upload-error="handleUploadError"
                />
            </div>

            <el-divider />

            <!-- 图片上传示例 -->
            <div class="example-section">
                <h3>图片上传</h3>
                <p>只允许上传图片文件</p>

                <OssDirectUpload
                    v-model="imageFiles"
                    :max-files="3"
                    :max-size="5 * 1024 * 1024"
                    :multiple="true"
                    :upload-options="{ folder: 'images' }"
                    accept="image/*"
                    accept-text="支持 JPG、PNG、GIF 等图片格式"
                    drag-text="拖拽图片到此处上传"
                />

                <!-- 图片预览 -->
                <div v-if="imageFiles.length > 0" class="image-preview">
                    <h4>图片预览：</h4>
                    <div class="image-grid">
                        <div
                            v-for="(file, index) in imageFiles"
                            :key="index"
                            class="image-item"
                        >
                            <img v-if="file.url" :alt="file.name" :src="file.url" />
                            <p>{{ file.name }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <el-divider />

            <!-- 单文件上传示例 -->
            <div class="example-section">
                <h3>单文件上传</h3>
                <p>只允许上传一个文件</p>

                <OssDirectUpload
                    v-model="singleFile"
                    :max-size="20 * 1024 * 1024"
                    :multiple="false"
                    :upload-options="{ folder: 'documents' }"
                    accept="application/pdf"
                    accept-text="仅支持 PDF 格式文档"
                    drag-text="拖拽PDF文件到此处上传"
                />
            </div>

            <el-divider />

            <!-- 手动上传示例 -->
            <div class="example-section">
                <h3>手动上传</h3>
                <p>选择文件后手动触发上传</p>

                <OssDirectUpload
                    ref="manualUploadRef"
                    v-model="manualFiles"
                    :auto-upload="false"
                    :multiple="true"
                    :upload-options="{ folder: 'manual-uploads' }"
                    accept="image/*,video/*"
                    drag-text="选择文件后点击上传按钮"
                />

                <div class="manual-actions">
                    <el-button type="primary" @click="handleManualUpload">
                        开始上传
                    </el-button>
                    <el-button @click="clearFiles">
                        清空文件
                    </el-button>
                </div>
            </div>

            <el-divider />

            <!-- 配置说明 -->
            <div class="example-section">
                <h3>配置说明</h3>
                <el-alert
                    :closable="false"
                    show-icon
                    title="配置说明"
                    type="info"
                >
                    <p><strong>推荐方式：</strong>通过后台接口获取STS临时凭证（更安全）</p>
                    <p>后台需要提供接口：<code>GET /api/oss/sts-token</code></p>
                    <p>返回格式：</p>
                    <pre class="env-config">
{
  "code": 200,
  "data": {
    "accessKeyId": "STS.xxx",
    "accessKeySecret": "xxx",
    "securityToken": "xxx",
    "bucketName": "your-bucket",
    "region": "oss-cn-shanghai"
  }
}
          </pre>
                    <p>
                        详细配置说明请参考项目根目录的
                        <el-link type="primary">OSS_UPLOAD_GUIDE.md</el-link>
                    </p>
                </el-alert>
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import OssDirectUpload from './OssDirectUpload.vue';

// 响应式数据
const basicFiles = ref([]);
const imageFiles = ref([]);
const singleFile = ref([]);
const manualFiles = ref([]);

// 手动上传组件引用
const manualUploadRef = ref();

// 事件处理函数
const handleUploadSuccess = (files: any[]) => {
    console.log('上传成功:', files);
    ElMessage.success(`成功上传 ${files.length} 个文件`);
};

const handleUploadError = (error: string) => {
    console.error('上传失败:', error);
    ElMessage.error('上传失败: ' + error);
};

const handleManualUpload = () => {
    manualUploadRef.value?.upload();
};

const clearFiles = () => {
    manualFiles.value = [];
};
</script>

<style scoped>
.oss-upload-examples {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.example-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
}

.example-section {
    margin-bottom: 30px;
}

.example-section h3 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.example-section p {
    margin: 0 0 15px 0;
    color: #606266;
    font-size: 14px;
}

.image-preview {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 6px;
}

.image-preview h4 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.image-item {
    text-align: center;
}

.image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.image-item p {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #6b7280;
    word-break: break-all;
}

.manual-actions {
    margin-top: 15px;
    display: flex;
    gap: 12px;
}

.env-config {
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    margin: 10px 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

:deep(.el-alert__content) {
    line-height: 1.6;
}

:deep(.el-alert code) {
    background-color: #f5f7fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}
</style>
