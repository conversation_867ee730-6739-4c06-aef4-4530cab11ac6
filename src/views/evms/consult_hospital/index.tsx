import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn,
    ElProgress,
    ElCard
} from 'element-plus';
import { defineComponent, getCurrentInstance, ref, reactive, toRefs, onMounted, watch } from 'vue';
import RightToolar from '@/components/RightToolbar/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import DictTag from '@/components/DictTag/index.vue';
import FileUpload from '@/components/FileUpload/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import {
    listConsultHospital,
    getConsultHospital,
    delConsultHospital,
    addConsultHospital,
    updateConsultHospital,
    EvmsConsultHospitalExpert,
    DeviceVos
} from '@/api/evms/consult_hospital';
import moment from 'moment';
import { RefsKes } from '@/type/common';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import '@/views/evms/expert/css/index.css';
import { getStatisticsData, StatisticsData } from '@/api/evms/expert';
import BindingDoc from './model/bindingDoc';
import ContractManage from './model/contractManage';
import ConsultOrder from '@/views/evms/ordert/model/consultOrder';

interface QueryParams extends Partial<EvmsConsultHospitalExpert> {
    pageNum: number;
    pageSize: number;
}
export default defineComponent({
    emits: ['selected'],
    props: {
        pageType: {
            type: String,
            required: false,
            default: ''
        }
    },
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;
        const state = reactive({
            consult_hospitalList: [] as EvmsConsultHospitalExpert[],
            loading: true,
            showSearch: true,
            ids: [] as Array<string | number>,
            single: true,
            total: 0,
            multiple: false,
            daterange: [] as string[],
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                hospitalName: '',
                signingStatus: '' as any
            } as QueryParams,
            rules: {
                hospitalId: [{ required: true, message: '医院id不能为空', trigger: 'blur' }],
                expertId: [{ required: true, message: '专家id不能为空', trigger: 'blur' }],
                createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }] as any[]
            },
            statistics: {
                dyingPeriodCount: 1,
                mocurrentMonth: {
                    serviceCount: 1,
                    signingCount: 1
                },
                lastMonth: {
                    serviceCount: 1,
                    signingCount: 0
                },
                signingCount: 80,
                unsignedCount: 90
            } as StatisticsData
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function getList() {
            state.loading = true;
            if (state.daterange.length === 2) {
                state.queryParams.signingStartTimeStamp = moment(state.daterange[0]).valueOf();
                state.queryParams.signingEndTimeStamp = moment(state.daterange[1]).valueOf();
            }
            listConsultHospital(state.queryParams).then(response => {
                response.data.list.map(item => {
                    item.hasChildren = true;
                    if (item.deviceVos && item.deviceVos.length <= 0) {
                        item.hasChildren = false;
                    }
                });
                state.consult_hospitalList = response.data.list;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function statisticsData() {
            getStatisticsData({
                type: 'HOSPITAL'
            }).then(res => {
                state.statistics = res.data;
            });
        }

        statisticsData();

        function handleQuery() {
            state.queryParams.pageNum = 1;
            instance.refs.deviceRef && instance.refs.deviceRef.setCurrentRow(null);
            getList();
        }

        watch(
            () => state.daterange,
            val => {
                if (!val) {
                    state.daterange = [];
                    state.queryParams.signingEndTimeStamp = null;
                    state.queryParams.signingStartTimeStamp = null;
                }
            }
        );

        function resetQuery() {
            state.daterange = [];
            state.queryParams.signingEndTimeStamp = null;
            state.queryParams.signingStartTimeStamp = null;
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleAdd() {
        }

        function handleUpdate(record: EvmsConsultHospitalExpert) {
            instance.refs.BindingDocRef.show(record);
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        function handleContract(record: DeviceVos) {
            instance.refs.ContractManageRef.show(record);
        }

        function onCurrentChange(currentRow: DeviceVos, oldCurrentRow: DeviceVos) {
            emit('selected', currentRow, oldCurrentRow);
        }

        function handleConsultOrder(record: DeviceVos) {
            instance.refs.ConsultOrderRef.add(record);
        }

        expose({ handleQuery });

        const slots = {
            column: {
                roomsNumber({ row: record }: { row: EvmsConsultHospitalExpert }) {
                    return <p style="color:#0581ce;">{(record.deviceVos && record.deviceVos.length) || 0}</p>;
                },
                signingStartTimeStamp({ row: record }: { row: DeviceVos }) {
                    return (
                        <span>
                            {record.signingStartTimeStamp
                                ? moment(record.signingStartTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingEndTimeStamp({ row: record }: { row: DeviceVos | EvmsConsultHospitalExpert }) {
                    return (
                        <span>
                            {record.signingEndTimeStamp
                                ? moment(record.signingEndTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                }
            },
            tableChild({ row: record }: { row: EvmsConsultHospitalExpert }) {
                if (!record.deviceVos || record.deviceVos.length <= 0) {
                    return null;
                }
                return (
                    <ElTable
                        row-key="deviceId"
                        ref="deviceRef"
                        data={record.deviceVos}
                        highlightCurrentRow={props.pageType === 'select' ? true : false}
                        onCurrent-change={onCurrentChange}
                        headerCellStyle={{ backgroundColor: 'rgba(0,0,0,20%) !important' }}
                        rowStyle={{ backgroundColor: 'rgba(0,0,0,10%)' }}
                    >
                        <ElTableColumn label="导管室名称" align="center" prop="deviceName" />
                        <ElTableColumn label="本周期次数" align="center" prop="cycleCount" />
                        <ElTableColumn label="总申请会诊次数" align="center" prop="totalCount" />
                        <ElTableColumn label="本周期剩余次数" align="center" prop="cycleSurplusCount" />
                        <ElTableColumn
                            label="签约时间"
                            align="center"
                            v-slots={slots.column.signingStartTimeStamp}
                        />
                        <ElTableColumn
                            label="到期时间"
                            align="center"
                            v-slots={slots.column.signingEndTimeStamp}
                        />
                        {props.pageType === 'select' ? (
                            ''
                        ) : (
                            <ElTableColumn
                                label="操作"
                                align="center"
                                class-name="small-padding fixed-width"
                                v-slots={slots.actionSlotsChild}
                            ></ElTableColumn>
                        )}
                    </ElTable>
                );
            },
            actionSlots({ row: record }: { row: EvmsConsultHospitalExpert }) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:consult_hospital:edit']]}
                            link
                            type="primary"
                            onClick={() => handleUpdate(record)}
                        >
                            绑定专家
                        </ElButton>
                    </>
                );
            },
            actionSlotsChild({ row: record }: { row: DeviceVos }) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:consult_hospital:edit']]}
                            link
                            type="primary"
                            onClick={() => handleContract(record)}
                        >
                            签约管理
                        </ElButton>
                        <ElButton
                            v-hasPermi={[['sys:test:add']]}
                            link
                            type="primary"
                            onClick={() => handleConsultOrder(record)}
                        >
                            申请会诊
                        </ElButton>
                    </>
                );
            }
        };

        function getRowClass({ row }: { row: EvmsConsultHospitalExpert }) {
            if (row.deviceVos?.length === 0) {
                return 'row-expand-cover';
            }
            return '';
        }

        getList();
        return () => (
            <div class="app-container">
                {state.statistics && props.pageType !== 'select' ? (
                    <ElCard class="progress-card">
                        <div class="progress-div">
                            <ElProgress
                                color="red"
                                type="circle"
                                percentage={
                                    state.statistics.lastMonth.serviceCount > 0
                                        ? parseInt(
                                            (state.statistics.mocurrentMonth.serviceCount /
                                                state.statistics.lastMonth.serviceCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">本月服务次数/上月服务次数</p>
                                <p class="progress-text2">
                                    {state.statistics.mocurrentMonth.serviceCount}/
                                    {state.statistics.lastMonth.serviceCount}
                                </p>
                            </div>
                        </div>
                        <div class="progress-div">
                            <ElProgress
                                color="blue"
                                type="circle"
                                percentage={
                                    state.statistics.lastMonth.signingCount > 0
                                        ? parseInt(
                                            (state.statistics.mocurrentMonth.signingCount /
                                                state.statistics.lastMonth.signingCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">本月签约医院/上月签约医院</p>
                                <p class="progress-text2">
                                    {state.statistics.mocurrentMonth.signingCount}/
                                    {state.statistics.lastMonth.signingCount}
                                </p>
                            </div>
                        </div>
                        <div class="progress-div">
                            <ElProgress
                                color="purple"
                                type="circle"
                                percentage={
                                    state.statistics.signingCount > 0
                                        ? parseInt(
                                            (state.statistics.dyingPeriodCount /
                                                state.statistics.signingCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">临期/已签约</p>
                                <p class="progress-text2">
                                    {state.statistics.dyingPeriodCount}/{state.statistics.signingCount}
                                </p>
                            </div>
                        </div>
                        <div class="progress-div">
                            <ElProgress
                                color="green"
                                type="circle"
                                percentage={
                                    state.statistics.unsignedCount > 0
                                        ? parseInt(
                                            (state.statistics.signingCount /
                                                state.statistics.unsignedCount) *
                                            100 +
                                            ''
                                        )
                                        : 0
                                }
                            ></ElProgress>
                            <div class="progress-text-div">
                                <p class="progress-text1">已签约/未签约</p>
                                <p class="progress-text2">
                                    {state.statistics.signingCount}/{state.statistics.unsignedCount}
                                </p>
                            </div>
                        </div>
                    </ElCard>
                ) : (
                    ''
                )}
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="医院名称" prop="hospitalName">
                        <ElInput
                            v-model={state.queryParams.hospitalName}
                            placeholder="请输入医院名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="合同状态" prop="signingStatus">
                        <ElSelect
                            v-model={state.queryParams.signingStatus}
                            placeholder="请选择合同状态"
                            clearable
                        >
                            <ElOption key={0} label="全部" value={''}></ElOption>
                            <ElOption key={1} label="未签约" value={false}></ElOption>
                            <ElOption key={2} label="已签约" value={true}></ElOption>
                        </ElSelect>
                    </ElFormItem>
                    <ElFormItem label="签约起止时间" prop="signingStartTime">
                        <ElDatePicker
                            v-model={state.daterange}
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                        />
                    </ElFormItem>
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}

                <ElRow gutter={10} class="mb8">
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable
                    row-key="hospitalId"
                    v-loading={state.loading}
                    data={state.consult_hospitalList}
                    tree-props={{ children: 'deviceVos', hasChildren: 'hasChildren' }}
                    rowClassName={getRowClass}
                >
                    <ElTableColumn prop="deviceVos" type="expand" v-slots={slots.tableChild} />
                    {/* <ElTableColumn label="创建时间" align="center" prop="hospitalName" /> */}
                    <ElTableColumn label="医院名称" align="center" prop="hospitalName" />
                    <ElTableColumn
                        label="导管室数量"
                        align="center"
                        v-slots={slots.column.roomsNumber}
                    ></ElTableColumn>
                    <ElTableColumn label="总申请会诊次数" align="center" prop="totalCount" />
                    <ElTableColumn label="总剩余次数" align="center" prop="totalSurplusCount" />
                    <ElTableColumn label="绑定专家数" align="center" prop="expertCount" />
                    <ElTableColumn
                        label="最后到期时间"
                        align="center"
                        v-slots={slots.column.signingEndTimeStamp}
                    />

                    {props.pageType === 'select' ? (
                        ''
                    ) : (
                        <ElTableColumn
                            label="操作"
                            align="center"
                            class-name="small-padding fixed-width"
                            v-slots={slots.actionSlots}
                        ></ElTableColumn>
                    )}
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
                <BindingDoc onOk={() => handleQuery()} ref="BindingDocRef"></BindingDoc>
                <ContractManage onOk={() => handleQuery()} ref="ContractManageRef"></ContractManage>
                <ConsultOrder onOk={handleQuery} ref="ConsultOrderRef"></ConsultOrder>
            </div>
        );
    },
});
