import { getCurrentInstance, Ref } from 'vue';

let tableSort = null,
    currentInstance: any = null;

function getParent(parentInstance: any, currentInstance: any) {
    if (!parentInstance) return false;
    while (currentInstance.parent) {
        if (currentInstance.parent === parentInstance) {
            return true;
        }
        currentInstance = currentInstance.parent;
    }
    return false;
}

export default {
    created(this: any) {
        let instance = getCurrentInstance()!;
        if (instance && (instance as any).ctx.getInstanceTableParams) {
            this.instance = instance;
            tableSort = this.tableSort;
            currentInstance = instance;
        }
    },
    mounted() {
        let instance = getCurrentInstance()!;
        if (
            instance.type.name === 'ElTableColumn' &&
            instance.props.prop &&
            (instance.props.prop as string).indexOf('.') === -1
        ) {
            if (getParent(currentInstance, instance)) {
                instance.props['sortable'] = 'custom';
            }
        }
    },
    data() {
        return {
            instance: null,
            queryParams: null,
            handleQuery: null
        };
    },
    updated(this: any) {
        let instance = getCurrentInstance()!;
        if (instance && (instance as any).ctx.getInstanceTableParams) {
            this.instance = instance;
            tableSort = this.tableSort;
            currentInstance = instance;
        }
        if (
            instance.type.name === 'ElTable' &&
            instance.vnode.props &&
            getParent(currentInstance, instance)
        ) {
            instance.vnode.props['onSortChange'] = tableSort!;
        }
    },
    methods: {
        tableSort(this: any, column: { prop: string; order: 'ascending' | 'descending' }) {
            let prop = column.prop;
            let order = column.order;
            if (this.instance && this.instance.ctx && this.instance.ctx.getInstanceTableParams) {
                let [queryParams, handleQuery] = this.instance.ctx.getInstanceTableParams();
                if (order === 'ascending') {
                    queryParams.value.sort = `${prop} asc`;
                } else if (order === 'descending') {
                    queryParams.value.sort = `${prop} desc`;
                } else {
                    delete queryParams.value.sort;
                }
                handleQuery();
            }
        }
    }
};
