import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import { ElButton, ElCol, ElDialog, ElForm, ElRow, ElEmpty } from 'element-plus';
import { RefsKes } from '@/type/common';
import moment from 'moment';
import { videoList, EvmsVideoDetail, EvmsCousultOrderDetail } from '@/api/evms/ordert';
import VideoPreview from '@/components/videoPreview/index';

interface QueryParams extends Partial<EvmsVideoDetail> {
}

export default defineComponent({
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        let instance: RefsKes;

        const state = reactive({
            listData: [] as EvmsVideoDetail[],
            visible: false,
            showSearch: true,
            queryParams: {} as QueryParams,
            loading: false,
            daterange: [] as string[],
            ids: [] as Array<string | number>,
            single: true,
            multiple: false
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsK<PERSON>;
        });

        function handleOk() {
            close();
        }

        function close() {
            state.visible = false;
        }

        function show(record: EvmsCousultOrderDetail) {
            state.visible = true;
            state.listData = [];
            state.queryParams.orderNumber = record.orderNumber;
            getList();
        }

        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };

        function getList() {
            state.loading = true;
            videoList(state.queryParams).then(response => {
                state.listData = response.data.list;
                state.loading = false;
            });
        }

        const textAlign = 'text-align:center;';

        function videoShow(url: string) {
            instance.refs.VideoPreviewRef.show(url);
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="录像列表"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {state.listData.length > 0 ? (
                    <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                        <ElRow gutter={24} class="mb8">
                            <ElCol style={textAlign} span={6}>
                                订单号
                            </ElCol>
                            <ElCol style={textAlign} span={6}>
                                时长
                            </ElCol>
                            <ElCol style={textAlign} span={6}>
                                视频大小
                            </ElCol>
                            <ElCol style={textAlign} span={6}>
                                操作
                            </ElCol>
                        </ElRow>

                        {state.listData.map(item => {
                            return (
                                <ElRow gutter={24} class="mb8">
                                    <ElCol style={textAlign} span={6}>
                                        {item.orderNumber}
                                    </ElCol>
                                    <ElCol style={textAlign} span={6}>
                                        {item.duration}
                                    </ElCol>
                                    <ElCol style={textAlign} span={6}>
                                        {item.size}
                                    </ElCol>
                                    <ElCol style={textAlign} span={6}>
                                        <ElButton
                                            link
                                            type="primary"
                                            onClick={() => videoShow(item.videoUrl)}
                                        >
                                            查看视频
                                        </ElButton>
                                        <ElButton link type="primary">
                                            <a href={item.videoUrl} target="_blank">
                                                下载
                                            </a>
                                        </ElButton>
                                    </ElCol>
                                </ElRow>
                            );
                        })}
                    </ElForm>
                ) : (
                    <ElEmpty description="暂无录像"></ElEmpty>
                )}
                <VideoPreview ref="VideoPreviewRef"></VideoPreview>
            </ElDialog>
        );
    }
});
