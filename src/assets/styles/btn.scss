@import './variables.module.scss';

@mixin colorBtn($color) {
    background: $color;

    &:hover {
        color: $color;

        &:before,
        &:after {
            background: $color;
        }
    }
}

.blue-btn {
    @include colorBtn($blue);
}

.light-blue-btn {
    @include colorBtn($light-blue);
}

.red-btn {
    @include colorBtn($red);
}

.pink-btn {
    @include colorBtn($pink);
}

.green-btn {
    @include colorBtn($green);
}

.tiffany-btn {
    @include colorBtn($tiffany);
}

.yellow-btn {
    @include colorBtn($yellow);
}

.pan-btn {
    font-size: 14px;
    color: #fff;
    padding: 14px 36px;
    border-radius: 8px;
    border: none;
    outline: none;
    transition: 600ms ease all;
    position: relative;
    display: inline-block;

    &:hover {
        background: #fff;

        &:before,
        &:after {
            width: 100%;
            transition: 600ms ease all;
        }
    }

    &:before,
    &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        height: 2px;
        width: 0;
        transition: 400ms ease all;
    }

    &::after {
        right: inherit;
        top: inherit;
        left: 0;
        bottom: 0;
    }
}

.custom-button {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    color: #fff;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: 0;
    margin: 0;
    padding: 10px 15px;
    font-size: 14px;
    border-radius: 4px;
}
