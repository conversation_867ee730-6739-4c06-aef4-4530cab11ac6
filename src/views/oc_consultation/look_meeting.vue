<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2025-06-20 09:40:21
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2025-06-20 09:44:01
-->
<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Rtc_meeting_info_base from '@/views/oc_consultation/components/rtc_meeting_info_base.vue';
import Rtc_meeting_info_attendee from '@/views/oc_consultation/components/rtc_meeting_info_attendee.vue';
import Rtc_meeting_info_chat from '@/views/oc_consultation/components/rtc_meeting_info_chat.vue';
import Rtc_meeting_info_record from '@/views/oc_consultation/components/rtc_meeting_info_record.vue';

const activeName = ref('base');
const route = useRoute();

const meetingId = ref(route.params.id);
</script>

<template>
    <div class="dms_device_container">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" class="demo-tabs" type="card">
                <el-tab-pane label="基础信息" name="base">
                    <rtc_meeting_info_base v-model:meeting-id="meetingId" />
                </el-tab-pane>
                <el-tab-pane :disabled="!meetingId" :lazy="true" label="参会人管理" name="member">
                    <rtc_meeting_info_attendee :meeting-id="meetingId" />
                </el-tab-pane>
                <el-tab-pane :disabled="!meetingId" :lazy="true" label="会议聊天" name="chat">
                    <rtc_meeting_info_chat :meeting-id="meetingId" />
                </el-tab-pane>
                <el-tab-pane :disabled="!meetingId" :lazy="true" label="会议录制" name="record">
                    <rtc_meeting_info_record :meeting-id="meetingId" />
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<style lang="scss" scoped>
.dms_device_container {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;

    :deep(.el-card) {
        height: 100%;
    }

    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }

    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }

    :deep(.el-tab-pane) {
        height: 100% !important;
        overflow-y: scroll;
    }

    :deep(.el-form) {
        height: 100% !important;
        overflow-y: scroll;

        &::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0px 0px 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0px 0px 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        height: 40px !important;
    }

    .form-con {
        height: 100%;
    }
}
</style>
