/* eslint-disable camelcase */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ElNotification, ElMessageBox, ElMessage, ElLoading } from 'element-plus';
import { getToken } from '@/utils/auth';
import errorCode from '@/utils/errorCode';
import { tansParams, blobValidate } from '@/utils/ruoyi';
import cache from '@/plugins/cache';
import { saveAs } from 'file-saver';
import useUserStore from '@/store/modules/user';
import { BaseApi } from '@/model/api';
import qs from 'qs';
import { isArray } from 'lodash';

let downloadLoadingInstance: ReturnType<typeof ElLoading.service>;

// 是否显示重新登录
export const isRelogin = { show: false };

// 创建axios实例
// const service = axios.create({
//     // axios中请求配置有baseURL选项，表示请求URL公共部分
//     baseURL: import.meta.env.VITE_APP_BASE_API,
//     // 超时
//     timeout: 10000,
//     headers: {
//         'Content-Type': 'application/json;charset=utf-8',
//     },
// });
class AxiosClass {
    interface: AxiosInstance;

    constructor(baseURL: string, isDoc = false) {
        this.interface = axios.create({
            baseURL: baseURL,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json;charset=utf-8',
            },
        });
        // 请求拦截器
        // 如果没有baseUrl 或者baseUrl是dev.medtion.com的话，不使用请求拦截器
        if (isDoc) {
            this.interface.interceptors.request.use(requestDocFn, requestErrFn);
        } else {
            this.interface.interceptors.request.use(requestFn, requestErrFn);
        }
        // 响应拦截器
        this.interface.interceptors.response.use(responseFn, responseErrFn);
    }
}

// request拦截器
function requestFn(config: AxiosRequestConfig) {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    if (getToken() && !isToken && config.headers) {
        config.headers['nanutoken'] = '' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
        let url = config.url + '?' + tansParams(config.params);
        url = url.slice(0, -1);
        config.params = {};
        config.url = url;
    }

    if (config.method === 'post' && config.headers!['Content-Type'] === 'application/x-www-form-urlencoded') {
        if (typeof config.data === 'object') {
            config.data = qs.stringify(config.data);
        }
    }
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
        const requestObj = {
            url: config.url,
            data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
            time: new Date().getTime(),
        };
        const sessionObj = cache.session.getJSON('sessionObj');
        if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
            cache.session.setJSON('sessionObj', requestObj);
        } else {
            const s_url = sessionObj.url; // 请求地址
            const s_data = sessionObj.data; // 请求数据
            const s_time = sessionObj.time; // 请求时间
            const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
            if (
                s_data === requestObj.data &&
                requestObj.time - s_time < interval &&
                s_url === requestObj.url
            ) {
                const message = '数据正在处理，请勿重复提交';
                console.warn(`[${s_url}]: ` + message);
                return Promise.reject(new Error(message));
            } else {
                cache.session.setJSON('sessionObj', requestObj);
            }
        }
    }
    if (config.method === 'delete') {
        const params = config.params;
        if (params) {
            for (const key in params) {
                if (isArray(params[key])) {
                    params[key] = JSON.stringify(params[key]);
                }
            }
        }
        config.params = params;
    }
    return config;
}
// 医生端请求拦截器
function requestDocFn(config: AxiosRequestConfig) {
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
        let url = config.url + '?' + tansParams(config.params);
        url = url.slice(0, -1);
        config.params = {};
        config.url = url;
    }
    if (config.method === 'post' && config.headers!['Content-Type'] === 'application/x-www-form-urlencoded') {
        if (typeof config.data === 'object') {
            config.data = qs.stringify(config.data);
        }
    }

    return config;
}
function requestErrFn(error: any) {
    console.log(error);
    return Promise.reject(error);
}

const responseFn = (res: AxiosResponse) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default'];
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
        return res.data;
    }
    if (code === 401) {
        if (!isRelogin.show) {
            isRelogin.show = true;
            ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
                confirmButtonText: '重新登录',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    isRelogin.show = false;
                    useUserStore()
                        .logOut()
                        .then(() => {
                            location.href = '/index';
                        });
                })
                .catch(() => {
                    isRelogin.show = false;
                });
        }
        return Promise.reject('无效的会话，或者会话已过期，请重新登录。');
    } else if (code === 500) {
        ElMessage({
            message: msg,
            type: 'error',
        });
        return Promise.reject(new Error(msg));
    } else if (code === 601) {
        ElMessage({
            message: msg,
            type: 'warning',
        });
        return Promise.reject(new Error(msg));
    } else if (code === 1) {
        return Promise.resolve(res.data);
    } else if (code === -3002 || code === -3003) {
        return Promise.resolve(res.data);
    } else if (code !== 200) {
        ElNotification.error({
            title: msg,
        });
        return Promise.reject('error');
    } else {
        console.log(1223);
        return Promise.resolve(res.data);
    }
};
const responseErrFn = (error: any) => {
    console.log('err' + error);
    let { message } = error;
    if (message === 'Network Error') {
        message = '后端接口连接异常';
    } else if (message.includes('timeout')) {
        message = '系统接口请求超时';
    } else if (message.includes('Request failed with status code')) {
        message = '系统接口' + message.substr(message.length - 3) + '异常';
    }
    ElMessage({
        message: message,
        type: 'error',
        duration: 5 * 1000,
    });
    return Promise.reject(error);
};

const service = new AxiosClass(import.meta.env.VITE_APP_BASE_API).interface;
// service.interceptors.request.use(
//     config => {
//         // 是否需要设置 token
//         const isToken = (config.headers || {}).isToken === false;
//         // 是否需要防止数据重复提交
//         const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
//         if (getToken() && !isToken && config.headers) {
//             config.headers['nanutoken'] = ''+getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
//         }
//         // get请求映射params参数
//         if (config.method === 'get' && config.params) {
//             let url = config.url + '?' + tansParams(config.params);
//             url = url.slice(0, -1);
//             config.params = {};
//             config.url = url;
//         }
//         if (config.method === 'post') {
//             const arr = [
//                 '/evms/dms_device/addDeviceUser',
//                 '/evms/dms_device/updateUserPermission',
//                 '/evms/dms_device/deleteUserByDeviceIdAndUserIdsStr',
//                 '/evms/dms_device/addUserDevice',
//                 '/evms/dms_device/deleteByUserIdAndDeviceIdsStr'
//             ]
//             if (arr.includes(config.url || '')) {
//                 config.headers!['Content-Type'] = 'application/x-www-form-urlencoded';
//                 config.data = qs.stringify(config.data)
//             }
//         }
//         if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
//             const requestObj = {
//                 url: config.url,
//                 data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
//                 time: new Date().getTime(),
//             };
//             const sessionObj = cache.session.getJSON('sessionObj');
//             if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
//                 cache.session.setJSON('sessionObj', requestObj);
//             } else {
//                 const s_url = sessionObj.url; // 请求地址
//                 const s_data = sessionObj.data; // 请求数据
//                 const s_time = sessionObj.time; // 请求时间
//                 const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
//                 if (
//                     s_data === requestObj.data &&
//                     requestObj.time - s_time < interval &&
//                     s_url === requestObj.url
//                 ) {
//                     const message = '数据正在处理，请勿重复提交';
//                     console.warn(`[${s_url}]: ` + message);
//                     return Promise.reject(new Error(message));
//                 } else {
//                     cache.session.setJSON('sessionObj', requestObj);
//                 }
//             }
//         }
//         return config;
//     },
//     error => {
//         console.log(error);
//         Promise.reject(error);
//     }
// );
//
// // 响应拦截器
// service.interceptors.response.use(
//     res => {
//         // 未设置状态码则默认成功状态
//         const code = res.data.code || 200;
//         // 获取错误信息
//         const msg = errorCode[code] || res.data.msg || errorCode['default'];
//         // 二进制数据则直接返回
//         if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
//             return res.data;
//         }
//         if (code === 401) {
//             if (!isRelogin.show) {
//                 isRelogin.show = true;
//                 ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
//                     confirmButtonText: '重新登录',
//                     cancelButtonText: '取消',
//                     type: 'warning',
//                 })
//                     .then(() => {
//                         isRelogin.show = false;
//                         useUserStore()
//                             .logOut()
//                             .then(() => {
//                                 location.href = '/index';
//                             });
//                     })
//                     .catch(() => {
//                         isRelogin.show = false;
//                     });
//             }
//             return Promise.reject('无效的会话，或者会话已过期，请重新登录。');
//         } else if (code === 500) {
//             ElMessage({
//                 message: msg,
//                 type: 'error',
//             });
//             return Promise.reject(new Error(msg));
//         } else if (code === 601) {
//             ElMessage({
//                 message: msg,
//                 type: 'warning',
//             });
//             return Promise.reject(new Error(msg));
//         } else if (code == 1) {
//
//             return Promise.resolve(res.data);
//         }else if (code !== 200) {
//             ElNotification.error({
//                 title: msg,
//             });
//             return Promise.reject('error');
//         } else {
//             console.log(1223)
//             return Promise.resolve(res.data);
//         }
//     },
//     error => {
//         console.log('err' + error);
//         let { message } = error;
//         if (message === 'Network Error') {
//             message = '后端接口连接异常';
//         } else if (message.includes('timeout')) {
//             message = '系统接口请求超时';
//         } else if (message.includes('Request failed with status code')) {
//             message = '系统接口' + message.substr(message.length - 3) + '异常';
//         }
//         ElMessage({
//             message: message,
//             type: 'error',
//             duration: 5 * 1000,
//         });
//         return Promise.reject(error);
//     }
// );

// 通用下载方法
export function download(url: string, params: any, filename: string, config: any = {}, transParams = true) {
    downloadLoadingInstance = ElLoading.service({
        text: '正在下载数据，请稍候',
        background: 'rgba(0, 0, 0, 0.7)',
    });
    const Nconfig = {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        responseType: 'blob',
        ...config,
    };
    if (transParams) {
        Nconfig.transformRequest = [
            (params: Record<string, any>) => {
                return tansParams(params);
            },
        ];
    }
    return service
        .post(url, params, Nconfig)
        .then(async (data: any) => {
            const isLogin = await blobValidate(data);
            if (isLogin) {
                const blob = new Blob([data]);
                saveAs(blob, filename);
            } else {
                const resText = await data.text();
                const rspObj = JSON.parse(resText);
                const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
                ElMessage.error(errMsg);
            }
            downloadLoadingInstance.close();
        })
        .catch(r => {
            console.error(r);
            ElMessage.error('下载文件出现错误，请联系管理员！');
            downloadLoadingInstance.close();
        });
}

const docService = new AxiosClass(import.meta.env.VITE_APP_BASE_URL, true).interface;
export default service;

export class Http {
    static async get<T>(url: string, params?: object, isDoc = false) {
        const serviceReq = isDoc ? docService : service;
        const res: BaseApi<T> = await serviceReq.get(url, {
            params,
            withCredentials: !isDoc,
        });
        return res;
    }

    static async post<T>(url: string, params?: object, isDoc = false, type = 'json') {
        const serviceReq = isDoc ? docService : service;
        const res: BaseApi<T> = await serviceReq.post(url, params, {
            headers: {
                'Content-Type':
                    type === 'json' ? 'application/json;charset=utf-8' : 'application/x-www-form-urlencoded',
            },
        });
        return res;
    }

    static async put<T>(url: string, params?: object, isDoc = false) {
        const serviceReq = isDoc ? docService : service;
        const res: BaseApi<T> = await serviceReq.put(url, params);
        return res;
    }

    static async delete<T>(url: string, params?: object, isDoc = false) {
        const serviceReq = isDoc ? docService : service;
        const res: BaseApi<T> = await serviceReq.delete(url, {
            params,
        });
        return res;
    }
}
