import request from '@/utils/request';
interface Response<T> {
    msg: string;
    code: number;
    data: T;
    timestamp: number;
    success: boolean;
}
interface PageResponse<T> {
    rows: Array<T>;
    total: number;
    pages: number;
}
export interface EvmsConsultExpert {
    id: number;
    doctorId: number;
    realName: string;
    signingStartTime: string;
    signingEndTime: string;
    consultationStatus: boolean;
    createBy: string;
    updateBy: string;
    createTime: string;
    updateTime: string;
    signingStatus: number;
    serviceCount: number;
    daterange: string[];
    contractUrl: string;
    expertName: string;
    sysFileId: number;
    fileOriginalName: string;
    signingStartTimeStamp: number;
    signingEndTimeStamp: number;
    startTimeStamp: number | null;
    endTimeStamp: number | null;
}

export interface EvmsChooseDoctor {
    id: number;
    userRealName: string;
    company: string;
    department: string;
    doctorName: string;
}

export interface StatisticsData {
    dyingPeriodCount: number;
    lastMonth: {
        serviceCount: number;
        signingCount: number;
    };
    mocurrentMonth: {
        serviceCount: number;
        signingCount: number;
    };
    signingCount: number;
    unsignedCount: number;
}

export interface ContractListType {
    fileName: string;
    fileOriginalName: string;
    fileSize: number;
    fileType: string;
    fileUrl: string;
    sysFileId: number;
    uploadTimeStamp: number;
}

export interface UpdateExpert {
    consultationStatus: boolean;
    doctorId: number;
    id: number;
    realName: string;
    signingEndTimeStamp: number;
    signingStartTimeStamp: number;
    sysFileId: number;
    removeSysFileId: boolean;
    // 自己加的参数
    fileOriginalName: string;
    contractUrl: string;
    daterange: any[];
}

export interface ContractDetail {
    contractAmount: number;
    contractCount: number;
    contractId: number;
    contractTotalAmount: number;
    contractUrl: string;
    deviceId: number;
    hospitalId: number;
    nativeId: string;
    signingEndTime: string;
    signingEndTimeStamp: number;
    signingStartTime: string;
    signingStartTimeStamp: number;
    sysFileId: number;
    fileOriginalName: string;
}

export interface UploadDetail {
}

// 查询会诊专家列表
export function listExpert(query: any) {
    return request({
        url: '/evms/expert/list',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<PageResponse<EvmsConsultExpert>>>;
}

// 查询会诊专家详细
export function getExpert(id: any) {
    return request({
        url: '/evms/expert/' + id,
        method: 'get',
    }) as unknown as Promise<Response<EvmsConsultExpert>>;
}

// 新增会诊专家
export function addExpert(data: EvmsConsultExpert) {
    return request({
        url: '/evms/expert/saveExpert',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<EvmsConsultExpert>>;
}

// 修改会诊专家
export function updateExpert(data: UpdateExpert) {
    return request({
        url: '/evms/expert/updateExpert',
        method: 'put',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 删除会诊专家
export function delExpert(id: any) {
    return request({
        url: '/evms/expert/' + id,
        method: 'delete',
    }) as unknown as Promise<Response<EvmsConsultExpert>>;
}

// 查询选择医生列表
export function listChooseDoctor(query: any) {
    return request({
        url: '/evms/expert/choose/doctor',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<PageResponse<EvmsChooseDoctor>>>;
}

// 添加专家
export function saveExpert(data: { doctorId: number; realName: string; consultationStatus: boolean }) {
    return request({
        url: '/evms/expert/saveExpert',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<boolean>>;
}

// 统计 type EXPERT-专家|HOSPITAL-医院
export function getStatisticsData(data: { type: string }) {
    return request({
        url: '/evms/data/consult/getData',
        method: 'get',
        params: data,
    }) as unknown as Promise<Response<StatisticsData>>;
}

// 开通|关闭 会诊
export function activateOrClose(data: { exprtId: number }) {
    return request({
        url: '/evms/expert/activateOrClose',
        method: 'get',
        params: data,
    }) as unknown as Promise<Response<string>>;
}
// 获取合同列表
export function getContractList(query: any) {
    return request({
        url: '/evms/contract/file/list',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<PageResponse<ContractListType>>>;
}

// 获取合同详情
export function getContractDetail(query: { contractId: number }) {
    return request({
        url: '/evms/contract/getContract',
        method: 'get',
        params: query,
    }) as unknown as Promise<Response<ContractDetail>>;
}
