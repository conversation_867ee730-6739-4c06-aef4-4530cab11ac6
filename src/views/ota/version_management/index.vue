<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
            <el-form-item label="平台类型" prop="platform">
                <el-select
                    v-model="queryParams.platform"
                    clearable
                    placeholder="请选择平台类型，win代表PC Widows平台，linux代表PC linux平台，mac代表苹果电脑，android代表安卓，iphone代表苹果手机，ipad代表苹果平板"
                >
                    <el-option
                        v-for="dict in evms_version_management_platform"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="版本号" prop="version">
                <el-input
                    v-model="queryParams.version"
                    clearable
                    placeholder="请输入版本号"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="应用程序类型" prop="applicationProgram">
                <el-select
                    v-model="queryParams.applicationProgram"
                    clearable
                    placeholder="请选择应用程序类型，evms_client代表桌面端，mvd_studio代表mvd设备，sserver代表流服务程序"
                >
                    <el-option
                        v-for="dict in evms_version_management_application_program"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select
                    v-model="queryParams.status"
                    clearable
                    placeholder="请选择状态，0代表未发布，1代表已发布"
                >
                    <el-option
                        v-for="dict in evms_client_version_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:version_management:add']"
                    icon="Plus"
                    plain
                    type="primary"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:version_management:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:version_management:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:version_management:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="VersionManagementList" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column align="center" label="平台类型" prop="platform">
                <template #default="scope">
                    <dict-tag :options="evms_version_management_platform" :value="scope.row.platform" />
                </template>
            </el-table-column>
            <el-table-column align="center" label="版本号" prop="version" />
            <el-table-column align="center" label="应用程序类型" prop="applicationProgram">
                <template #default="scope">
                    <dict-tag
                        :options="evms_version_management_application_program"
                        :value="scope.row.applicationProgram"
                    />
                </template>
            </el-table-column>
            <el-table-column align="center" label="版本安装包地址" prop="url" />
            <el-table-column align="center" label="状态" prop="status">
                <template #default="scope">
                    <dict-tag :options="evms_client_version_status" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['ota:version_management:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>
                    <el-button
                        v-hasPermi="['ota:version_management:remove']"
                        icon="Delete"
                        link
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改版本管理对话框 -->
        <el-dialog v-model="open" :destroy-on-close="true" :title="title" append-to-body width="800px">
            <el-form ref="VersionManagementRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="平台类型" prop="platform">
                    <el-radio-group v-model="form.platform">
                        <el-radio
                            v-for="dict in evms_version_management_platform"
                            :key="dict.value"
                            :label="dict.value"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="应用程序类型" prop="applicationProgram">
                    <el-radio-group v-model="form.applicationProgram">
                        <el-radio
                            v-for="dict in evms_version_management_application_program"
                            :key="dict.value"
                            :label="dict.value"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="版本号" prop="version">
                    <el-input v-model="form.version" placeholder="请输入版本号" />
                </el-form-item>
                <el-form-item label="版本安装包地址" prop="url">
                    <file-upload2
                        v-model="form.url"
                        :application-program="data.form.applicationProgram"
                        :platform="data.form.platform"
                        :version="data.form.version"
                    />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio
                            v-for="dict in evms_client_version_status"
                            :key="dict.value"
                            :label="parseInt(dict.value)"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="VersionManagement" setup>
import {
    addVersionManagement,
    delVersionManagement,
    getVersionManagement,
    listVersionManagement,
    updateVersionManagement
} from '@/api/ota/version_management';
import { queryFormat } from '@/utils/ruoyi';
import { ComponentInternalInstance, getCurrentInstance, reactive, ref, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
    evms_client_version_status,
    evms_version_management_application_program,
    evms_version_management_platform
} = proxy!.useDict(
    'evms_client_version_status',
    'evms_version_management_application_program',
    'evms_version_management_platform'
);

const VersionManagementList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        platform: undefined,
        version: undefined,
        applicationProgram: undefined,
        url: undefined,
        status: undefined,
        code: undefined
    },
    rules: {
        platform: [{ required: true, message: '请选择平台类型', trigger: 'blur' }],
        version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        url: [{ required: true, message: '请上传文件', trigger: 'blur' }],
        applicationProgram: [{ required: true, message: '请选择应用程序类型', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询版本管理列表 */
function getList() {
    loading.value = true;

    listVersionManagement(queryFormat(queryParams.value)).then((response: any) => {
        VersionManagementList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        platform: undefined,
        version: undefined,
        applicationProgram: undefined,
        url: undefined,
        status: 0,
        code: undefined,
        createBy: undefined,
        updateBy: undefined,
        createTime: undefined,
        updateTime: undefined
    };
    proxy!.resetForm('VersionManagementRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加版本管理';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getVersionManagement(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改版本管理';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['VersionManagementRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateVersionManagement(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addVersionManagement(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除版本管理编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delVersionManagement(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'ota/VersionManagement/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `VersionManagement_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
