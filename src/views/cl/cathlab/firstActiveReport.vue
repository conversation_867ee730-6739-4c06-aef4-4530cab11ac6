<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button disabled>{{ cathLabTitle }}--首次激活报告</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:firstactivereport:add']"
                    type="primary"
                    plain
                    icon="Plus"
                    :disabled="exists"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:firstactivereport:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:firstactivereport:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="firstactivereportList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" width="150" prop="id" />

            <el-table-column label="填写人" align="center" prop="writer" />

            <el-table-column label="所在办事处" align="center" prop="office" />

            <el-table-column label="填写时间" align="center" prop="writeTime">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.writeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="医院" align="center" prop="hospital" />

            <el-table-column label="专家" align="center" prop="expert" />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:firstactivereport:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:firstactivereport:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改首次激活报告对话框 -->
        <el-dialog v-model="open" :title="title" width="800px" append-to-body>
            <el-form ref="firstactivereportRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="填写人" prop="writer">
                            <el-input v-model="form.writer" placeholder="请输入填写人" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="填写时间" prop="writeTime">
                            <el-date-picker
                                clearable
                                v-model="form.writeTime"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择填写时间"
                                :disabled-date="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="所在办事处" prop="office">
                            <el-input v-model="form.office" placeholder="请输入所在办事处" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="专家" prop="expert">
                            <el-input v-model="form.expert" placeholder="请输入专家" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="医院" prop="hospital">
                            <el-input v-model="form.hospital" placeholder="请输入医院" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="反馈" prop="feedback">
                            <el-input
                                type="textarea"
                                :rows="3"
                                v-model="form.feedback"
                                placeholder="请输入反馈"
                            />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="ipad画面" prop="ipadImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.ipadImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="elab画面" prop="elabImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.elabImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="ebs画面" prop="ebsImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.ebsImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Firstactivereport" lang="ts">
import {
    listFirstactivereport,
    getFirstactivereport,
    delFirstactivereport,
    addFirstactivereport,
    updateFirstactivereport
} from '@/api/cl/firstactivereport';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import ImageUpload from '@/components/ImageUpload/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import { getCathlab } from '@/api/cl/cathlab';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const firstactivereportList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const route = useRoute();
const exists = ref(false);
const cathLabTitle = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cathLab_id: undefined,
        office: undefined,
        expert: undefined,
        feedback: undefined,
        ipadImage: undefined,
        elabImage: undefined,
        ebsImage: undefined,
    },
    rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 日期限制为 今天之前日期*/
function pickerOptions(time: Date) {
    const today = new Date();
    today.setHours(23, 59, 59, 59);
    return time.getTime() > today.getTime();
}

/** 查询首次激活报告列表 */
function getList() {
    loading.value = true;
    listFirstactivereport(queryFormat(queryParams.value)).then((response: any) => {
        firstactivereportList.value = response.data.rows;
        total.value = response.data.total;
        exists.value = total.value > 0;
        loading.value = false;
    });
}

/** 关闭按钮 */
function close() {
    const obj = { path: '/cl/cathlab' };
    proxy!.$tab.closeOpenPage(obj);
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        cathId: undefined,
        writer: undefined,
        office: undefined,
        expert: undefined,
        feedback: undefined,
        hospital: undefined,
        writeTime: undefined,
        ipadImage: undefined,
        elabImage: undefined,
        ebsImage: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('firstactivereportRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    form.value.cathId = queryParams.value.cathLab_id;
    title.value = '添加首次激活报告';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getFirstactivereport(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改首次激活报告';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['firstactivereportRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateFirstactivereport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addFirstactivereport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除首次激活报告编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delFirstactivereport(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy?.download(
        'cl/firstactivereport/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `firstactivereport_${new Date().getTime()}#.xlsx`
    );
}

function getCLFirstactivereports(cathId: any) {
    if (cathId) {
        queryParams.value.cathLab_id = cathId;
        getCathlab(cathId).then((response: any) => {
            cathLabTitle.value = response.data.name;
        });
    }
    getList();
}

getCLFirstactivereports(route.params && route.params.cathId);
</script>
