import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { RefsKes } from '@/type/common';
import RightToolar from '@/components/RightToolbar/index.vue';
import moment from 'moment';
import Pagination from '@/components/Pagination/index.vue';
import {
    listExpert,
    getExpert,
    delExpert,
    addExpert,
    updateExpert,
    EvmsConsultExpert
} from '@/api/evms/expert';
import HospitalPage from '@/views/evms/consult_hospital/index';
import { DeviceVos } from '@/api/evms/consult_hospital';

interface QueryParams extends Partial<EvmsConsultExpert> {
    pageNum: number;
    pageSize: number;
}

export default defineComponent({
    emits: ['selected'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;

        const state = reactive({
            visible: false,
            currentRow: null as DeviceVos | null,
            oldCurrentRow: null as DeviceVos | null
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function handleOk() {
            if (state.currentRow) {
                close();
                emit('selected', state.currentRow, state.oldCurrentRow);
            } else {
                proxy?.$modal.msgWarning('请选择医院下的导管室');
            }
        }

        function close() {
            state.visible = false;
        }

        function show() {
            state.visible = true;
            instance.refs.HospitalPageRef.handleQuery();
        }

        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };

        function onSelected(currentRow: DeviceVos, oldCurrentRow: DeviceVos) {
            state.currentRow = currentRow;
            state.oldCurrentRow = oldCurrentRow;
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="病历详情"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                <HospitalPage ref="HospitalPageRef" onSelected={onSelected} pageType="select"></HospitalPage>
                {/* 添加model 区域 */}
            </ElDialog>
        );
    }
});
