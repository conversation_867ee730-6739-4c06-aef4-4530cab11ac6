import request from '@/utils/request';

// 查询导管室设备列表
export function listEquipment(query: any) {
    return request({
        url: '/cl/equipment/list',
        method: 'get',
        params: query,
    });
}

// 查询导管室设备详细
export function getEquipment(id: any) {
    return request({
        url: '/cl/equipment/' + id,
        method: 'get',
    });
}

// 新增导管室设备
export function addEquipment(data: any) {
    return request({
        url: '/cl/equipment',
        method: 'post',
        data: data,
    });
}

// 修改导管室设备
export function updateEquipment(data: any) {
    return request({
        url: '/cl/equipment',
        method: 'put',
        data: data,
    });
}

// 删除导管室设备
export function delEquipment(id: any) {
    return request({
        url: '/cl/equipment/' + id,
        method: 'delete',
    });
}

export function getByDeviceId(id: number) {
    const data = {
        deviceId: id
    };
    return request({
        url: '/cl/equipment/getEquipmentByDeviceId',
        method: 'get',
        params: data,
    });
}
