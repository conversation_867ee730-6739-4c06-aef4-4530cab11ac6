<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import type { DeviceUserItem } from '@/model/dms_device';
import type { ConsultationOrder } from '@/model/consultation';
import { listDmsDevice } from '@/api/evms/dms_device';
import type { FormInstance } from 'element-plus/es/components/form';
import type { TableInstance } from 'element-plus/es/components/table';
import { useRoute } from 'vue-router';
import { addCnsOrder, getOrderDetail, updateCnsOrder } from '@/api/evms/consultation';
import { getTableInfo } from '@/minix/tables';

interface DeviceOption {
    value: string;
    id: number;
}

interface BaseResponse<T = any> {
    code: number;
    message?: string;
    msg?: string;
    data: T;
}

// 组件实例
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cns_consultation_type_enum } = proxy!.useDict('cns_consultation_type_enum');
const { cns_consultation_status_enum } = proxy!.useDict('cns_consultation_status_enum');
const { cns_consultation_guide_type_enum } = proxy!.useDict('cns_consultation_guide_type_enum');

// 表单引用
const formRef = ref<FormInstance>();

// 校验规则
const rules = {
    theme: [{ required: true, message: '请输入主题', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    organizerId: [{ required: true, message: '请选择发起人', trigger: 'change' }],
    startTime: [
        { required: true, message: '请选择预约时间', trigger: 'change' },
        {
            validator: (rule: any, value: any) => {
                console.log('预约时间value', value);
                if (!value) return Promise.resolve();
                // 确保值是数字类型
                const selectedTime = value;
                const now = Date.now();

                if (!orderId && selectedTime < now - 60 * 1000) {
                    return Promise.reject('预约时间不能早于当前时间');
                }
                return Promise.resolve();
            },
            trigger: ['blur', 'change'] // 添加多个触发时机
        }
    ],
    guideType: [{ required: true, message: '请选择指导类型', trigger: 'blur' }],
    deviceId: [{ required: true, message: '请选择导管室', trigger: 'change' }],
    expertId: [
        {
            validator: (rule: any, value: any) => {
                // 当类型为自选专家(S)时才进行必选校验
                if (state.cnsInfo.type === 'S' && !value) {
                    return Promise.reject('请选择自选专家');
                }
                return Promise.resolve();
            },
            trigger: ['change', 'blur']
        }
    ]
};

// 当前激活的标签页
const activeName = ref('first');

// 状态管理
const state = reactive({
    cnsInfo: {
        type: 'P',
        status: 1,
        guideType: 'SD',
        expertId: undefined,
        organizerId: undefined,
        userIds: [] as number[]
    } as ConsultationOrder,
    otherInfo: {
        fqr: '',
        zdzj: '',
        zdjb: '',
        zxzj: ''
    },
    // 指导嘉宾列表，用于标签显示
    guestList: [] as { id: number; name: string }[]
});

// 导管室列表查询
const querySearchDevice = async (queryString: string, cb: (suggestions: DeviceOption[]) => void) => {
    try {
        const response = await listDmsDevice({ q_like_name: queryString });
        const results = response as unknown as BaseResponse;

        if (results.code === 200 && Array.isArray(results.data?.rows)) {
            const suggestions = results.data.rows.map((item: any) => ({
                value: `${item.hospitalVo?.name || ''}-${item.name}`,
                id: item.id
            }));
            cb(suggestions);
        } else {
            cb([]);
        }
    } catch (error) {
        console.error('查询导管室失败:', error);
        cb([]);
    }
};

// 选择导管室
const handleSelectDevice = (item: DeviceOption) => {
    state.cnsInfo.deviceId = item.value;
    state.cnsInfo.device = {
        id: String(item.id),
        name: item.value,
        type: 0, // 默认值
        description: '',
        permission: {
            elab: false,
            ebs: false,
            outpatient: false,
            videoExportPermission: false
        }
    };
};

// 判断行是否可选
const selectable = (row: DeviceUserItem) => {
    const selectedIds = [state.cnsInfo.expertId, state.cnsInfo.organizerId, ...(state.cnsInfo.userIds || [])]
        .filter(Boolean)
        .map(id => Number(id));

    return !selectedIds.includes(Number(row.id));
};

// 返回上一页
const handleBack = () => {
    state.cnsInfo = {} as ConsultationOrder;
    history.back();
};

// 记录新增的医院id
let newOrderId = '';

// 保存操作
const save = async () => {
    if (state.cnsInfo.guideType === 'UR' && !state.cnsInfo.startTime) {
        state.cnsInfo.startTime = Date.now();
    }
    if (!formRef.value) return;
    if (newOrderId) {
        proxy!.$modal.msgWarning('已经保存过了');
        return;
    }

    try {
        const valid = await formRef.value.validate();
        if (!valid) return false;

        const data: Partial<ConsultationOrder> = {
            description: state.cnsInfo.description,
            deviceId: state.cnsInfo.device?.id,
            expertId: state.cnsInfo.expertId,
            guideType: state.cnsInfo.guideType,
            organizerId: state.cnsInfo.organizerId,
            startTime: state.cnsInfo.startTime,
            theme: state.cnsInfo.theme,
            type: state.cnsInfo.type,
            userIds: state.cnsInfo.userIds,
            status: state.cnsInfo.status
        };

        if (orderId) {
            data.id = Number(state.cnsInfo.id);
            const response = await updateCnsOrder(data as ConsultationOrder);
            const res = response as unknown as BaseResponse;
            if (res.code === 200) {
                proxy!.$modal.msgSuccess('保存成功');
            } else if (res.code === -3003) {
                //对话确认框 提示用户是否继续
                proxy!.$modal
                    .confirm(res.msg as string)
                    .then(() => {
                        // 用户点击确定，执行更新操作
                        data.isCancelConflict = 'T';
                        return updateCnsOrder({ ...data, isCancelConflict: 'T' });
                    })
                    .then(res => {
                        if (res.code === 200) {
                            // getList();
                            proxy!.$modal.msgSuccess('保存成功');
                            // 重置列表
                        }
                    });
            } else {
                proxy!.$modal.msgError(res.msg as string);
            }
        } else {
            if (data.guideType === 'UR') {
                delete data.startTime;
            }
            const response = await addCnsOrder(data as ConsultationOrder);
            const res = response as unknown as BaseResponse;
            if (res.code === 200) {
                newOrderId = String(res.data.id);
                proxy!.$modal.msgSuccess('保存成功');
            } else if (res.code === -3003) {
                //对话确认框 提示用户是否继续
                proxy!.$modal
                    .confirm(res.msg as string)
                    .then(() => {
                        // 用户点击确定，执行更新操作
                        return addCnsOrder({ ...data, isCancelConflict: 'T' });
                    })
                    .then(res => {
                        if (res.code === 200) {
                            // getList();
                            proxy!.$modal.msgSuccess('保存成功');
                            // 重置列表
                        }
                    });
            } else {
                proxy!.$modal.msgError(res.msg as string);
            }
        }
    } catch (error) {
        console.error('保存失败:', error);
        proxy!.$modal.msgError('保存失败');
    }
};

// 保存并返回
const saveAndBack = async () => {
    if (state.cnsInfo.guideType === 'UR' && !state.cnsInfo.startTime) {
        state.cnsInfo.startTime = Date.now();
    }
    if (!formRef.value) return;
    if (newOrderId) {
        history.back();
        return;
    }

    try {
        const valid = await formRef.value.validate();
        if (!valid) return false;

        const data: Partial<ConsultationOrder> = {
            description: state.cnsInfo.description,
            deviceId: state.cnsInfo.device?.id,
            expertId: state.cnsInfo.expertId,
            guideType: state.cnsInfo.guideType,
            organizerId: state.cnsInfo.organizerId,
            startTime: state.cnsInfo.startTime,
            theme: state.cnsInfo.theme,
            type: state.cnsInfo.type,
            userIds: state.cnsInfo.userIds,
            status: state.cnsInfo.status
        };

        if (orderId) {
            data.id = Number(state.cnsInfo.id);

            const response = await updateCnsOrder(data as ConsultationOrder);
            const res = response as unknown as BaseResponse;
            if (res.code === 200) {
                proxy!.$modal.msgSuccess('保存成功');
                history.back();
            } else if (res.code === -3003) {
                //对话确认框 提示用户是否继续
                proxy!.$modal
                    .confirm(res.msg as string)
                    .then(() => {
                        // 用户点击确定，执行更新操作
                        return addCnsOrder({ ...data, isCancelConflict: 'T' });
                    })
                    .then(res => {
                        if (res.code === 200) {
                            // getList();
                            proxy!.$modal.msgSuccess('保存成功');
                            // 重置列表
                        }
                    });
            } else {
                proxy!.$modal.msgError(res.msg as string);
            }
        } else {
            if (data.guideType === 'UR') {
                delete data.startTime;
            }
            const response = await addCnsOrder(data as ConsultationOrder);
            const res = response as unknown as BaseResponse;
            if (res.code === 200) {
                proxy!.$modal.msgSuccess('保存成功');
                history.back();
            } else if (res.code === -3003) {
                //对话确认框 提示用户是否继续
                proxy!.$modal
                    .confirm(res.msg as string)
                    .then(() => {
                        // 用户点击确定，执行更新操作
                        return addCnsOrder({ ...data, isCancelConflict: 'T' });
                    })
                    .then(res => {
                        if (res.code === 200) {
                            // getList();
                            proxy!.$modal.msgSuccess('保存成功');
                            // 重置列表
                        }
                    });
            } else {
                proxy!.$modal.msgError(res.msg as string);
            }
        }
    } catch (error) {
        console.error('保存失败:', error);
        proxy!.$modal.msgError('保存失败');
    }
};

// 路由参数
const route = useRoute();
const orderId = route.params.id as string;

// 获取订单详情
const getOrderInfo = async (id: string) => {
    try {
        const response = await getOrderDetail(id);
        const res = response as unknown as BaseResponse<ConsultationOrder>;
        if (res.code === 200) {
            state.cnsInfo = res.data || ({} as ConsultationOrder);
            state.cnsInfo.organizerId = state.cnsInfo.organizer?.userid;
            state.otherInfo.fqr = state.cnsInfo.organizer?.realname || '';
            state.cnsInfo.deviceId = state.cnsInfo.device?.name;
            state.cnsInfo.expertId = state.cnsInfo.expert?.userid;
            state.otherInfo.zdzj = state.cnsInfo.expert?.realname || '';
            state.cnsInfo.userIds = state.cnsInfo.memberList?.map(i => i.user.userid);

            // 初始化嘉宾列表用于标签显示
            state.guestList =
                state.cnsInfo.memberList?.map(i => ({
                    id: i.user.userid,
                    name: i.user.realname
                })) || [];

            // 保持原有字符串格式，用于兼容性
            state.otherInfo.zdjb = state.cnsInfo.memberList?.map(i => i.user.realname).join('、') || '';
            state.otherInfo.zxzj = state.cnsInfo.expert?.realname || '';
        }
    } catch (error) {
        console.error('获取订单详情失败:', error);
        proxy!.$modal.msgError('获取订单详情失败');
    }
};

// 初始化数据
if (orderId) {
    getOrderInfo(orderId);
}

// 对话框类型枚举
enum DialogEnum {
    FQR,
    ZDZJ,
    ZDJB,
    ZXZJ,
}

const activeTypeName = ref<DialogEnum>(DialogEnum.FQR);

const titleTypeEnum: { [key in DialogEnum]: string } = {
    [DialogEnum.FQR]: '发起人',
    [DialogEnum.ZDZJ]: '指导专家',
    [DialogEnum.ZDJB]: '指导嘉宾',
    [DialogEnum.ZXZJ]: '自选专家'
};

// 表格数据
const tableInfoAdd = getTableInfo('/evms/doctor_user/list', { q_type: 0 });
const tableInfoAdd1 = getTableInfo('/evms/doctor_expert/list');

// 对话框状态
const open = ref(false);
const open1 = ref(false);
const openMore = ref(false);
const openMore1 = ref(false);

// 关闭对话框
const closeDialog = () => {
    open.value = false;
    open1.value = false;
    openMore.value = false;
    openMore1.value = false;
    tableInfoAdd.reset();
    tableInfoAdd1.reset();
};

// 处理点击事件
const handleClick = (v: DialogEnum) => {
    activeTypeName.value = v;
    if (v === DialogEnum.ZDZJ && state.cnsInfo.type === 'P') {
        open1.value = true;
        tableInfoAdd.load();
    } else if (v === DialogEnum.ZDJB) {
        tableInfoAdd.load();
        openMore.value = true;
    } else {
        tableInfoAdd.load();
        open.value = true;
    }
};

// 选择用户相关
const idsForAdd = ref<DeviceUserItem[]>([]);
const multipleTableRef = ref<TableInstance>();

const handleSelectionChangeForAdd = (selection: DeviceUserItem[]) => {
    idsForAdd.value = selection;
};

const handleCurrentChange = (row: DeviceUserItem | undefined) => {
    if (!row) return;

    const selectedIds = [
        state.cnsInfo.expertId,
        state.cnsInfo.organizerId,
        ...(state.cnsInfo.userIds || [])
    ].map(id => Number(id));

    if (!selectedIds.includes(Number(row.id))) {
        idsForAdd.value = [row];
    } else {
        proxy!.$modal.msgWarning('该用户已经选择');
        multipleTableRef.value?.setCurrentRow(null);
    }
};

const handleCurrentChange1 = (row: DeviceUserItem | undefined) => {
    if (!row) return;

    const selectedIds = [
        state.cnsInfo.expertId,
        state.cnsInfo.organizerId,
        ...(state.cnsInfo.userIds || [])
    ].map(id => Number(id));

    if (!selectedIds.includes(Number(row.id))) {
        idsForAdd.value = [row];
    } else {
        proxy!.$modal.msgWarning('该用户已经选择');
        multipleTableRef.value?.setCurrentRow(null);
    }
};

// 提交表单
const submitForm = () => {
    if (!idsForAdd.value.length) {
        proxy!.$modal.msgError('请选择用户');
        return;
    }

    const selectedUser = idsForAdd.value[0];

    switch (activeTypeName.value) {
        case DialogEnum.FQR:
            state.cnsInfo.organizerId = Number(selectedUser.id);
            state.otherInfo.fqr = selectedUser.realname;
            break;
        case DialogEnum.ZDZJ:
            state.cnsInfo.expertId = Number(selectedUser.id);
            state.otherInfo.zdzj = selectedUser.realname;
            break;
        case DialogEnum.ZDJB:
            // 更新userIds
            state.cnsInfo.userIds = [
                ...(state.cnsInfo.userIds || []),
                ...idsForAdd.value.map(i => Number(i.id))
            ];

            // 更新嘉宾列表用于标签显示
            const newGuests = idsForAdd.value.map(i => ({
                id: Number(i.id),
                name: i.realname
            }));
            state.guestList = [...state.guestList, ...newGuests];

            // 保持原有字符串格式，用于兼容性
            state.otherInfo.zdjb = state.guestList.map(guest => guest.name).join('、');
            break;
        case DialogEnum.ZXZJ:
            state.cnsInfo.expertId = Number(selectedUser.id);
            state.otherInfo.zxzj = selectedUser.realname;
            break;
    }

    idsForAdd.value = [];
    closeDialog();
};

// 取消选择
const cancel = () => {
    closeDialog();
    idsForAdd.value = [];
};

// 删除单个嘉宾
const handleRemoveGuest = (guest: { id: number; name: string }) => {
    // 从嘉宾列表中移除
    state.guestList = state.guestList.filter(item => item.id !== guest.id);

    // 从userIds中移除
    state.cnsInfo.userIds = state.cnsInfo.userIds?.filter(id => id !== guest.id) || [];

    // 更新字符串显示
    state.otherInfo.zdjb = state.guestList.map(g => g.name).join('、');
};

// 清空所有嘉宾
const clearAllGuests = () => {
    state.guestList = [];
    state.cnsInfo.userIds = [];
    state.otherInfo.zdjb = '';
};
const disabledDate = (time: Date) => {
    return time.getTime() < Date.now() - 8.64e7;
};

const disabledTime = (date: Date) => {
    if (!date) return;
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentSecond = now.getSeconds();

        return {
            disabledHours: () => Array.from({ length: currentHour }, (_, i) => i),
            disabledMinutes: (selectedHour: number) => {
                if (selectedHour === currentHour) {
                    return Array.from({ length: currentMinute }, (_, i) => i);
                }
                return [];
            },
            disabledSeconds: (selectedHour: number, selectedMinute: number) => {
                if (selectedHour === currentHour && selectedMinute === currentMinute) {
                    return Array.from({ length: currentSecond }, (_, i) => i);
                }
                return [];
            }
        };
    }

    return {
        disabledHours: () => [],
        disabledMinutes: () => [],
        disabledSeconds: () => []
    };
};
</script>

<template>
    <div class="consultation_order_container">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" class="demo-tabs" type="card">
                <el-tab-pane label="基础信息" name="first">
                    <el-form ref="formRef" :model="state.cnsInfo" :rules="rules" label-position="top">
                        <el-row :gutter="10" class="form-con" justify="space-between">
                            <el-col :span="24">
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="主题" prop="theme" style="width: 100%">
                                            <el-input
                                                v-model="state.cnsInfo.theme"
                                                clearable
                                                maxlength="255"
                                                placeholder="请输入主题"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="进行状态" prop="status">
                                            <el-select
                                                v-model="state.cnsInfo.status"
                                                class="m-2"
                                                disabled
                                                placeholder=""
                                                size="large"
                                                value-key="value"
                                            >
                                                <el-option
                                                    v-for="dict in cns_consultation_status_enum"
                                                    :key="dict.value"
                                                    :label="dict.label"
                                                    :value="parseInt(dict.value)"
                                                />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="发起人" prop="organizerId">
                                            <el-input
                                                v-model="state.otherInfo.fqr"
                                                placeholder="点击选择发起人"
                                                style="width: 240px"
                                                @focus="() => handleClick(DialogEnum.FQR)"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="预约时间" prop="startTime">
                                            <el-date-picker
                                                v-model="state.cnsInfo.startTime"
                                                :disabled="state.cnsInfo.guideType === 'UR'"
                                                placeholder="请选择时间"
                                                type="datetime"
                                                value-format="x"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="指导类型" prop="guideType">
                                            <el-radio-group v-model="state.cnsInfo.guideType">
                                                <el-radio
                                                    v-for="item in cns_consultation_guide_type_enum"
                                                    :key="item.value"
                                                    :label="item.value"
                                                    size="large"
                                                >
                                                    {{ item.label }}
                                                </el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="导管室" prop="deviceId">
                                            <el-autocomplete
                                                v-model="state.cnsInfo.deviceId"
                                                :fetch-suggestions="querySearchDevice"
                                                clearable
                                                placeholder="请选择导管室"
                                                style="width: 240px"
                                                @select="handleSelectDevice"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="专家类型" prop="type">
                                                    <el-radio-group v-model="state.cnsInfo.type">
                                                        <el-radio
                                                            v-for="item in cns_consultation_type_enum.filter(
                                                                i => i.value !== 'G',
                                                            )"
                                                            :key="item.value"
                                                            :label="item.value"
                                                            size="large"
                                                        >
                                                            {{ item.label }}
                                                        </el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-col>
                                            <el-col v-show="state.cnsInfo.type === 'P'" :span="24">
                                                <el-form-item label="指导专家(单选)" prop="expertId">
                                                    <el-input
                                                        v-model="state.otherInfo.zdzj"
                                                        placeholder="点击选择指导专家"
                                                        style="width: 240px"
                                                        @focus="() => handleClick(DialogEnum.ZDZJ)"
                                                    />
                                                </el-form-item>
                                            </el-col>
                                            <el-col v-show="state.cnsInfo.type === 'P'" :span="24">
                                                <el-form-item label="指导嘉宾(多选)" prop="userIds">
                                                    <div style="position: relative; width: 240px">
                                                        <div
                                                            class="guest-tags-container"
                                                            style="
                                                                width: 100%;
                                                                min-height: 40px;
                                                                background: #f8f9fb;
                                                                border-radius: 4px;
                                                                border: 1px solid #dcdfe6;
                                                                padding: 4px 8px;
                                                                display: flex;
                                                                flex-wrap: wrap;
                                                                align-items: center;
                                                                cursor: pointer;
                                                            "
                                                            @click="() => handleClick(DialogEnum.ZDJB)"
                                                        >
                                                            <template v-if="state.guestList.length">
                                                                <el-tag
                                                                    v-for="guest in state.guestList"
                                                                    :key="guest.id"
                                                                    closable
                                                                    style="
                                                                        margin: 2px 4px;
                                                                        max-width: 120px;
                                                                        overflow: hidden;
                                                                        text-overflow: ellipsis;
                                                                        white-space: nowrap;
                                                                    "
                                                                    @close.stop="handleRemoveGuest(guest)"
                                                                >
                                                                    {{ guest.name }}
                                                                </el-tag>
                                                            </template>
                                                            <span
                                                                v-else
                                                                style="color: #909399; font-size: 14px"
                                                            >点击选择指导嘉宾</span
                                                            >
                                                        </div>
                                                        <el-button
                                                            v-if="state.guestList.length"
                                                            type="text"
                                                            style="
                                                                position: absolute;
                                                                right: 5px;
                                                                top: 5px;
                                                                z-index: 1;
                                                                margin-right: 5px;
                                                                padding: 4px 8px;
                                                            "
                                                            @click.stop="clearAllGuests"
                                                        >清空
                                                        </el-button
                                                        >
                                                    </div>
                                                </el-form-item>
                                            </el-col>
                                            <el-col v-show="state.cnsInfo.type === 'S'" :span="24">
                                                <el-form-item label="自选专家(单选)" prop="expertId">
                                                    <el-input
                                                        v-model="state.otherInfo.zxzj"
                                                        :readonly="true"
                                                        placeholder="点击选择自选专家"
                                                        style="width: 240px"
                                                        @focus="() => handleClick(DialogEnum.ZXZJ)"
                                                    />
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-col>

                                    <el-col :span="10">
                                        <el-form-item label="主诉" prop="description">
                                            <el-input
                                                v-model="state.cnsInfo.description"
                                                maxlength="500"
                                                placeholder="请输入主诉"
                                                style="width: 70%; height: 111px"
                                                type="textarea"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-col>
                            <el-col :span="24" style="height: 100px; margin-top: auto">
                                <el-row justify="end">
                                    <el-button @click="handleBack">返回</el-button>
                                    <el-button type="primary" @click="save">保存</el-button>
                                    <el-button type="primary" @click="saveAndBack">保存并返回</el-button>
                                </el-row>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </el-card>
        <!--    选择发起人用户弹框 单选 -->
        <el-dialog
            v-model="open"
            append-to-body
            draggable
            modal-class="consultation_select_group"
            width="1156px"
            @closed="closeDialog"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">选择{{ titleTypeEnum[activeTypeName] }}</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAdd.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="8">
                            <el-form-item label="ID" prop="id">
                                <el-input
                                    v-model="tableInfoAdd.from.id"
                                    clearable
                                    placeholder="请输入ID"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAdd.from.like_realname"
                                    clearable
                                    placeholder="请输入"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-button icon="Search" type="primary" @click="tableInfoAdd.search"
                            >搜索
                            </el-button>
                            <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                        </el-col>
                    </el-row>
                    <!--            <el-form-item label="描述" prop="description">-->
                    <!--                <el-input-->
                    <!--                    v-model="tableInfo.from.description"-->
                    <!--                    placeholder="请输入描述"-->
                    <!--                    clearable-->
                    <!--                    style="width: 240px"-->
                    <!--                    @keyup.enter="handleQuery"-->
                    <!--                />-->
                    <!--            </el-form-item>-->
                </el-form>
                <el-table
                    ref="multipleTableRef"
                    :data="tableInfoAdd.list.value"
                    height="350"
                    highlight-current-row
                    style="width: 100%; margin-top: 20px"
                    @current-change="handleCurrentChange"
                >
                    <el-table-column label="ID" prop="id" width="140"></el-table-column>
                    <el-table-column label="用户姓名" prop="realname" width="120"></el-table-column>
                    <el-table-column label="手机号" prop="mobile"></el-table-column>
                    <el-table-column label="医院" prop="company"></el-table-column>
                    <el-table-column label="科室" prop="department"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAdd.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAdd.total.value > 0"
                        v-model:limit="tableInfoAdd.pageParam.pageSize"
                        v-model:page="tableInfoAdd.pageParam.pageNum"
                        :total="tableInfoAdd.total.value"
                        @pagination="tableInfoAdd.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!--    选择平台专家用户弹框 单选 -->
        <el-dialog
            v-model="open1"
            append-to-body
            draggable
            modal-class="consultation_select_group"
            width="1156px"
            @closed="closeDialog"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">选择{{ titleTypeEnum[activeTypeName] }}</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAdd.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="8">
                            <el-form-item label="ID" prop="id">
                                <el-input
                                    v-model="tableInfoAdd.from.id"
                                    clearable
                                    placeholder="请输入ID"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAdd.from.like_realname"
                                    clearable
                                    placeholder="请输入"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-button icon="Search" type="primary" @click="tableInfoAdd.search"
                            >搜索
                            </el-button>
                            <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                        </el-col>
                    </el-row>
                    <!--            <el-form-item label="描述" prop="description">-->
                    <!--                <el-input-->
                    <!--                    v-model="tableInfo.from.description"-->
                    <!--                    placeholder="请输入描述"-->
                    <!--                    clearable-->
                    <!--                    style="width: 240px"-->
                    <!--                    @keyup.enter="handleQuery"-->
                    <!--                />-->
                    <!--            </el-form-item>-->
                </el-form>
                <el-table
                    ref="multipleTableRef"
                    :data="tableInfoAdd.list.value"
                    height="350"
                    highlight-current-row
                    style="width: 100%; margin-top: 20px"
                    @current-change="handleCurrentChange1"
                >
                    <el-table-column label="ID" prop="id" width="140"></el-table-column>
                    <el-table-column label="用户姓名" prop="realname" width="120"></el-table-column>
                    <el-table-column label="手机号" prop="mobile"></el-table-column>
                    <el-table-column label="医院" prop="company"></el-table-column>
                    <el-table-column label="科室" prop="department"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAdd.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAdd.total.value > 0"
                        v-model:limit="tableInfoAdd.pageParam.pageSize"
                        v-model:page="tableInfoAdd.pageParam.pageNum"
                        :total="tableInfoAdd.total.value"
                        @pagination="tableInfoAdd.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!--    选择专家弹框 多选 -->
        <el-dialog
            v-model="openMore1"
            append-to-body
            draggable
            modal-class="consultation_select_group"
            width="1156px"
            @closed="closeDialog"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">选择{{ titleTypeEnum[activeTypeName] }}</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAdd1.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="8">
                            <el-form-item label="ID" prop="id">
                                <el-input
                                    v-model="tableInfoAdd1.from.id"
                                    clearable
                                    placeholder="请输入ID"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd1.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAdd1.from.like_realname"
                                    clearable
                                    placeholder="请输入"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd1.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-button icon="Search" type="primary" @click="tableInfoAdd1.search"
                            >搜索
                            </el-button>
                            <el-button icon="Refresh" @click="tableInfoAdd1.reset">重置</el-button>
                        </el-col>
                    </el-row>
                    <!--            <el-form-item label="描述" prop="description">-->
                    <!--                <el-input-->
                    <!--                    v-model="tableInfo.from.description"-->
                    <!--                    placeholder="请输入描述"-->
                    <!--                    clearable-->
                    <!--                    style="width: 240px"-->
                    <!--                    @keyup.enter="handleQuery"-->
                    <!--                />-->
                    <!--            </el-form-item>-->
                </el-form>
                <el-table
                    :data="tableInfoAdd1.list.value"
                    height="350"
                    style="width: 100%; margin-top: 20px"
                    @selection-change="handleSelectionChangeForAdd"
                >
                    <el-table-column :selectable="selectable" type="selection" width="55"></el-table-column>
                    <el-table-column label="ID" prop="id" width="140"></el-table-column>
                    <el-table-column label="用户姓名" prop="realname" width="120"></el-table-column>
                    <el-table-column label="手机号" prop="mobile"></el-table-column>
                    <el-table-column label="医院" prop="company"></el-table-column>
                    <el-table-column label="科室" prop="department"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAdd1.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAdd1.total.value > 0"
                        v-model:limit="tableInfoAdd1.pageParam.pageSize"
                        v-model:page="tableInfoAdd1.pageParam.pageNum"
                        :total="tableInfoAdd1.total.value"
                        @pagination="tableInfoAdd1.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!--    选择用户弹框 多选 -->
        <el-dialog
            v-model="openMore"
            append-to-body
            draggable
            modal-class="consultation_select_group"
            width="1156px"
            @closed="closeDialog"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">选择{{ titleTypeEnum[activeTypeName] }}</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAdd.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="8">
                            <el-form-item label="ID" prop="id">
                                <el-input
                                    v-model="tableInfoAdd.from.id"
                                    clearable
                                    placeholder="请输入ID"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAdd.from.like_realname"
                                    clearable
                                    placeholder="请输入"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-button icon="Search" type="primary" @click="tableInfoAdd.search"
                            >搜索
                            </el-button>
                            <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                        </el-col>
                    </el-row>
                    <!--            <el-form-item label="描述" prop="description">-->
                    <!--                <el-input-->
                    <!--                    v-model="tableInfo.from.description"-->
                    <!--                    placeholder="请输入描述"-->
                    <!--                    clearable-->
                    <!--                    style="width: 240px"-->
                    <!--                    @keyup.enter="handleQuery"-->
                    <!--                />-->
                    <!--            </el-form-item>-->
                </el-form>
                <el-table
                    :data="tableInfoAdd.list.value"
                    height="350"
                    style="width: 100%; margin-top: 20px"
                    @selection-change="handleSelectionChangeForAdd"
                >
                    <el-table-column :selectable="selectable" type="selection" width="55"></el-table-column>
                    <el-table-column label="ID" prop="id" width="140"></el-table-column>
                    <el-table-column label="用户姓名" prop="realname" width="120"></el-table-column>
                    <el-table-column label="手机号" prop="mobile"></el-table-column>
                    <el-table-column label="医院" prop="company"></el-table-column>
                    <el-table-column label="科室" prop="department"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAdd.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAdd.total.value > 0"
                        v-model:limit="tableInfoAdd.pageParam.pageSize"
                        v-model:page="tableInfoAdd.pageParam.pageNum"
                        :total="tableInfoAdd.total.value"
                        @pagination="tableInfoAdd.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<style lang="scss">
.consultation_select_group {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
<style lang="scss" scoped>
.consultation_order_container {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;

    :deep(.el-card) {
        height: 100%;
    }

    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }

    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }

    :deep(.el-tab-pane) {
        height: 100% !important;
        overflow-y: scroll;
    }

    :deep(.el-form) {
        height: 100% !important;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0 0 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        height: 40px !important;
    }

    .form-con {
        height: 100%;
    }

    :deep(.el-form-item__content) {
        width: 100% !important;
    }

    :deep(.el-textarea) {
        width: 100% !important;
    }

    :deep(.el-row) {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}
</style>
