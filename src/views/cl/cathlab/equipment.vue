<template>
    <div class="app-container">
        <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="导管室基础信息" name="CLCathLabInfo"
            >{{ cathLabTitle }}--基础信息
            </el-tab-pane
            >
            <el-tab-pane label="导管室设备信息" name="CLEquipment">{{ cathLabTitle }}--设备信息</el-tab-pane>
            <el-tab-pane label="导管室联系信息" name="CLContactInfo"
            >{{ cathLabTitle }}--联系信息
            </el-tab-pane
            >
            <el-tab-pane label="导管室合作信息" name="CLCooperate">{{ cathLabTitle }}--合作信息</el-tab-pane>
        </el-tabs>

        <!-- 添加或修改导管室设备对话框 -->
        <el-form v-show="open" ref="equipmentRef" :model="form" :rules="rules" label-width="200px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="导管室账号" prop="account">
                        <el-input v-model="form.account" placeholder="请输入导管室账号" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="主产品" prop="main">
                        <el-input v-model="form.main" placeholder="请输入主产品" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="摄像头" prop="camera">
                        <el-input v-model="form.camera" placeholder="请输入摄像头" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="降频仪" prop="frequency">
                        <el-input v-model="form.frequency" placeholder="请输入降频仪" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="DSA" prop="dsa">
                        <el-input v-model="form.dsa" placeholder="请输入DSA" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="3D" prop="stereoscopic">
                        <el-input v-model="form.stereoscopic" placeholder="请输入3D" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="ipad" prop="ipad">
                        <el-input v-model="form.ipad" placeholder="请输入ipad" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="其他设备" prop="others">
                        <el-input v-model="form.others" placeholder="请输入其他设备" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="病例保存期限" prop="preservation">
                        <el-input v-model="form.preservation" placeholder="请输入病例保存期限" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="安装日期" prop="installTime">
                        <el-date-picker
                            clearable
                            v-model="form.installTime"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            placeholder="请选择安装日期"
                            :disabled-date="pickerOptions"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="花生壳到期" prop="expire">
                        <el-input v-model="form.expire" placeholder="请输入花生壳到期" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="网络状态" prop="network">
                        <el-input v-model="form.network" placeholder="请输入网络状态" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="设备状态" prop="status">
                        <el-input v-model="form.status" placeholder="请输入设备状态" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="费用" prop="expense">
                        <el-input v-model="form.expense" placeholder="请输入费用" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-button type="primary" @click="submitForm">保 存</el-button>
            <el-button @click="cancel">取 消</el-button>
        </el-form>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Equipment" lang="ts">
import { listEquipment, getEquipment, delEquipment, addEquipment, updateEquipment } from '@/api/cl/equipment';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { getCathlab } from '@/api/cl/cathlab';
import { queryFormat } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const open = ref(false);
const cathLabTitle = ref('');
const route = useRoute();
const router = useRouter();
const activeName = ref('CLEquipment');

const data = reactive<{
    form: any;
    rules: any;
}>({
    form: {},
    rules: {}
});

const { form, rules } = toRefs(data);

/** 日期限制为 今天之前日期*/
function pickerOptions(time: Date) {
    const today = new Date();
    today.setHours(23, 59, 59, 59);
    return time.getTime() > today.getTime();
}

/** 取消按钮 */
function cancel() {
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        cathId: undefined,
        status: undefined,
        account: undefined,
        main: undefined,
        camera: undefined,
        dsa: undefined,
        stereoscopic: undefined,
        others: undefined,
        frequency: undefined,
        ipad: undefined,
        preservation: undefined,
        installTime: undefined,
        network: undefined,
        expire: undefined,
        expense: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('equipmentRef');
}

/** 关闭按钮 */
function close() {
    const obj = { path: '/cl/cathlab', name: 'Cathlab' };
    proxy!.$tab.closeOpenPage(obj).then(() => {
        // 页面关闭后，刷新页面
        proxy!.$tab.refreshPage(obj);
    });
}

function handleClick(tab: any) {
    const cathId = Number(route.params.cathId);
    if ('CLCathLabInfo' == tab.props.name) {
        router.push('/cl/cath-lab/cathLabInfo/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    } else if ('CLContactInfo' == tab.props.name) {
        router.push('/cl/cath-lab/contactInfo/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    } else if ('CLCooperate' == tab.props.name) {
        router.push('/cl/cath-lab/cooperate/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    }
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['equipmentRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateEquipment(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('修改成功');
                    form.value = response.data;
                    open.value = true;
                });
            } else {
                if (Number(route.params.cathId) === 0) {
                    proxy?.$modal.msgError('请先添加导管室数据');
                    reset();
                    open.value = true;
                } else {
                    addEquipment(form.value).then((response: any) => {
                        proxy?.$modal.msgSuccess('新增成功');
                        form.value = response.data;
                        open.value = true;
                    });
                }
            }
        }
    });
}

function getCLEquipments(cathId: any) {
    if (cathId && cathId != 0) {
        getCathlab(cathId).then((response: any) => {
            cathLabTitle.value = response.data.name;
        });
        listEquipment(queryFormat({ cathLab_id: cathId })).then((response: any) => {
            if (!response.data.rows[0]) {
                reset();
            } else {
                form.value = response.data.rows[0];
            }
            form.value.cathId = cathId;
            open.value = true;
        });
    } else {
        reset();
        open.value = true;
    }
}

getCLEquipments(route.params && route.params.cathId);
</script>
