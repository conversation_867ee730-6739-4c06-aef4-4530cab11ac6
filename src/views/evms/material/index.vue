<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="素材名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入素材名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
                    <el-option
                        v-for="dict in evms_ebs_source_material_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="是否在回收站" prop="recycleBin">
                <el-select v-model="queryParams.recycleBin" placeholder="请选择是否在回收站" clearable>
                    <el-option
                        v-for="dict in evms_recycle_bin"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="所属用户" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入所属用户"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:material:add']"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['evms:material:edit']"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['evms:material:remove']"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['evms:material:export']"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="MaterialList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="素材名称" align="center" prop="name" />
            <el-table-column label="视频封面" align="center" prop="cover" width="100">
                <template #default="scope">
                    <image-preview :src="scope.row.cover" :width="50" :height="50" />
                </template>
            </el-table-column>
            <el-table-column label="类型" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="evms_ebs_source_material_type" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="素材文件url" align="center" prop="fileUrl" />
            <el-table-column label="是否在回收站" align="center" prop="recycleBin">
                <template #default="scope">
                    <dict-tag :options="evms_recycle_bin" :value="scope.row.recycleBin" />
                </template>
            </el-table-column>
            <el-table-column label="所属用户" align="center" prop="userId" />
            <el-table-column label="宽" align="center" prop="width" />
            <el-table-column label="高" align="center" prop="height" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:material:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:material:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改EBS素材库对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="MaterialRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="素材名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入素材名称" />
                </el-form-item>
                <el-form-item label="视频封面" prop="cover">
                    <image-upload v-model="form.cover" />
                </el-form-item>
                <el-form-item label="类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择类型">
                        <el-option
                            v-for="dict in evms_ebs_source_material_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="素材文件url" prop="fileUrl">
                    <file-upload v-model="form.fileUrl" />
                </el-form-item>
                <el-form-item label="是否在回收站" prop="recycleBin">
                    <el-select v-model="form.recycleBin" placeholder="请选择是否在回收站">
                        <el-option
                            v-for="dict in evms_recycle_bin"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属用户" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入所属用户" />
                </el-form-item>
                <el-form-item label="宽" prop="width">
                    <el-input v-model="form.width" placeholder="请输入宽" />
                </el-form-item>
                <el-form-item label="高" prop="height">
                    <el-input v-model="form.height" placeholder="请输入高" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Material" lang="ts">
import { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial } from '@/api/evms/material';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_ebs_source_material_type, evms_recycle_bin } = proxy!.useDict(
    'evms_ebs_source_material_type',
    'evms_recycle_bin'
);

const MaterialList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        cover: undefined,
        type: undefined,
        fileUrl: undefined,
        recycleBin: undefined,
        userId: undefined,
        width: undefined,
        height: undefined
    },
    rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询EBS素材库列表 */
function getList() {
    loading.value = true;

    listMaterial(queryFormat(queryParams.value)).then((response: any) => {
        MaterialList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        name: undefined,
        cover: undefined,
        type: undefined,
        fileUrl: undefined,
        recycleBin: undefined,
        userId: undefined,
        width: undefined,
        height: undefined,
        createBy: undefined,
        updateBy: undefined,
        createTime: undefined,
        updateTime: undefined
    };
    proxy!.resetForm('MaterialRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加EBS素材库';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getMaterial(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改EBS素材库';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['MaterialRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateMaterial(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addMaterial(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除EBS素材库编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delMaterial(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'evms/Material/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `Material_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
