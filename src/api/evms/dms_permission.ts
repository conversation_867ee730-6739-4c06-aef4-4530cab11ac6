import request, { Http } from '@/utils/request';
import { DmsUserInfo, UserDevice } from '@/model/dms_user';
import { AddDeviceUserParams } from '@/model/dms_permission';
import { DmsHospitalInfo } from '@/model/dms_hospital';

// 查询DMS用户访问授权列表
export function listDmsPermission(query: any) {
    return request({
        url: '/evms/dms_permission/list',
        method: 'get',
        params: query,
    });
}

// 根据用户id查询DMS用户访问授权（用户信息和通用权限）
export async function getDmsPermission(id: number) {
    return await Http.get<DmsUserInfo>('/evms/doctor_user/' + id);
}

// 新增DMS用户访问授权
export async function addDmsPermission(data: any) {
    return await Http.post<DmsUserInfo>('/evms/dms_permission', data);
}

// 修改DMS设备的用户访问授权
export async function updateDmsPermission(data: Partial<DmsUserInfo>) {
    return await Http.put<DmsUserInfo>('/evms/doctor_user', data);
}

// 删除DMS用户访问授权
export function delDmsPermission(id: any) {
    return request({
        url: '/evms/dms_permission/' + id,
        method: 'delete',
    });
}

// 根据用户ID查询设备列表
export async function listDeviceByUserId(query: any) {
    return await Http.get<UserDevice[]>('/evms/dms_device/getDeviceListByUserId', query);
}
// 查询该用户没有的设备列表
// export async function listDeviceNotInUserId(query: DmsUserDeviceQuery) {
//     return await Http.get<UserDevice[]>('/evms/dms_device/getDeviceListByNotUserId', query);
// }
// 批量新增用户设备
export async function addDeviceByUserId(data: AddDeviceUserParams) {
    return await Http.post<null>('/evms/dms_device/addUserDevice', data, false, 'form');
}
// 批量删除用户设备
export async function deleteDeviceByUserId(data: AddDeviceUserParams) {
    return await Http.post<null>('/evms/dms_device/deleteByUserIdAndDeviceIdsStr', data, false, 'form');
}
// 根据用户id和关键字查询医院
export async function getHospitalListByKeyAndUserId(data: { userId: number; keywords: string }) {
    return await Http.get<DmsHospitalInfo>('/evms/dms_hospital/getListByKeywordsAndUserId', data);
}
