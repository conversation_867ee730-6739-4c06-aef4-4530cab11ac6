<template>
    <div class="app-container">
        <el-card shadow="never" style="padding-right: 50px; border-radius: 12px">
            <el-form
                v-show="showSearch"
                :model="tableInfo.from"
                :inline="true"
                label-width="100"
                size="default"
            >
                <el-form-item label="ID" prop="id">
                    <el-input
                        v-model="tableInfo.from.id"
                        placeholder="请输入ID"
                        clearable
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="医院名称" prop="name">
                    <el-autocomplete
                        v-model="tableInfo.from.like_name"
                        :fetch-suggestions="querySearch"
                        clearable
                        placeholder="请输入医院名称"
                        @select="handleSelect"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="医院类型" prop="type">
                    <el-select
                        v-model="tableInfo.from.type"
                        placeholder="请选择医院类型"
                        clearable
                        style="width: 240px"
                    >
                        <el-option
                            v-for="dict in evms_dms_hospital_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
                <!-- 可折叠的高级搜索项 -->
                <template v-if="isExpand">
                    <el-form-item label="所在区域" prop="address">
                        <el-select
                            filterable
                            v-model="tableInfo.from.area"
                            placeholder="请选择所在区域"
                            clearable
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in areaList"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所在省份" prop="province">
                        <el-select
                            v-model="tableInfo.from.province"
                            filterable
                            placeholder="请选择所在省份"
                            clearable
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in provinceList"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所在城市" prop="city">
                        <el-select
                            v-model="tableInfo.from.city"
                            filterable
                            placeholder="请选择城市"
                            clearable
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in citysList"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                </template>

                <!-- 搜索按钮区域 -->
                <el-row justify="start" :gutter="20" style="width: 100%; margin-top: 10px">
                    <el-col :span="12" style="padding-left: 50px">
                        <el-button type="primary" icon="Search" @click="tableInfo.search">搜索</el-button>
                        <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                        <el-button type="text" @click="isExpand = !isExpand">
                            {{ isExpand ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <arrow-up v-if="isExpand" />
                                <arrow-down v-else />
                            </el-icon>
                        </el-button>
                    </el-col>
                </el-row>

                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        color="#009dff"
                        plain
                        icon="Plus"
                        @click="handleAdd"
                        v-hasPermi="['evms:dms_hospital:add']"
                    >新增
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        color="#01c064"
                        plain
                        icon="Edit"
                        :disabled="single"
                        @click="handleUpdate"
                        v-hasPermi="['evms:dms_hospital:edit']"
                    >修改
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        color="#ff5c00"
                        plain
                        icon="Delete"
                        :disabled="multiple"
                        @click="handleDelete"
                        v-hasPermi="['evms:dms_hospital:remove']"
                    >删除
                    </el-button>
                </el-col>
                <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
            </el-row>

            <el-table
                row-key="id"
                :data="tableInfo.list.value"
                height="450"
                style="margin-top: 20px"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="ID" align="center" prop="id" />
                <el-table-column label="医院名称" align="center" prop="name" />
                <el-table-column label="类型" align="center" prop="type">
                    <template #default="scope">
                        <dict-tag :options="evms_dms_hospital_type" :value="scope.row.type" />
                    </template>
                </el-table-column>
                <el-table-column label="所在区域" align="center" prop="area" />
                <el-table-column label="所在省份" align="center" prop="province" />
                <el-table-column label="所在城市" align="center" prop="city" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button
                            link
                            type="primary"
                            icon="Edit"
                            @click="handleUpdate(scope.row)"
                            v-hasPermi="['evms:dms_hospital:edit']"
                        >修改
                        </el-button>
                        <el-button
                            link
                            type="primary"
                            icon="Delete"
                            @click="handleDelete(scope.row)"
                            v-hasPermi="['evms:dms_hospital:remove']"
                        >删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-row justify="space-between" align="bottom">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    :total="tableInfo.total.value"
                    v-model:page="tableInfo.pageParam.pageNum"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
    </div>
</template>
<script lang="ts">
export default {
    name: 'Dms_hospital'
};
</script>
<script setup lang="ts">
import { delDmsHospital } from '@/api/evms/dms_hospital';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, watch } from 'vue';

import { getTableInfo } from '@/minix/tables';
import router from '@/router';
import { cityList } from '@/assets/constant/city';
import { DmsHospitalInfo } from '@/model/dms_hospital';
import { getHospitalListByKeywords } from '@/api/evms/dms_device';
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';

// 添加展开折叠控制
const isExpand = ref(false);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_hospital_type } = proxy!.useDict('evms_dms_hospital_type');
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
// const total = ref(0);
const title = ref('');

const data = reactive({});

const tableInfo = getTableInfo<DmsHospitalInfo>('/evms/dms_hospital/list');
tableInfo.load();

// 医院名称推荐选择
type HopitalItem = {
    value: string;
    id: number;
};
const handleSelect = (item: HopitalItem) => {
    console.log(item);
    tableInfo.from.like_name = item.value;
};
// 获取医院名称搜索结果

const querySearch = async (queryString: string, cb: any) => {
    const results = await getHospitalListByKeywords({ keywords: queryString });
    // call callback function to return suggestions
    if (results.code === 200) {
        console.log(results.data, '--1');
        cb(
            results?.data && (results.data as DmsHospitalInfo[]).length > 0
                ? (results.data as DmsHospitalInfo[])?.map((item: any) => {
                    return {
                        value: item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};

// 所在区域列表
const areaArr: any = [];
const provinceArr: any = [];
const citysArr: any = [];

cityList.forEach((item: any) => {
    areaArr.push({
        value: item.name,
        label: item.name
    });
    item.children &&
    item.children.forEach((sitem: any) => {
        provinceArr.push({
            value: sitem.name,
            label: sitem.name
        });
        sitem.children &&
        sitem.children.forEach((cItem: any) => {
            citysArr.push({
                value: cItem.name,
                label: cItem.name
            });
        });
    });
});
const areaList = ref<any[]>(areaArr);
// 所在省份列表
let provinceList = ref<any[]>(provinceArr);
// 城市列表
let citysList = ref<any[]>(citysArr);

watch(
    () => tableInfo.from.area,
    (val, oldValue) => {
        console.log(val, oldValue, '---val');
        if (val === oldValue) return;
        if (!val) {
            provinceList.value = provinceArr;
            citysList.value = citysArr;
        } else {
            const list: any[] = [];
            cityList.forEach((item: any) => {
                if (item.name === val) {
                    item.children &&
                    item.children.forEach((sitem: any) => {
                        list.push({
                            value: sitem.name,
                            label: sitem.name
                        });
                    });
                }
            });
            console.log(list, '---list');
            provinceList.value = list;
        }

        tableInfo.from.province = '';
        tableInfo.from.city = '';
    },
);
watch(
    () => tableInfo.from.province,
    (val, oldValue) => {
        if (val === oldValue) return;
        if (!val && !tableInfo.from.area) {
            citysList.value = citysArr;
            provinceList.value = provinceArr;
        } else {
            let list: any[] = [];
            cityList.forEach((item: any) => {
                if (!tableInfo.from.area || item.name === tableInfo.from.area) {
                    item.children &&
                    item.children.forEach((sitem: any) => {
                        if (sitem.name === val) {
                            sitem.children &&
                            sitem.children.forEach((cItem: any) => {
                                list.push({
                                    value: cItem.name,
                                    label: cItem.name
                                });
                            });
                        }
                    });
                }
            });
            citysList.value = list;
        }

        tableInfo.from.city = '';
    },
);

/** 重置按钮操作 */

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    tableInfo.reset();
    open.value = true;
    title.value = '添加DMS医院';
    router.push('/evms/dms/dms_hospital_info');
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    // tableInfo.reset();
    const _id = row.id || ids.value;
    router.push('/evms/dms/dms_hospital_info/' + _id);
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除DMS医院编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delDmsHospital(_ids);
        })
        .then(res => {
            if (res.code === 200) {
                // getList();
                proxy!.$modal.msgSuccess('删除成功');
                // 重置列表
                tableInfo.reset();
            }
        })
        .catch(() => {
        });
}

// getList();
</script>
<style scoped lang="scss">
.app-container {
    background: #f8f9fb;
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
