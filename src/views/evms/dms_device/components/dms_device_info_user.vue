<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { getTableInfo } from '@/minix/tables';
import { DeviceUserItem, DeviceUserPermission } from '@/model/dms_device';
import { addDeviceUser, delDeviceUser, updateDeviceUser } from '@/api/evms/dms_device';
import { ElMessage } from 'element-plus';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// 修改用户权限
const userPermission = ref<DeviceUserPermission>({} as DeviceUserPermission);
const editUserId = ref(-1);
const props = defineProps(['deviceId']);
const tableInfo = getTableInfo<DeviceUserItem>(
    '/evms/dms_device/getUserListByDeviceId',
    {
        id: props.deviceId
    },
    true,
    true
);
tableInfo.load();
const tableInfoAdd = getTableInfo<DeviceUserItem>('/evms/dms_device/getDoctorUserByNotDeviceId', {
    id: props.deviceId
});
// 删除是否禁用
const multiple = ref(true);
const ids = ref<number[]>([]);
const idsForAdd = ref<number[]>([]);

// 新增
const handleAdd = () => {
    open.value = true;
    tableInfoAdd.load();
};
const handleUpdate = (row: DeviceUserItem) => {
    console.log(row);
    userPermission.value = { ...row.permission! };
    editUserId.value = row.id;
    openEdit.value = true;
};
// 删除
const deleteBtnLoading = ref(false);
const handleDeleteSel = () => {
    if (ids.value.length) {
        deleteUser(ids.value.join(','));
    }
};
const handleDelete = async (row: any) => {
    deleteUser(row.id.toString());
};
// 删除用户
const deleteUser = async (id: string) => {
    if (!id) {
        ElMessage.error('请选择用户');
        return;
    }
    deleteBtnLoading.value = true;
    // 询问
    proxy!.$modal
        .confirm('是否确认删除DMS设备编号为"' + id + '"的数据项？')
        .then(function() {
            return delDeviceUser({
                deviceId: parseInt(props.deviceId),
                userIdsStr: id
            });
        })
        .then(res => {
            if (res.code === 200) {
                // getList();
                proxy!.$modal.msgSuccess('删除成功');
                // 重置列表
                tableInfo.reset();
            }
        })
        .catch(() => {
        });
    deleteBtnLoading.value = false;
};

// 列表选择
const handleSelectionChange = (selection: DeviceUserItem[]) => {
    ids.value = selection.map(item => item.id);
    multiple.value = !selection.length;
};
const handleSelectionChangeForAdd = (selection: DeviceUserItem[]) => {
    idsForAdd.value = selection.map(item => item.id);
};
const open = ref(false);
const openEdit = ref(false);

// 新增用户确认
const addBtnLoading = ref(false);
const submitForm = async () => {
    console.log('新增用户确认');
    if (!idsForAdd.value.length) {
        ElMessage.error('请选择用户');
        return;
    }
    addBtnLoading.value = true;
    const res = await addDeviceUser({
        id: parseInt(props.deviceId),
        userIdsStr: idsForAdd.value.join(',')
    });
    if (res.code === 200) {
        open.value = false;
        ElMessage.success('新增成功');
        tableInfo.reset();
        tableInfoAdd.reset();
        console.log('tabInof', tableInfo);
    } else {
        ElMessage.error(res.msg);
    }
    addBtnLoading.value = false;
};
// 新增用户取消
const cancel = () => {
    open.value = false;
    idsForAdd.value = [];
};
// 修改权限
const editBtnLoading = ref(false);
const submitEditForm = async () => {
    editBtnLoading.value = true;
    const res = await updateDeviceUser({
        deviceId: parseInt(props.deviceId),
        userId: editUserId.value,
        permission: JSON.stringify(userPermission.value)
    });
    if (res.code === 200) {
        openEdit.value = false;
        ElMessage.success('修改成功');
        tableInfo.reset();
        userPermission.value = {} as DeviceUserPermission;
        editUserId.value = -1;
    } else {
        ElMessage.error(res.msg);
    }
    editBtnLoading.value = false;
};
// 修改权限取消
const cancelEdit = () => {
    openEdit.value = false;
};
</script>

<template>
    <div class="dms_device_info_user">
        <el-form :model="tableInfo.from" size="default" :inline="true" label-width="60">
            <el-form-item label="ID">
                <el-input
                    style="width: 240px"
                    v-model="tableInfo.from.id"
                    placeholder="请输入ID"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-form-item label="用户名">
                <el-input
                    style="width: 240px"
                    v-model="tableInfo.from.like_username"
                    placeholder="请输入用户名"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-form-item label="姓名">
                <el-input
                    style="width: 240px"
                    v-model="tableInfo.from.like_realname"
                    placeholder="请输入姓名"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-form-item label="手机号">
                <el-input
                    style="width: 240px"
                    v-model="tableInfo.from.like_mobile"
                    placeholder="请输入手机号"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-row :gutter="30" justify="start" style="margin-top: 10px">
                <el-col :span="6">
                    <el-button type="primary" icon="Search" @click="tableInfo.search">搜索</el-button>
                    <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8" style="margin-top: 20px">
            <el-col :span="1.5">
                <el-button
                    color="#009dff"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:dms_hospital:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    color="#ff5c00"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDeleteSel"
                    v-hasPermi="['evms:dms_hospital:remove']"
                    :loading="deleteBtnLoading"
                >删除
                </el-button>
            </el-col>
            <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
        </el-row>
        <el-table
            row-key="id"
            :data="tableInfo.list.value"
            height="450"
            style="width: 100%; margin-top: 20px"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="ID" width="140"></el-table-column>
            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
            <el-table-column prop="realname" label="姓名" width="120"></el-table-column>
            <el-table-column prop="mobile" label="手机号"></el-table-column>
            <el-table-column prop="company" label="单位"></el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:dms_hospital:edit']"
                    >修改
                    </el-button>
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:dms_hospital:remove']"
                        :loading="deleteBtnLoading"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-row justify="space-between" align="bottom">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                :total="tableInfo.total.value"
                v-model:page="tableInfo.pageParam.pageNum"
                v-model:limit="tableInfo.pageParam.pageSize"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
    <!--    新增关联用户-->
    <el-dialog
        v-model="open"
        width="1156px"
        append-to-body
        modal-class="dms_device_info_user_dialog"
        draggable
    >
        <template #header>
            <div class="my-header">
                <div class="dialog_header">新增关联用户</div>
            </div>
        </template>
        <div>
            <el-form :model="tableInfoAdd.from" :inline="true" label-width="100">
                <el-row justify="space-between">
                    <el-col :span="8">
                        <el-form-item label="ID" prop="id">
                            <el-input
                                v-model="tableInfoAdd.from.id"
                                placeholder="请输入ID"
                                clearable
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="用户名" prop="userId">
                            <el-input
                                v-model="tableInfoAdd.from.like_username"
                                placeholder="请输入用户名"
                                clearable
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="姓名" prop="deviceId">
                            <el-input
                                v-model="tableInfoAdd.from.like_realname"
                                placeholder="请输入姓名"
                                clearable
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row justify="space-between">
                    <el-col :span="8">
                        <el-form-item label="手机号" prop="deviceId">
                            <el-input
                                v-model="tableInfoAdd.from.like_mobile"
                                placeholder="请输入手机号"
                                clearable
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="单位" prop="deviceId">
                            <el-input
                                v-model="tableInfoAdd.from.like_company"
                                placeholder="请输入单位"
                                clearable
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="科室" prop="deviceId">
                            <el-input
                                v-model="tableInfoAdd.from.like_department"
                                placeholder="请输入科室"
                                clearable
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row justify="start" :gutter="20" style="padding-left: 50px">
                    <el-button type="primary" icon="Search" @click="tableInfoAdd.search">搜索</el-button>
                    <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                </el-row>
                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
            <el-table
                :data="tableInfoAdd.list.value"
                height="350"
                style="width: 100%; margin-top: 20px"
                @selection-change="handleSelectionChangeForAdd"
            >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="id" label="ID" width="140"></el-table-column>
                <el-table-column prop="realname" label="姓名" width="120"></el-table-column>
                <el-table-column prop="username" label="用户名"></el-table-column>
                <el-table-column prop="company" label="所在医院"></el-table-column>
            </el-table>
            <el-row justify="space-between" align="bottom">
                <div>共 {{ tableInfoAdd.total }} 项数据</div>
                <pagination
                    v-show="tableInfoAdd.total.value > 0"
                    :total="tableInfoAdd.total.value"
                    v-model:page="tableInfoAdd.pageParam.pageNum"
                    v-model:limit="tableInfoAdd.pageParam.pageSize"
                    @pagination="tableInfoAdd.pagination"
                />
            </el-row>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="submitForm" :loading="addBtnLoading">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
    <!--    修改用户权限-->
    <el-dialog
        v-model="openEdit"
        width="561px"
        style="background-color: rgba(248, 249, 251, 1)"
        append-to-body
        modal-class="dms_device_info_user_edit"
        draggable
    >
        <template #header>
            <div class="my-header" style="background-color: #fff">
                <div class="dialog_edit_header">修改用户权限</div>
            </div>
        </template>
        <el-form :label-position="'left'" style="background-color: rgba(244, 244, 244, 1)">
            <el-row :gutter="1" style="margin-bottom: 1px">
                <el-col :span="12">
                    <el-form-item label="在线会议" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.meeting"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实时视频" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.realplay"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="1" style="margin-bottom: 1px">
                <el-col :span="12">
                    <el-form-item label="回放视频" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.backplay"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="下载视频" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.download"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="1" style="margin-bottom: 1px">
                <el-col :span="12">
                    <el-form-item label="对讲" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.talk"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="远程控制" prop="realname" style="width: 100%">
                        <el-switch
                            v-model="userPermission.control"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="1" style="margin-bottom: 8px">
                <el-col :span="12">
                    <el-form-item label="视频水印" prop="watermark" style="width: 100%">
                        <el-switch
                            v-model="userPermission.watermark"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="12" style="width: 100%; display: flex; justify-content: flex-start">
                    <div class="el-form-item__label"></div>
                    <div class="el-form-item__content" style="flex: 1"></div>
                </el-col>
            </el-row>
            <el-row>
                <el-col class="last_item">
                    <el-form-item
                        label="摄像头视频可见"
                        prop="watermark"
                        style="width: 100%; background-color: #fff"
                    >
                        <el-radio-group v-model="userPermission.visibleState">
                            <!-- works when >=2.6.0, recommended ✔️ not work when <2.6.0 ❌ -->
                            <el-radio :label="1">可见</el-radio>
                            <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
                            <el-radio :label="0">不可见</el-radio>
                            <el-radio :label="2">以设备设置为准</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelEdit">取 消</el-button>
                <el-button type="primary" @click="submitEditForm" :loading="editBtnLoading">确 定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss">
.dms_device_info_user_dialog,
.dms_device_info_user_edit {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}

.dms_device_info_user_edit {
    .el-dialog {
        border-radius: 20px;
        border: 1px solid rgba(204, 204, 204, 0.8);
        background: #f8f9fb;
        box-shadow: 10px 10px 20px 0 rgba(0, 0, 0, 0.15);
        overflow: clip;
    }

    .dialog_edit_header {
        padding: 6.5px 0;
        display: flex;
        justify-content: center;
        color: #333;
        text-align: center;
        font-size: 14px;
        background-color: #fff;
    }

    .el-dialog__headerbtn {
        top: 4.5px;
    }

    .el-form-item__content {
        justify-content: end;
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-form-item {
        height: 53px;
        padding: 0;

        &:last-child {
            border-bottom: none;
        }
    }

    .el-form-item--default {
        margin: 0;
    }

    .el-form-item__label {
        color: #666;
        font-size: 14px;
        line-height: 150%; /* 21px */
        height: 100%;
        width: 120px;
        padding-left: 30px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        background-color: rgba(250, 250, 250, 1);
    }
    .last_item .el-form-item__label {
        width: 172px;
    }
    .el-form-item__content {
        height: 100%;
        padding-right: 30px;
        background-color: rgba(248, 249, 251, 1);
    }

    .el-switch {
        height: 18px;
    }

    .dialog-footer {
        padding-top: 30px;
        display: flex;
        justify-content: center;

        .el-button {
            padding: 5px 36px;
            border-radius: 15px;
        }
    }
}
</style>
