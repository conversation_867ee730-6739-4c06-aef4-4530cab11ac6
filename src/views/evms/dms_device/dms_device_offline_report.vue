<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="ID" prop="id">
                <el-input
                    v-model="queryParams.id"
                    placeholder="请输入id"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item label="填写人" prop="writer">
                <el-input
                    v-model="queryParams.writer"
                    placeholder="请输入填写人姓名"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item label="填写时间" prop="writeTime">
                <el-date-picker
                    clearable
                    v-model="queryParams.writeTime"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择填写时间"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>

                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:elabpatrolreport:add']"
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:elabpatrolreport:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:elabpatrolreport:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="elabofflinereportList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" width="150" prop="id" />

            <el-table-column label="填写人" align="center" prop="writer" />

            <el-table-column label="所在办事处" align="center" prop="office" />

            <el-table-column label="填写时间" align="center" prop="createTime">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:elabpatrolreport:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:elabpatrolreport:remove']"
                        link
                        type="danger"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Elabofflinereport" lang="ts">
import {
    listElabpatrolreport,
    addElabpatrolreport,
    updateElabpatrolreport,
    delElabpatrolreport
} from '@/api/cl/elabpatrolreport';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const elabofflinereportList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const route = useRoute();
const router = useRouter();
const cathLabTitle = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        cathLab_id: undefined,
        device_id: undefined,
        writer: undefined,
        office: undefined,
        result: undefined,
        inspectTime: undefined,
        writeTime: undefined,
        record: undefined,
        type: 'D',
        network: undefined
    },
    rules: {
        result: [{ required: true, message: '巡检结果不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询线下巡检报告列表 */
function getList() {
    loading.value = true;
    listElabpatrolreport(queryFormat(queryParams.value)).then((response: any) => {
        elabofflinereportList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 关闭按钮 */
function close() {
    console.log('返回');
    router.back();
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    const deviceId = route.params.id;
    const id = 0;
    router.push({ path: '/evms/dms/dms_device_offline_report_info/' + deviceId + '/' + id });
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    const id = row.id || ids.value;
    const deviceId = route.params.id;
    router.push({ path: '/evms/dms/dms_device_offline_report_info/' + deviceId + '/' + id });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['elabofflinereportRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateElabpatrolreport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addElabpatrolreport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除线下巡检报告编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delElabpatrolreport(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

function getCLElabOfflineReports(id: any) {
    if (id) {
        queryParams.value.device_id = id;
    }
    getList();
}

getCLElabOfflineReports(route.params.id);
</script>
