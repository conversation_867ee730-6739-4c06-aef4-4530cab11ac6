import { defineStore } from 'pinia';
import { ls, ss } from '@/vue-ls';
// console.log(ls.get('LOCAL_CONFIG').evmsOrdertQuery)
import { commonType, LOCAL_CONFIG, SESSION_CONFIG, commonLocal, commonSession } from '../mutation_type';

const useCommonStore = defineStore('common', {
    state: (): commonType.commonLocal => ({
        local: commonLocal,
        session: commonSession,
    }),
    actions: {
        setLocal(obj: { name: string; status: any }) {
            this.local[obj.name] = obj.status;
            ls.set(LOCAL_CONFIG, this.local);
        },
        setLocalConfig(local: commonType.commonLocal) {
            this.local = local;
            ls.set(LOCAL_CONFIG, local);
        },
        setSession(obj: { name: string; status: any }) {
            this.session[obj.name] = obj.status;
            ss.set(SESSION_CONFIG, this.session);
        },
        setSessionConfig(session: commonType.commonLocal) {
            this.session = session;
            ss.set(SESSION_CONFIG, session);
        },
    },
    getters: {
        getLocalKey: state => (key: string) => state.local[key],
        getSessionKey: state => (key: string) => state.session[key]
    },
});

export default useCommonStore;
