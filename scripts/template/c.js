import {
    createDaoType,
    createEdit,
    createList,
    createSearch,
    createSlots,
    getColumnName,
    javaTypeEnum,
    typeEnum
} from './createTemplate.js';

export function createPage(genTable) {
    if (!genTable || !genTable.columns) return '';
    let BusinessName = genTable.BusinessName || '';
    let businessName = genTable.businessName || '';
    genTable.columns.map(item => (item.columnName = getColumnName(item.columnComment)));
    let isBetweenTime = genTable.columns.find(
        column => column.htmlType === 'datetime' && column.queryType === 'BETWEEN'
    );
    let formObject = genTable.columns.reduce((sub, column) => {
        if (column.htmlType === typeEnum.checkbox) {
            sub += `${column.javaField}: [],
`;
        } else {
            sub += `${column.javaField}: undefined as any,
`;
        }
    }, '');
    let pkField = 'id';
    let functionName = genTable.functionName;
    return `
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from "element-plus";
import { defineComponent, getCurrentInstance, ref, reactive,toRefs } from "vue";
import RightToolar from "@/components/RightToolbar/index.vue"
import ImagePreview from "@/components/ImagePreview/index.vue"
import DictTag from "@/components/DictTag/index.vue"
import moment from "moment"
import { queryFormat, parseTime } from '@/utils/ruoyi';
interface ${genTable.className} {
    ${createDaoType(genTable.columns)}
}
interface QueryParams extends Partial<${genTable.className}> {
    pageNum:number,
    pageSize:number,
    name:any
}
export default defineComponent({
    setup() {
        const { proxy } = getCurrentInstance()!
        ${genTable.dicts ? `const { ${genTable.dicts.replaceAll('\'', '')} } = proxy?.useDict(${genTable.dicts})!;` : ''}
        const state = reactive({
            ${businessName}List:[] as ${genTable.className}[],
            open:false,
            loading:true,
            showSearch:true,
            ids:[],
            single:true,
            total:0,
            title:"",
            daterange:[] as string[],
            form: {} as unknown as ${genTable.className},
            queryParams:{
                pageNum: 1,
                pageSize: 10,
                name: undefined,
                ${genTable.columns.reduce((sub, column, index) => {
        let taps = '                ';
        if (column.isQuery == 1) {
            if (sub.length > 0) {
                sub += taps;
            }
            sub += `${column.javaField}:${javaTypeEnum[column.javaType] === 'string' ? '""' : 'null as unknown as number'},
`;
        }
        return sub;
    }, '')}
            } as QueryParams,
            rules: {
                ${genTable.columns.reduce((sub, column, index) => {
        if (column.isRequired == 1) {
            if (index !== 0 && sub.length > 0) {
                sub += ',\r\n                ';
            }
            sub += `${column.javaField}:[{ required: true, message: "${column.columnName}不能为空", trigger: ${column.htmlType == 'select' || column.htmlType == 'radio' ? '"change"' : '"blur"'}}]`;
        }
        if (index === genTable.columns.length - 1) {
            sub += 'as any[]';
        }
        return sub;
    }, '')}
            }
        })
        const ${businessName}List = ref<${genTable.className}[]>([]);
        const open = ref(false);
        const loading = ref(true);
        const showSearch = ref(true);
        const ids = ref<any[]>([]);
        const single = ref(true);
        const multiple = ref(true);
        const total = ref(0);
        const title = ref("");
        ${isBetweenTime ? 'const daterange = ref<any[]>([]);' : ''}
        const data = reactive<{
            form: ${genTable.className};
            queryParams: QueryParams;
            rules: any;
        }>({
            form: {} as unknown as ${genTable.className},
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: undefined,
                ${genTable.columns.reduce((sub, column, index) => {
        let taps = '                ';
        if (column.isQuery == 1) {
            if (index !== 0) {
                sub += taps;
            }
            sub += `${column.javaField}:${javaTypeEnum[column.javaType] === 'string' ? '""' : 'null as unknown as number'},
`;
        }
        return sub;
    }, '')}
            }
        });
        const { queryParams, form, rules } = toRefs(data);
        function getList() {
            loading.value = true;
            ${
        isBetweenTime
            ? 'List' +
            BusinessName +
            '((proxy?.addDateRange(queryParams.value, dateRange.value)).then((response:any)=>{'
            : 'List' + BusinessName + 'queryFormat(queryParams.value).then((response:any)=>{'
    }
                ${businessName}List.value = response.data.rows;
                total.value = response.data.total;
                loading.value = false;
            })
        }
        function cancel() {
            open.value = false;
            reset();
        }
        // 重置区域
        function reset() {
            form.value = {} as unknown as ${genTable.className}
            proxy?.resetForm("${businessName}Ref");
        }
        function handleQuery() {
            queryParams.value.pageNum = 1;
            getList();
        }
        function resetQuery() {
            ${isBetweenTime ? 'daterange.value = []' : ''}
            proxy?.resetForm('queryRef');
            handleQuery()
        }
        function handleSelectionChange(selection: any[]) {
            ids.value = selection.map(item => item.${pkField});
            single.value = selection.length !== 1;
            multiple.value = !selection.length;
        }
        function handleAdd() {
            reset();
            open.value = true;
            title.value = "添加${functionName}";
        }
        function handleUpdate(row: any) {
            reset();
            const _${pkField} = row.${pkField} || ids.value
            get${BusinessName}(_${pkField}).then((response: any) => {
                form.value = response.data;
                ${genTable.columns.reduce((sub, column) => {
        if (column.htmlType === typeEnum.checkbox) {
            sub += `form.value.${column.javaField} = form.value.${column.javaField}.split(",");
`;
        }
        return sub;
    }, '')}
                open.value = true;
                title.value = "修改${functionName}";
            })
        }
        function submitForm() {
            (proxy?.$refs["${BusinessName}Ref"] as any).validate((valid: boolean) => {
                if (valid) {
                    ${genTable.columns.reduce((sub, column) => {
        if (column.htmlType === typeEnum.checkbox) {
            sub += `form.value.${column.javaField} = form.value.${column.javaField}.join(",");
`;
        }
        return sub;
    }, '')}
                    if (form.value.${pkField} != null) {
                        update${BusinessName}(form.value).then(() => {
                            proxy?.$modal.msgSuccess("修改成功");
                            open.value = false;
                            getList();
                        });
                    } else {
                        add${BusinessName}(form.value).then(() => {
                            proxy?.$modal.msgSuccess("新增成功");
                            open.value = false;
                            getList();
                        });
                    }
                }
            });
        }
        function handleDelete(row: ${genTable.className}) {
            const _${pkField}s = row.${pkField} || ids.value;
            proxy?.$modal.confirm("是否确认删除${functionName}编号为" + _${pkField}s + "的数据项？").then(()=> {
                return del${BusinessName}(_${pkField}s);
            }).then(() => {
                getList();
                proxy?.$modal.msgSuccess("删除成功");
            }).catch((e) => {console.log(e)});
        }
        function handleExport() {
            proxy?.download('${genTable.moduleName}/${businessName}/exportExcel', {
                ...queryFormat(queryParams.value)
            }, \`${businessName}_\${new Date().getTime()}}#.xlsx\`);
        }
        const slots = {
            ${createSlots(genTable.columns, genTable.className) || ''},
            actionSlots(record:${genTable.className}){
                return <>
                    <ElButton
                        v-hasPermi="['${genTable.moduleName}:${businessName}:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        onClick={()=>handleUpdate(record)}
                    >
                        修改
                    </ElButton>
                    <ElButton
                        v-hasPermi="['${genTable.moduleName}:${businessName}:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        onClick={()=>handleDelete(record)}
                    >
                        删除
                    </ElButton>
                </>
            }
        }
        getList();
        return () => (
            <div class="app-container">
                {/* 查询区域 */}
                <ElForm
                    v-show={showSearch}
                    ref="queryRef"
                    model={queryParams}
                    inline={true}
                >
                    ${createSearch(genTable.columns)}
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={()=>handleQuery}>搜索</ElButton>
                        <ElButton icon="Refresh" onClick={()=>resetQuery}>重置</ElButton>
                    </ElFormItem>
                </ElForm >
                {/* 编辑区域 */}

                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi="['sys:test:add']"
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={()=>handleAdd}
                        >新增
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi="['sys:test:edit']"
                            type="success"
                            plain
                            icon="Edit"
                            disabled={single.value}
                            onClick={()=>handleUpdate}
                        >修改
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi="['sys:test:remove']"
                            type="danger"
                            plain
                            icon="Delete"
                            disabled={multiple.value}
                            onClick={()=>handleDelete}
                        >删除</ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi="['sys:test:export']"
                            type="warning"
                            plain
                            icon="Download"
                            onClick={()=>handleExport}
                        >导出</ElButton>
                    </ElCol >
                    <RightToolar v-model={[showSearch, "showSearch"]} onQueryTable="getList"></RightToolar>
                </ElRow >
                {/* 表格区域 */}
                <ElTable v-loading="loading" data={${businessName}List.value} onSelection-change={handleSelectionChange}>
                    <ElTableColumn
                        type="selection"
                        width="55"
                        align="center"
                    />
                    ${createList(genTable.columns)}
                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    >
                    </ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <ElPagination
                    v-show={total.value>0}
                    v-model={[queryParams.value.pageNum,"apge"]}
                    v-model:limit={[queryParams.value.pageSize,"limit"]}
                    total={total.value}
                    onCurrent-change={getList}
                />
                {/* 添加model 区域 */}
                <ElDialog
                    v-model={[open,"value"]}
                    title={title.value}
                    width="500px"
                    appendToBody
                >
                    <ElForm
                        ref="Ref"
                        model={form}
                        rules={rules.value}
                        labelWidth="80px"
                    >
                    ${createEdit(genTable.columns)}
                    </ElForm>
                </ElDialog>
            </div >
        )
    }
})`;
}
