<template>
    <div class="app-container">
        <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="导管室基础信息" name="CLCathLabInfo"
            >{{ cathLabTitle }}--基础信息
            </el-tab-pane
            >
            <el-tab-pane label="导管室设备信息" name="CLEquipment">{{ cathLabTitle }}--设备信息</el-tab-pane>
            <el-tab-pane label="导管室联系信息" name="CLContactInfo"
            >{{ cathLabTitle }}--联系信息
            </el-tab-pane
            >
            <el-tab-pane label="导管室合作信息" name="CLCooperate">{{ cathLabTitle }}--合作信息</el-tab-pane>
        </el-tabs>

        <!-- 添加或修改导管室联系信息对话框 -->
        <el-form v-show="open" ref="contactinfoRef" :model="form" :rules="rules" label-width="200px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="合作专家/医生" prop="cooperator">
                        <el-input v-model="form.cooperator" placeholder="请输入合作专家/医生" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="联系方式" prop="coopContact">
                        <el-input v-model="form.coopContact" placeholder="请输入联系方式" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="对接医生" prop="dockingDoctor">
                        <el-input v-model="form.dockingDoctor" placeholder="请输入对接医生" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="联系方式" prop="dockContact">
                        <el-input v-model="form.dockContact" placeholder="请输入联系方式" />
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="主任办公室" prop="office">
                        <el-input v-model="form.office" placeholder="请输入主任办公室地址" />
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="辐射中心" prop="radiationCenter">
                        <el-input v-model="form.radiationCenter" placeholder="请输入辐射中心" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-button type="primary" @click="submitForm">保 存</el-button>
            <el-button @click="cancel">取 消</el-button>
        </el-form>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Contactinfo" lang="ts">
import {
    listContactinfo,
    getContactinfo,
    delContactinfo,
    addContactinfo,
    updateContactinfo
} from '@/api/cl/contactinfo';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { getCathlab } from '@/api/cl/cathlab';
import { useRouter } from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const open = ref(false);
const route = useRoute();
const router = useRouter();
const cathLabTitle = ref('');
const activeName = ref('CLContactInfo');

const data = reactive<{
    form: any;
    rules: any;
}>({
    form: {},
    rules: {}
});

const { form, rules } = toRefs(data);

/** 取消按钮 */
function cancel() {
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        cathId: undefined,
        cooperator: undefined,
        coopContact: undefined,
        office: undefined,
        dockingDoctor: undefined,
        dockContact: undefined,
        radiationCenter: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('contactinfoRef');
}

/** 关闭按钮 */
function close() {
    const obj = { path: '/cl/cathlab', name: 'Cathlab' };
    proxy!.$tab.closeOpenPage(obj).then(() => {
        // 页面关闭后，刷新页面
        proxy!.$tab.refreshPage(obj);
    });
}

function handleClick(tab: any) {
    const cathId = Number(route.params.cathId);
    if ('CLCathLabInfo' == tab.props.name) {
        router.push('/cl/cath-lab/cathLabInfo/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    } else if ('CLEquipment' == tab.props.name) {
        router.push('/cl/cath-lab/equipment/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    } else if ('CLCooperate' == tab.props.name) {
        router.push('/cl/cath-lab/cooperate/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    }
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['contactinfoRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateContactinfo(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('修改成功');
                    form.value = response.data;
                    open.value = true;
                });
            } else {
                if (Number(route.params.cathId) === 0) {
                    proxy?.$modal.msgError('请先添加导管室数据');
                    reset();
                    open.value = true;
                } else {
                    addContactinfo(form.value).then((response: any) => {
                        proxy?.$modal.msgSuccess('新增成功');
                        form.value = response.data;
                        open.value = true;
                    });
                }
            }
        }
    });
}

function getCLContactInfos(cathId: any) {
    if (cathId && cathId != 0) {
        getCathlab(cathId).then((response: any) => {
            cathLabTitle.value = response.data.name;
        });
        listContactinfo(queryFormat({ cathLab_id: cathId })).then((response: any) => {
            if (!response.data.rows[0]) {
                reset();
            } else {
                form.value = response.data.rows[0];
            }
            form.value.cathId = cathId;
            open.value = true;
        });
    } else {
        reset();
        open.value = true;
    }
}

getCLContactInfos(route.params && route.params.cathId);
</script>
