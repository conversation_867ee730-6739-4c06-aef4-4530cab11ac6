{"org.parent": "上级组织", "org.name": "名称", "org.address": "地址", "org.phone": "电话", "org.contacts": "联系人", "org.order": "顺序", "group.name": "名称", "group.description": "描述", "group.allAccessPermission": "所有浏览权限", "group.allAccessPermission.tooltip": "是否拥有所有文章的浏览权限权限", "group.type": "类型", "group.type.1": "系统组", "group.type.2": "常规组", "group.type.3": "IP组", "group.order": "顺序", "role.name": "名称", "role.description": "描述", "role.globalPermission": "全局数据", "role.globalPermission.tooltip": "是否拥有全局数据的管理权限。如：所有的组织、所有的用户和全局共享的数据", "role.allArticlePermission": "所有文章数据", "role.allArticlePermission.tooltip": "是否拥有所有文章的数据权限。不受数据权限范围限制", "role.allChannelPermission": "所有栏目数据", "role.allChannelPermission.tooltip": "是否拥有所有栏目的数据权限", "role.dataScope": "数据权限范围", "role.dataScope.tooltip": "在数据权限的范围内（如文章权限），进一步控制权限，拥有所有数据、本组织数据或自身数据的权限", "role.dataScope.1": "所有", "role.dataScope.2": "本组织", "role.dataScope.3": "自身", "role.allPermission": "所有功能权限", "role.permission": "功能权限", "role.permission.tooltip": "超级管理员(ID为1)拥有所有权限", "role.allGrantPermission": "所有授权权限", "role.grantPermission": "授权权限", "role.grantPermission.tooltip": "新增或修改角色时，可授权的权限。超级管理员(ID为1)拥有所有权限", "role.dataPermission": "数据权限", "role.articlePermission": "文章权限", "role.channelPermission": "栏目权限", "role.rank": "等级", "role.rank.tooltip": "数值越小等级越高。用户只能被授予同等级或更低等级的角色", "role.type": "类型", "role.type.tooltip": "系统管理员、安全管理员、审计管理员为三员管理员", "role.type.1": "系统管理员", "role.type.2": "安全管理员", "role.type.3": "审计管理员", "role.type.4": "常规角色", "role.scope": "共享范围", "role.scope.0": "本站私有", "role.scope.1": "子站点共享", "role.scope.2": "全局共享", "role.order": "顺序", "role.error.scopeNotAllowd": "数据已被其它站点使用，不允许设置为本站私有", "role.error.rankHigherThenCurrentUser": "等级高于当前用户", "user.op.status": "状态修改为", "user.username": "用户名", "user.group": "用户组", "user.org": "组织", "user.role": "角色", "user.role.tooltip": "角色等级数值越小等级越高。用户只能被授予同等级或更低等级的角色", "user.origPassword": "原密码", "user.newPassword": "新密码", "user.plainPassword": "密码", "user.passwordAgain": "确认密码", "user.email": "电子邮箱", "user.mobile": "手机号", "user.avatar": "头像", "user.rank": "等级", "user.rank.tooltip": "数值越小等级越高。等级高的用户可以管理等级低的用户，等级低的用户不能管理等级高的用户", "user.status": "状态", "user.status.0": "正常", "user.status.1": "未激活", "user.status.2": "已锁定", "user.status.3": "已注销", "user.error.passwordLength": "密码长度为{min}-{max}个字符", "user.error.passwordPattern.0": "密码可以是任意字符", "user.error.passwordPattern.1": "密码中必须包含字母、数字", "user.error.passwordPattern.2": "密码中必须包含大写字母、小写字母、数字", "user.error.passwordPattern.3": "密码中必须包含字母、数字、特殊字符", "user.error.passwordPattern.4": "密码中必须包含大写字母、小写字母、数字、特殊字符", "user.realName": "真实姓名", "user.created": "创建日期", "user.gender": "性别", "user.birthday": "出生日期", "user.location": "居住地", "user.bio": "自我介绍", "user.loginDate": "最后登录日期", "user.loginIp": "最后登录IP", "user.loginCount": "登录次数", "user.errorDate": "登录错误日期", "user.errorCount": "登录错误次数", "user.error.usernameExist": "用户名已存在", "user.error.emailExist": "邮箱地址已存在", "user.error.mobileExist": "手机号码已存在", "user.error.passwordNotMatch": "两次输入密码不一致", "user.error.passwordWrong": "密码错误", "": ""}