import request from '@/utils/request';

// 查询省市管理列表
export function listArea(query: any) {
    return request({
        url: '/dataset/area/list',
        method: 'get',
        params: query,
    });
}

// 查询省市管理详细
export function getArea(id: any) {
    return request({
        url: '/dataset/area/' + id,
        method: 'get',
    });
}

// 新增省市管理
export function addArea(data: any) {
    return request({
        url: '/dataset/area',
        method: 'post',
        data: data,
    });
}

// 修改省市管理
export function updateArea(data: any) {
    return request({
        url: '/dataset/area',
        method: 'put',
        data: data,
    });
}

// 删除省市管理
export function delArea(id: any) {
    return request({
        url: '/dataset/area/' + id,
        method: 'delete',
    });
}

/**
 * 获取下属城市
 */
export function children(parentId: any, name: any) {
    const data = {
        parentId: parentId,
        name: name,
    };
    return request({
        url: '/dataset/area/children',
        method: 'get',
        params: data,
    });
}
