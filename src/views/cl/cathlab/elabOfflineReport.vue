<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button disabled>{{ cathLabTitle }}--E-lab线下巡检报告</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:elabpatrolreport:add']"
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:elabpatrolreport:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:elabpatrolreport:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="elabofflinereportList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" width="150" prop="id" />

            <el-table-column label="填写人" align="center" prop="writer" />

            <el-table-column label="所在办事处" align="center" prop="office" />

            <el-table-column label="填写时间" align="center" prop="createTime">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>

            <!--
            <el-table-column label="医院" align="center" prop="cathLab.hospital.name"/>

            <el-table-column label="院区" align="center" prop="cathLab.hospital.courtyard"/>

            <el-table-column label="省/直辖市" align="center" prop="cathLab.hospital.province.name"/>

            <el-table-column label="市/区" align="center" prop="cathLab.hospital.city.name"/>

            <el-table-column label="区域" align="center" prop="cathLab.hospital.area"/>

-->

            <el-table-column label="导管室地址" align="center" prop="cathLab.address" />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:elabpatrolreport:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:elabpatrolreport:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改线下巡检报告对话框 -->
        <el-dialog v-model="open" :title="title" width="800px" append-to-body>
            <el-form ref="elabofflinereportRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="填写人" prop="writer">
                            <el-input v-model="form.writer" placeholder="请输入填写人" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="医院" prop="hospital">
                            <el-input v-model="form.hospital" disabled />
                        </el-form-item>
                    </el-col>

                    <!--                    <el-col :span="12">
                        <el-form-item label="填写时间" prop="writeTime">
                            <el-date-picker clearable
                                            v-model="form.writeTime"
                                            type="datetime"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                            placeholder="请选择填写时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>-->

                    <el-col :span="12">
                        <el-form-item label="所在办事处" prop="office">
                            <el-input v-model="form.office" placeholder="请输入办事处" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="院区" prop="courtyard">
                            <el-input v-model="form.courtyard" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="巡检日期" prop="inspectTime">
                            <el-date-picker
                                clearable
                                v-model="form.inspectTime"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="请选择巡检日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="省市" prop="provinceCity">
                            <el-input v-model="form.provinceCity" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="网络测试情况" prop="network">
                            <el-input v-model="form.network" placeholder="请输入网络测试情况" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="地址" prop="address">
                            <el-input v-model="form.address" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="巡检结果" prop="result">
                            <el-input v-model="form.result" placeholder="请输入巡检结果" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="区域" prop="area">
                            <el-input v-model="form.area" disabled />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="巡检工作记录" prop="record">
                            <el-input
                                type="textarea"
                                :rows="3"
                                v-model="form.record"
                                placeholder="请输入巡检工作记录"
                            />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="elab画面" prop="elabImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.elabImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="ebs画面" prop="ebsImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.ebsImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="脑医汇logo" prop="logo">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.logo"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Elabofflinereport" lang="ts">
import {
    listElabpatrolreport,
    getElabpatrolreport,
    addElabpatrolreport,
    updateElabpatrolreport,
    delElabpatrolreport
} from '@/api/cl/elabpatrolreport';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import ImageUpload from '@/components/ImageUpload/index.vue';
import { getCathlab, getHospitalById } from '@/api/cl/cathlab';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const elabofflinereportList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const route = useRoute();
const cathLabTitle = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cathLab_id: undefined,
        office: undefined,
        result: undefined,
        inspectTime: undefined,
        record: undefined,
        type: 'D',
        network: undefined,
    },
    rules: {
        result: [{ required: true, message: '巡检结果不能为空', trigger: 'blur' }]
    },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询线下巡检报告列表 */
function getList() {
    loading.value = true;
    listElabpatrolreport(queryFormat(queryParams.value)).then((response: any) => {
        elabofflinereportList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 关闭按钮 */
function close() {
    const obj = { path: '/cl/cathlab' };
    proxy!.$tab.closeOpenPage(obj);
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        cathId: undefined,
        writer: undefined,
        writeTime: undefined,
        office: undefined,
        result: undefined,
        inspectTime: undefined,
        record: undefined,
        elabImage: undefined,
        ebsImage: undefined,
        network: undefined,
        logo: undefined,
        type: 'D',
        status: undefined,
        fault: undefined,
        reason: undefined,
        solveTime: undefined,
        hospital: undefined,
        courtyard: undefined,
        provinceCity: undefined,
        address: undefined,
        area: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('elabofflinereportRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    form.value.cathId = queryParams.value.cathLab_id;
    getCathlab(form.value.cathId).then((response: any) => {
        form.value.courtyard = response.data.courtyard;
        form.value.provinceCity = response.data.provinceCity;
        form.value.address = response.data.address;
        form.value.area = response.data.area;
        if (response.data.hospitalId) {
            getHospitalById(response.data.hospitalId).then((response: any) => {
                if (response.data) {
                    form.value.hospital = response.data[0].name;
                }
            });
        }
    });
    form.value.type = 'D';
    open.value = true;
    title.value = '添加线下巡检报告';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getElabpatrolreport(_id).then((response: any) => {
        form.value = response.data;
        if (response.data.hospitalId) {
            getHospitalById(response.data.hospitalId).then((response: any) => {
                if (response.data) {
                    form.value.hospital = response.data[0].name;
                }
            });
        }
        open.value = true;
        title.value = '修改线下巡检报告';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['elabofflinereportRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateElabpatrolreport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addElabpatrolreport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除线下巡检报告编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delElabpatrolreport(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy?.download(
        'cl/elabofflinereport/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `elabofflinereport_${new Date().getTime()}#.xlsx`
    );
}

function getCLElabOfflineReports(cathId: any) {
    if (cathId) {
        queryParams.value.cathLab_id = cathId;
        getCathlab(cathId).then((response: any) => {
            cathLabTitle.value = response.data.name;
        });
    }
    getList();
}

getCLElabOfflineReports(route.params && route.params.cathId);
</script>
