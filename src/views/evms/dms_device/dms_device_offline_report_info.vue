<script setup lang="ts">
import { ref } from 'vue';
import Dms_device_offline_report_base from '@/views/evms/dms_device/components/dms_device_offline_report_base.vue';
import { useRoute } from 'vue-router';

const activeName = ref('base');
const route = useRoute();

const reportId = ref(route.params.id);
const deviceId = ref(route.params.deviceId);
</script>

<template>
    <div class="dms_device_offline_report_base">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" type="card" class="demo-tabs">
                <el-tab-pane label="线下巡检报告" name="base">
                    <dms_device_offline_report_base />
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<style scoped lang="scss">
.dms_device_offline_report_base {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;

    :deep(.el-card) {
        height: 100%;
    }

    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }

    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }

    :deep(.el-tab-pane) {
        height: 100% !important;
    }

    :deep(.el-form) {
        height: 100% !important;
        overflow-y: scroll;

        &::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0px 0px 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0px 0px 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        height: 40px !important;
    }

    .form-con {
        height: 100%;
    }
}
</style>
