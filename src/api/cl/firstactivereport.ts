import request from '@/utils/request';

// 查询首次激活报告列表
export function listFirstactivereport(query: any) {
    return request({
        url: '/cl/first/active/report/list',
        method: 'get',
        params: query,
    });
}

// 查询首次激活报告详细
export function getFirstactivereport(id: any) {
    return request({
        url: '/cl/first/active/report/' + id,
        method: 'get',
    });
}

// 新增首次激活报告
export function addFirstactivereport(data: any) {
    return request({
        url: '/cl/first/active/report',
        method: 'post',
        data: data,
    });
}

// 修改首次激活报告
export function updateFirstactivereport(data: any) {
    return request({
        url: '/cl/first/active/report',
        method: 'put',
        data: data,
    });
}

// 删除首次激活报告
export function delFirstactivereport(id: any) {
    return request({
        url: '/cl/first/active/report/' + id,
        method: 'delete',
    });
}

// 查询首次激活报告详细
export function getFirstActiveReportByDeviceId(deviceId: any) {
    const data = {
        deviceId: deviceId
    };
    return request({
        url: '/cl/first/active/report/getFirstActiveReportByDeviceId',
        method: 'get',
        params: data,
    });
}
