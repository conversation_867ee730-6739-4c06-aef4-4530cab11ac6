<script lang="ts">
export default {
    name: 'ConsultationHospitalMember'
};
</script>

<script lang="ts" setup>
import { getTableInfo } from '@/minix/tables';
import { ConsultationMember, Device } from '@/model/consultation';
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { DeviceUserItem } from '@/model/dms_device';
import { oc_deleteGroupMem, oc_addGroupMem, GroupMemberData } from '@/api/evms/oc_consultation';

interface BaseApiResponse {
    code: number;
    msg?: string;
}

interface ApiResponse<T = any> extends BaseApiResponse {
    data: T;
}

const props = defineProps<{
    groupId: string;
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const tableInfo = getTableInfo<ConsultationMember>('/oc/group/listMember', {
    groupId: props.groupId
});
tableInfo.load();

const ids = ref<string[]>([]);
const multiple = ref(true);

// 多选框选中数据
function handleSelectionChange(selection: ConsultationMember[]) {
    ids.value = selection.map(item => item.id);
    multiple.value = !selection.length;
}

// 删除
const handleDelete = (row?: ConsultationMember) => {
    const _ids: string = row?.id || ids.value.join(',');
    proxy!.$modal
        .confirm('是否确认成员ID为"' + _ids + '"的数据项？')
        .then(function() {
            return oc_deleteGroupMem(_ids);
        })
        .then((res: BaseApiResponse) => {
            if (res.code === 200) {
                tableInfo.search();
                proxy!.$modal.msgSuccess('删除成功');
            } else {
                proxy!.$modal.msgError(res.msg || '删除失败');
            }
        })
        .catch(() => {
        });
};

const open = ref(false);
// 新增
const handleAdd = () => {
    open.value = true;
    tableInfoAdd.load();
};
const lookClose = () => {
    tableInfoAdd.reset();
    open.value = false;
};

const tableInfoAdd = getTableInfo('/evms/doctor_user/list', {
    q_type: 0
});
const idsForAdd = ref<number[]>([]);

const handleSelectionChangeForAdd = (selection: DeviceUserItem[], row: DeviceUserItem) => {
    idsForAdd.value = selection.map(item => item.id);
};
const selectable = (row: DeviceUserItem) => {
    return !tableInfo.list.value.map(i => i.user.userid).includes(row.id - 0); // 只有 canSelect 为 true 的行可以被选中
};
// 新增用户确认
const addBtnLoading = ref(false);
const submitForm = async () => {
    if (!idsForAdd.value.length) {
        proxy!.$modal.msgError('请选择用户');
        return;
    }
    addBtnLoading.value = true;
    try {
        const params: GroupMemberData = {
            id: parseInt(props.groupId),
            userIds: idsForAdd.value
        };
        const res = (await oc_addGroupMem(params)) as BaseApiResponse;
        if (res.code === 200) {
            open.value = false;
            proxy!.$modal.msgSuccess('新增成功');
            tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '新增失败');
        }
    } catch (error) {
        proxy!.$modal.msgError('添加失败');
    } finally {
        addBtnLoading.value = false;
    }
};
// 新增用户取消
const cancel = () => {
    open.value = false;
    idsForAdd.value = [];
};
</script>

<template>
    <div class="consultation_hospital_member">
        <el-form :inline="true" :model="tableInfo.from" label-width="100">
            <el-row justify="space-between">
                <el-col :span="8">
                    <el-form-item label="姓名" prop="userId">
                        <el-input
                            v-model="tableInfo.from.like_Juser_realname"
                            clearable
                            placeholder="请输入姓名"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="手机号" prop="id">
                        <el-input
                            v-model="tableInfo.from.Juser_username"
                            clearable
                            placeholder="请输入手机号"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-button icon="Search" type="primary" @click="tableInfo.search">搜索</el-button>
                    <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button color="#009dff" icon="Plus" plain @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    :disabled="multiple"
                    color="#ff5c00"
                    icon="Delete"
                    plain
                    @click="() => handleDelete()"
                >删除
                </el-button>
            </el-col>
            <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
        </el-row>
        <el-table
            :data="tableInfo.list.value"
            height="350"
            style="width: 100%; margin-top: 20px"
            @selection-change="handleSelectionChange"
        >
            <el-table-column align="center" type="selection" />
            <el-table-column align="center" label="用户ID" prop="user.userid" width="140"></el-table-column>
            <el-table-column align="center" label="用户姓名" prop="user.realname" width="120">
            </el-table-column>
            <el-table-column align="center" label="手机号" prop="user.username"></el-table-column>
            <el-table-column align="center" label="医院" prop="user.company"></el-table-column>
            <el-table-column align="center" label="科室" prop="user.department"></el-table-column>
            <el-table-column align="center" label="是否是群主" prop="leader">
                <template #default="scope">
                    <span>{{ scope.row.leader ? '是' : '否' }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="220">
                <template #default="scope">
                    <el-button icon="Delete" link type="danger" @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-row align="bottom" justify="space-between">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                v-model:limit="tableInfo.pageParam.pageSize"
                v-model:page="tableInfo.pageParam.pageNum"
                :total="tableInfo.total.value"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
    <!--    查看群组成员-->
    <el-dialog
        v-model="open"
        append-to-body
        draggable
        modal-class="consultation_hospital_look"
        width="1156px"
        @closed="lookClose"
    >
        <template #header>
            <div class="my-header">
                <div class="dialog_header">新增群组成员</div>
            </div>
        </template>
        <div>
            <el-form :inline="true" :model="tableInfoAdd.from" label-width="100">
                <el-row justify="space-between">
                    <el-col :span="8">
                        <el-form-item label="姓名" prop="userId">
                            <el-input
                                v-model="tableInfoAdd.from.like_realname"
                                clearable
                                placeholder="请输入姓名"
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="手机号" prop="id">
                            <el-input
                                v-model="tableInfoAdd.from.like_mobile"
                                clearable
                                placeholder="请输入手机号"
                                style="width: 240px"
                                @keyup.enter="tableInfoAdd.search"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-button icon="Search" type="primary" @click="tableInfoAdd.search">搜索</el-button>
                        <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                    </el-col>
                </el-row>
            </el-form>

            <el-table
                :data="tableInfoAdd.list.value"
                height="350"
                style="width: 100%; margin-top: 20px"
                @selection-change="handleSelectionChangeForAdd"
            >
                <el-table-column :selectable="selectable" type="selection" width="55"></el-table-column>
                <el-table-column label="用户ID" prop="id" width="140"></el-table-column>
                <el-table-column label="用户姓名" prop="realname" width="120"></el-table-column>
                <el-table-column label="手机号" prop="mobile"></el-table-column>
                <el-table-column label="医院" prop="company"></el-table-column>
                <el-table-column label="科室" prop="department"></el-table-column>
            </el-table>
            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfoAdd.total }} 项数据</div>
                <pagination
                    v-show="tableInfoAdd.total.value > 0"
                    v-model:limit="tableInfoAdd.pageParam.pageSize"
                    v-model:page="tableInfoAdd.pageParam.pageNum"
                    :total="tableInfoAdd.total.value"
                    @pagination="tableInfoAdd.pagination"
                />
            </el-row>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button :loading="addBtnLoading" type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss">
.consultation_hospital_look {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
