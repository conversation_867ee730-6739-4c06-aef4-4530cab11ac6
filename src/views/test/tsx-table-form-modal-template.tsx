import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { RefsKes } from '@/type/common';
import RightToolar from '@/components/RightToolbar/index.vue';
import moment from 'moment';
import Pagination from '@/components/Pagination/index.vue';
import {
    listExpert,
    getExpert,
    delExpert,
    addExpert,
    updateExpert,
    EvmsConsultExpert
} from '@/api/evms/expert';

interface QueryParams extends Partial<EvmsConsultExpert> {
    pageNum: number;
    pageSize: number;
}

export default defineComponent({
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;

        const state = reactive({
            listData: [] as EvmsConsultExpert[],
            visible: false,
            showSearch: true,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                realName: ''
            } as QueryParams,
            loading: false,
            daterange: [] as string[],
            total: 0,
            ids: [] as Array<string | number>,
            single: true,
            multiple: false
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function handleOk() {
            close();
        }

        function close() {
            state.visible = false;
        }

        function show() {
            state.visible = true;
            handleQuery();
        }

        function handleUpdate(row?: EvmsConsultExpert) {
            const _id = row ? row.id : state.ids;
        }

        function handleDelete(row?: EvmsConsultExpert) {
            const _ids = row ? row.id : state.ids;
            proxy?.$modal
                .confirm('是否确认删除会诊专家编号为' + _ids + '的数据项？')
                .then(() => {
                    return delExpert(_ids);
                })
                .then(() => {
                    getList();
                    proxy?.$modal.msgSuccess('删除成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        const slots = {
            column: {
                signingStartTime({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingStartTime
                                ? moment(record.signingStartTime).format('YYYY-MM-DD HH:mm:ss')
                                : '--'}
                        </span>
                    );
                }
            },
            actionSlots({ row: record }: { row: EvmsConsultExpert }) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:expert:edit']]}
                            link
                            type="primary"
                            icon="Edit"
                            onClick={() => handleUpdate(record)}
                        >
                            修改
                        </ElButton>
                        <ElButton
                            v-hasPermi={[['evms:expert:remove']]}
                            link
                            type="primary"
                            icon="Delete"
                            onClick={() => handleDelete(record)}
                        >
                            删除
                        </ElButton>
                    </>
                );
            },
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };

        function getList() {
            state.loading = true;
            // if (state.daterange.length === 2) {
            //     state.queryParams.signingStartTimeStamp = moment(state.daterange[0]).valueOf()
            //     state.queryParams.signingEndTimeStamp = moment(state.daterange[1]).valueOf()
            // }
            listExpert(state.queryParams).then(response => {
                state.listData = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        function resetQuery() {
            state.daterange = [];
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleAdd() {
        }

        function handleSelectionChange(selection: EvmsConsultExpert[]) {
            state.ids = selection.map(item => item.id);
            state.single = selection.length !== 1;
            state.multiple = !selection.length;
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="病历详情"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="专家名称" prop="realName">
                        <ElInput
                            v-model={state.queryParams.realName}
                            placeholder="请输入专家名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}
                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['sys:test:add']]}
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={handleAdd}
                        >
                            添加专家
                        </ElButton>
                    </ElCol>
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable
                    v-loading={state.loading}
                    data={state.listData}
                    onSelection-change={handleSelectionChange}
                >
                    <ElTableColumn type="selection" width="55" align="center" />
                    <ElTableColumn label="医生id" align="center" prop="doctorId" />
                    <ElTableColumn label="专家名称" align="center" prop="realName" />
                    <ElTableColumn
                        label="签约起止时间"
                        align="center"
                        prop="signingStartTime"
                        width="180"
                        v-slots={slots.column.signingStartTime}
                    />
                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    ></ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
            </ElDialog>
        );
    }
});
