const listeners: { [propsname: string]: Array<Function> } = {};

export class WebStorageEvent {
    static on(name: string, callback: Function): void {
        if (typeof listeners[name] !== 'undefined') {
            listeners[name] = [];
        }
        listeners[name].push(callback);
    }
    static off(name: string, callback: Function): void {
        if (listeners[name].length) {
            listeners[name].splice(listeners[name].indexOf(callback), 1);
        } else {
            listeners[name] = [];
        }
    }
}
export function emit(event: Event): void {
    const e = event;
    const getValue = (data: string | null) => {
        if (typeof data === 'string') {
            return data;
        } else {
            return null;
        }
    };
    const fire = (listener: Function) => {
        const newValue = getValue((e as StorageEvent).newValue);
        const oldValue = getValue((e as StorageEvent).oldValue);
        listener(newValue, oldValue, (e as StorageEvent).url);
    };
    if (typeof e === 'undefined' || typeof (e as StorageEvent).key === 'undefined') {
        return;
    }

    const all = listeners[(e as StorageEvent).key as string];

    if (typeof all !== 'undefined') {
        all.forEach(fire);
    }
}
