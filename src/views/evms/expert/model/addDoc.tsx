import { defineComponent, getCurrentInstance, onMounted, reactive } from 'vue';
import { ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElRow, ElTable, ElTableColumn } from 'element-plus';
import { RefsKes } from '@/type/common';
import RightToolar from '@/components/RightToolbar/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import { EvmsChooseDoctor, listChooseDoctor, saveExpert } from '@/api/evms/expert';

interface QueryParams extends Partial<EvmsChooseDoctor> {
    pageNum: number;
    pageSize: number;
    identityStr: number;
}

export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;

        const state = reactive({
            expertList: [] as EvmsChooseDoctor[],
            visible: false,
            showSearch: true,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                doctorName: '',
                identityStr: 1
            } as QueryParams,
            loading: false,
            dateRange: [] as string[],
            total: 0,
            ids: [] as Array<string | number>,
            single: true,
            multiple: false,
            currentRow: null as EvmsChooseDoctor | null,
            oldCurrentRow: null as EvmsChooseDoctor | null
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function handleOk() {
            if (state.currentRow) {
                saveExpert({
                    doctorId: state.currentRow.id,
                    realName: state.currentRow.userRealName,
                    consultationStatus: true
                }).then(res => {
                    close();
                    emit('ok');
                    proxy?.$modal.msgSuccess('添加成功');
                });
            } else {
                proxy?.$modal.msgWarning('请选择专家');
            }
        }

        function close() {
            state.visible = false;
        }

        // type 1 会诊专家列表添加专家 2会诊医院绑定专家
        function show() {
            state.visible = true;
            handleQuery();
        }

        function handleUpdate(row?: EvmsChooseDoctor) {
            const _id = row ? row.id : state.ids;
        }

        const slots = {
            column: {},
            actionSlots(record: EvmsChooseDoctor) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:expert:edit']]}
                            link
                            type="primary"
                            icon="Edit"
                            onClick={() => handleUpdate(record)}
                        >
                            修改
                        </ElButton>
                    </>
                );
            },
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };

        function getList() {
            state.loading = true;
            listChooseDoctor(state.queryParams).then(response => {
                state.expertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        function resetQuery() {
            state.dateRange = [];
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleAdd() {
        }

        function handleSelectionChange(selection: EvmsChooseDoctor[]) {
            state.ids = selection.map(item => item.id);
            state.single = selection.length !== 1;
            state.multiple = !selection.length;
        }

        function onCurrentChange(currentRow: EvmsChooseDoctor, oldCurrentRow: EvmsChooseDoctor) {
            state.currentRow = currentRow;
            state.oldCurrentRow = oldCurrentRow;
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="添加专家"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="专家名称" prop="doctorName">
                        <ElInput
                            v-model={state.queryParams.doctorName}
                            placeholder="请输入专家名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}
                <ElRow gutter={10} class="mb8">
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable
                    v-loading={state.loading}
                    data={state.expertList}
                    highlightCurrentRow={true}
                    onCurrent-change={onCurrentChange}
                >
                    {/* <ElTableColumn type="selection" width="55" align="center" /> */}
                    <ElTableColumn label="专家ID" align="center" prop="id" />
                    <ElTableColumn label="专家名称" align="center" prop="userRealName" />
                    <ElTableColumn label="所在医院" align="center" prop="company" />
                    <ElTableColumn label="科室" align="center" prop="department" />
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
            </ElDialog>
        );
    }
});
