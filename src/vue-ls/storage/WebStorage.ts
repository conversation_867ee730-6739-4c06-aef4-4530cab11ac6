import { WebStorageEvent, emit } from './WebStorageEvent';
import { MemoryStorageType } from './MemoryStorage';

export enum Types {
    Session = 'session',
    Local = 'local',
    Memory = 'memory',
}

export interface Options {
    namespace?: string;
    name?: string;
    storage?: Types;
    events?: Array<keyof WindowEventMap>;
}

export class WebStorage {
    constructor(storage: MemoryStorageType) {
        this.storage = storage;
        this.options = {
            namespace: '',
            events: ['storage']
        };
        Object.defineProperty(this, 'length', {
            get() {
                return this.storage.length;
            }
        });
        if (typeof window !== 'undefined' && this.options.events) {
            for (let i = 0; i < this.options.events.length; i++) {
                if (window.addEventListener) {
                    window.addEventListener(this.options.events[i], emit, false);
                }
            }
        }
    }

    length: number = 0;
    options: Options;
    storage: MemoryStorageType;

    setOptions(options: Options = {}) {
        this.options = Object.assign(this.options, options);
    }

    set<T>(name: string, value: T, expire: number | null = null) {
        const stringifyValue = JSON.stringify({
            value,
            expire: expire !== null ? new Date().getTime() + expire : null
        });
        this.storage.setItem<string>(this.options.namespace + name, stringifyValue);
    }

    get<T>(name: string, def: T | null = null): T | null {
        const item = this.storage.getItem(this.options.namespace + name);
        if (item !== null) {
            try {
                const data = JSON.parse(item);
                if (data.expire === null) {
                    return data.value;
                }
                if (data.expirce >= new Date().getTime()) {
                    return data.value;
                }
                this.remove(name);
            } catch (err) {
                return def;
            }
        }
        return def;
    }

    key(index: number) {
        return this.storage.key(index);
    }

    remove(name: string) {
        return this.storage.removeItem(this.options.namespace + name);
    }

    clear() {
        if (this.length === 0) {
            return;
        }
        const removedKeys = [];
        for (let i = 0; i < this.length; i++) {
            const key = this.storage.key(i);
            const regexp = new RegExp(`^${this.options.namespace}.+`, 'i');
            if (regexp.test(key) === false) {
                continue;
            }
            removedKeys.push(key);
        }
        for (const key in removedKeys) {
            this.storage.removeItem(removedKeys[key]);
        }
    }

    on(name: string, callback: Function) {
        WebStorageEvent.on(this.options.namespace + name, callback);
    }

    /**
     * Remove storage change event
     *
     * @param {string} name
     * @param {Function} callback
     */
    off(name: string, callback: Function) {
        WebStorageEvent.off(this.options.namespace + name, callback);
    }
}
