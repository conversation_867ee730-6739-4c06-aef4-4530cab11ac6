import request from '@/utils/request';

// 查询EBS病例分组列表
export function listEbsGroup(query: any) {
    return request({
        url: '/evms/ebs_group/list',
        method: 'get',
        params: query,
    });
}

// 查询EBS病例分组详细
export function getEbsGroup(id: any) {
    return request({
        url: '/evms/ebs_group/' + id,
        method: 'get',
    });
}

// 新增EBS病例分组
export function addEbsGroup(data: any) {
    return request({
        url: '/evms/ebs_group',
        method: 'post',
        data: data,
    });
}

// 修改EBS病例分组
export function updateEbsGroup(data: any) {
    return request({
        url: '/evms/ebs_group',
        method: 'put',
        data: data,
    });
}

// 删除EBS病例分组
export function delEbsGroup(id: any) {
    return request({
        url: '/evms/ebs_group/' + id,
        method: 'delete',
    });
}
