import type { BaseResponse } from '@/types/consultation';
import { getCurrentInstance } from 'vue';

export const useConsultationUtils = () => {
    const instance = getCurrentInstance();
    if (!instance) {
        throw new Error('useConsultationUtils 必须在组件设置中调用');
    }
    const { proxy } = instance;

    const handleDeleteConfirm = async (
        id: string | number,
        deleteFunc: (id: string | number) => Promise<any>,
    ): Promise<boolean> => {
        try {
            await proxy!.$modal.confirm(`是否确认删除ID为"${id}"的数据项？`);
            const response = await deleteFunc(id);
            const res = response as unknown as BaseResponse<void>;

            if (res.code === 200) {
                proxy!.$modal.msgSuccess('删除成功');
                return true;
            }
            return false;
        } catch (error) {
            console.error('删除失败:', error);
            return false;
        }
    };

    return {
        handleDeleteConfirm,
    };
};

export const formatDateTime = (dateStr: string): string => {
    return dateStr ? dateStr.slice(0, 10) : '';
};
