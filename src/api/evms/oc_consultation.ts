/*
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-03-11 16:40:21
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-07-03 13:37:46
 * @FilePath: /evms-web/src/api/evms/consultation.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Http } from '@/utils/request';
import { ConsultationHospital, ConsultationHospitalParams, ConsultationOrder } from '@/model/consultation';
import { BaseApi } from '@/model/api';

// 删除会诊
export function oc_delCns(id: string): Promise<BaseApi<null>> {
    return Http.delete(`/oc/consultation/${id}`);
}

// 取消会诊
export function oc_cancelConsultation(ids: string): Promise<BaseApi<null>> {
    return Http.post('/oc/consultation/cancel', { ids }, false, 'form');
}

// 结束会诊
export function oc_closeConsultation(ids: string): Promise<BaseApi<null>> {
    return Http.post('/oc/consultation/close', { ids }, false, 'form');
}

// 获取订单详情
export function oc_getOrderDetail(id: string): Promise<BaseApi<ConsultationOrder>> {
    return Http.get(`/oc/consultation/${id}`);
}

// 新增会诊
export function oc_addCnsOrder(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.post('/oc/consultation/add', data);
}

// 修改会诊
export function oc_updateCnsOrder(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.put('/oc/consultation/update', data);
}

// 新增专家示教
export function oc_addCnsOrderGroup(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.post('/oc/consultation_group/add', data);
}

// 修改专家示教
export function oc_updateCnsOrderGroup(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.put('/oc/consultation_group/update', data);
}
// 取消专家示教
export function oc_cancelConsultationGroup(ids: string): Promise<BaseApi<null>> {
    return Http.post('/oc/consultation_group/cancel', { ids }, false, 'form');
}
// 结束专家示教
export function oc_closeConsultationGroup(ids: string): Promise<BaseApi<null>> {
    return Http.post('/oc/consultation_group/close', { ids }, false, 'form');
}
// 删除专家示教
export function oc_delCnsGroup(id: string): Promise<BaseApi<null>> {
    return Http.delete(`/oc/consultation_group/${id}`);
}
// 删除专家
export function oc_deleteExpert(id: string): Promise<BaseApi<null>> {
    return Http.delete(`/evms/doctor_expert/${id}`);
}
// 获取专家示教详情
export function oc_getGroupOrderDetail(id: string): Promise<BaseApi<ConsultationOrder>> {
    return Http.get(`/oc/consultation_group/${id}`);
}
// 添加专家
export function oc_addExpert(data: string): Promise<BaseApi<null>> {
    return Http.post('/evms/doctor_expert/batchAdd', { ids: data }, false, 'form');
}

// 删除群组
export function oc_deleteGroup(ids: string): Promise<BaseApi<null>> {
    return Http.delete(`/oc/group/${ids}`);
}

// 获取群组信息
export function oc_getGroupDetail(id: string): Promise<BaseApi<ConsultationHospital>> {
    return Http.get(`/oc/group/${id}`);
}

// 新增群组
export function oc_addGroup(data: ConsultationHospitalParams): Promise<BaseApi<null>> {
    return Http.post('/oc/group/add', data);
}

// 修改群组
export function oc_updateGroup(data: ConsultationHospitalParams): Promise<BaseApi<null>> {
    return Http.put('/oc/group/update', data);
}

// 删除群组成员
export function oc_deleteGroupMem(ids: string): Promise<BaseApi<null>> {
    return Http.delete(`/oc/group/removeMember/${ids}`);
}

// 添加群组成员
export interface GroupMemberData {
    id: number;
    userIds: number[];
}

export function oc_addGroupMem(data: GroupMemberData): Promise<BaseApi<null>> {
    return Http.post('/oc/group/addMember', data);
}
