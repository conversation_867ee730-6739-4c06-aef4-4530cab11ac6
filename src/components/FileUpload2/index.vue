<template>
    <div class="upload-file">
        <el-upload
            ref="fileUpload"
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            :disabled="uploadDisabled"
            :file-list="fileList"
            :headers="headers"
            :limit="limit"
            :on-error="handleUploadError"
            :on-exceed="handleExceed"
            :on-progress="handleUploadProcess"
            :on-success="handleUploadSuccess"
            :show-file-list="false"
            class="upload-file-uploader"
            multiple
        >
            <!-- 上传按钮 -->
            <el-button type="primary">选取文件</el-button>
        </el-upload>
        <span v-show="uploadDisabled">请先选择平台类型、应用程序类型、输入版本号后再上传安装包</span>

        <!-- 上传提示 -->
        <div v-if="showTip" class="el-upload__tip">
            请上传
            <template v-if="fileSize">
                大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
            </template>
            <template v-if="fileType">
                格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
            </template>
            的文件
        </div>
        <!-- 文件列表 -->
        <transition-group
            class="upload-file-list el-upload-list el-upload-list--text"
            name="el-fade-in-linear"
            tag="ul"
        >
            <li
                v-for="(file, index) in fileList"
                :key="file.uid"
                class="el-upload-list__item ele-upload-list__item-content"
            >
                <el-link :href="`${file.url}`" :underline="false" target="_blank">
                    <span class="el-icon-document">
                        {{ getFileName(file.name) + '——' + fileNames[index] }}
                    </span>
                </el-link>
                <div class="ele-upload-list__item-content-action">
                    <el-link :underline="false" type="danger" @click="handleDelete(index)">删除</el-link>
                </div>
            </li>
        </transition-group>
    </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import { ComponentInternalInstance, computed, getCurrentInstance, onBeforeUnmount, ref, watch } from 'vue';

const props = defineProps({
    platform: {
        type: String,
        default: ''
    },
    applicationProgram: {
        type: String,
        default: ''
    },
    version: {
        type: String,
        default: ''
    },
    modelValue: [String, Object, Array] as any,
    // 数量限制
    limit: {
        type: Number,
        default: 1
    },
    // 大小限制(MB)
    fileSize: {
        type: Number,
        default: 500
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
        type: Array as () => Array<any>,
        default: () => [
            'doc',
            'docx',
            'xls',
            'xlsx',
            'ppt',
            'pptx',
            'txt',
            'pdf',
            'wmv',
            'mp4',
            'avi',
            '3gp',
            'exe',
            'png',
            'zip',
            'rar',
            '7z',
            'pak',
            'pkg',
            'dmg',
            'apk'
        ]
    },
    // 是否显示提示
    isShowTip: {
        type: Boolean,
        default: true
    },
});
const percent = ref(0);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emit = defineEmits(['update:modelValue', 'update:signature']);
const number = ref(0);
const uploadList = ref<any[]>([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/sys/file/upload2'); // 上传文件服务器地址
const headers = ref({ Authorization: 'Bearer ' + getToken() });
const fileList = ref<any[]>([]);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));
const fileNames = ref<string[]>([]);
watch(
    () => props.modelValue,
    val => {
        if (val) {
            let temp = 1;
            // 首先将值转为数组
            const list = Array.isArray(val) ? val : props.modelValue!.split(',');
            // 然后将数组转为对象数组
            fileList.value = list.map((item: any) => {
                if (typeof item === 'string') {
                    item = { name: item, url: item };
                }
                item.uid = item.uid || new Date().getTime() + temp++;
                return item;
            });
        } else {
            fileList.value = [];
            return [];
        }
    },
    { deep: true, immediate: true }
);
// 控制是否可上传
const uploadDisabled = ref(true);
const allPropsFilled = computed(() => {
    return props.platform && props.version && props.applicationProgram;
});
const applicationProgram = ref('');
const platform = ref('');
const version = ref('');
watch(
    props,
    newObj => {
        console.log(newObj, '-------0');
        if (allPropsFilled.value) {
            uploadDisabled.value = false;
            // 在这里你可以执行你需要的操作
            applicationProgram.value = props.applicationProgram;
            if ('evms_client' === props.applicationProgram) {
                applicationProgram.value = 'Brainmed';
            }

            if ('app' === props.applicationProgram) {
                applicationProgram.value = 'Brainmed';
            }

            platform.value = props.platform;
            if ('mac-arm' === props.platform) {
                platform.value = 'Apple';
            }

            if ('mac-x64' === props.platform) {
                platform.value = 'Intel';
            }

            if ('android' === props.platform) {
                platform.value = 'Android';
            }

            console.log('------------' + props.applicationProgram);
            uploadFileUrl.value =
                import.meta.env.VITE_APP_BASE_API +
                '/sys/file/upload2?filename=' +
                applicationProgram.value +
                '_' +
                props.version +
                '_' +
                platform.value;
        } else {
            uploadDisabled.value = true;
            uploadFileUrl.value = import.meta.env.VITE_APP_BASE_API + '/sys/file/upload2';
        }
    },
    {
        deep: true // 这将确保深层监听对象内部属性的变化
    }
);

// 上传前校检格式和大小
function handleBeforeUpload(file: any) {
    console.log(file, '---');
    // 校检文件类型
    if (props.fileType.length) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
            proxy!.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`);
            return false;
        }
    }
    // 校检文件大小
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            proxy!.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    proxy!.$modal.loading('正在上传文件，请稍候...');
    number.value++;
    return true;
}

// 文件个数超出
function handleExceed() {
    proxy!.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError(err: any) {
    proxy!.$modal.msgError('上传文件失败');
}

// 上传成功回调
function handleUploadSuccess(res: any, file: any) {
    if (res.code === 200) {
        console.log(res);
        fileNames.value.push(file.name);
        console.log('filenames---', fileNames.value);

        uploadList.value.push({ name: res.data.url, url: res.data.url, md5: res.data.md5 });
        uploadedSuccessfully();
    } else {
        number.value--;
        proxy!.$modal.closeLoading();
        proxy!.$modal.msgError(res.msg);
        (proxy!.$refs.fileUpload as any).handleRemove(file);
        uploadedSuccessfully();
    }
}

function handleUploadProcess(event: any, file: any) {
    percent.value = Math.floor(event.percent);
    proxy!.$modal.updateLoading('正在上传文件，' + percent.value + '%');
}

// 删除文件
function handleDelete(index: any) {
    fileList.value.splice(index, 1);
    fileNames.value.splice(index, 1);
    emit('update:modelValue', listToString(fileList.value));
}

// 上传结束处理
function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
        fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
        let md5 = uploadList.value
            .filter(f => f.md5 !== undefined)
            .map(f => f.md5)
            .join(',');
        console.log(md5 + '           md5');
        console.log(fileList.value);
        uploadList.value = [];
        console.log(uploadList);
        number.value = 0;
        emit('update:modelValue', listToString(fileList.value));
        emit('update:signature', md5);
        proxy!.$modal.closeLoading();
    }
}

// 获取文件名称
function getFileName(name: any) {
    if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1);
    } else {
        return '';
    }
}

// 对象转成指定字符串分隔
function listToString(list: any, separator?: any) {
    let strs = '';
    separator = separator || ',';
    for (let i in list) {
        if (list[i].url) {
            strs += list[i].url + separator;
        }
    }
    console.log(strs);
    console.log(strs !== '' ? strs.substr(0, strs.length - 1) : '');
    return strs !== '' ? strs.substr(0, strs.length - 1) : '';
}

onBeforeUnmount(() => {
    fileNames.value = [];
});
</script>

<style lang="scss" scoped>
.upload-file-uploader {
    margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
}

.upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
}

.ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
}
</style>
