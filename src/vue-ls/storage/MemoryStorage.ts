let ls: { [propsname: string]: any } = {};
export type MemoryStorageType = {
    getItem(name: string): any;
    setItem<T>(name: string, value: T): boolean;
    removeItem(name: string): boolean;
    clear(): boolean;
    key(index: number): any;
};

class MemoryStorageInterface {
    constructor() {
        Object.defineProperty(this, 'length', {
            get() {
                return Object.keys(ls).length;
            }
        });
    }

    getItem(name: string): any {
        return name in ls ? ls[name] : null;
    }

    setItem<T>(name: string, value: T): boolean {
        ls[name] = value;
        return true;
    }

    removeItem(name: string): boolean {
        const found: boolean = name in ls;
        if (found) {
            return delete ls[name];
        }
        return false;
    }

    claer(): boolean {
        ls = {};
        return true;
    }

    key(index: number): any {
        const keys = Object.keys(ls);
        return typeof keys[index] !== 'undefined' ? keys[index] : null;
    }
}

const MemoryStorage = new MemoryStorageInterface();
export { MemoryStorage };
