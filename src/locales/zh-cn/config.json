{"config.port": "端口", "config.port.tooltip": "如不指定特殊端口，应留空。此端口仅用于生成网站的链接地址，如 http://www.example.com:8080/。此设置不会改变服务器的端口，应以服务器实际的端口进行设置", "config.contextPath": "上下文路径", "config.contextPath.tooltip": "Tomcat等应用服务器中部署的上下文路径(context path)，如部署在ROOT目录下，请留空", "config.channelUrl": "栏目访问地址", "config.channelUrl.tooltip": "前台栏目默认访问地址为：/channel。使用默认地址，留空即可。可改为其它地址，如：/categories", "config.articleUrl": "文章访问地址", "config.articleUrl.tooltip": "前台文章默认访问地址为：/article。使用默认地址，留空即可。可改为其它地址，如：/archives", "config.defaultSite": "默认站点", "config.multiDomain": "多域名", "config.multiDomain.tooltip": "使用多个域名且生成静态页，需要开启该项。开启后生成静态页时，静态页存储路径会加上域名，以区分不同域名的静态页面", "config.settings.base": "基础设置", "config.settings.upload": "上传设置", "config.settings.register": "注册设置", "config.settings.security": "安全设置", "config.settings.sms": "短信设置", "config.settings.email": "邮箱设置", "config.settings.uploadStorage": "附件存储点", "config.settings.htmlStorage": "静态页存储点", "config.settings.templateStorage": "模板存储点", "config.settings.customs": "自定义设置", "config.upload.imageTypes": "允许的图片类型", "config.upload.imageTypes.tooltip": "允许上传的图片类型。多个类型用英文逗号分隔，留空则不限制。常用格式如：jpg,jpeg,png,gif", "config.upload.videoTypes": "允许的视频类型", "config.upload.videoTypes.tooltip": "允许上传的视频类型。多个类型用英文逗号分隔，留空则不限制。常用格式如：mp4,webm,ogg", "config.upload.audioTypes": "允许的音频类型", "config.upload.audioTypes.tooltip": "允许上传的视频类型。多个类型用英文逗号分隔，留空则不限制。常用格式如：mp3,ogg,wav", "config.upload.libraryTypes": "允许的文库类型", "config.upload.libraryTypes.tooltip": "允许上传的文库类型。多个类型用英文逗号分隔，留空则不限制。常用格式如：pdf,doc,docx,xls,xlsx,ppt,pptx", "config.upload.docTypes": "允许的DOC导入类型", "config.upload.docTypes.tooltip": "允许上传的DOC导入类型。多个类型用英文逗号分隔，留空则不限制。常用格式如：doc,docx,xls,xlsx", "config.upload.fileTypes": "允许的文件类型", "config.upload.fileTypes.tooltip": "允许上传的文件类型。多个类型用英文逗号分隔，留空则不限制。常用格式如：zip,7z,gz,bz2,iso,rar,txt,pdf,doc,docx,xls,xlsx,ppt,pptx", "config.upload.imageLimit": "图片最大长度", "config.upload.imageLimit.tooltip": "为 0 则不限制。单位 MB", "config.upload.videoLimit": "视频最大长度", "config.upload.videoLimit.tooltip": "为 0 则不限制。单位 MB", "config.upload.audioLimit": "音频最大长度", "config.upload.audioLimit.tooltip": "为 0 则不限制。单位 MB", "config.upload.libraryLimit": "文库最大长度", "config.upload.libraryLimit.tooltip": "为 0 则不限制。单位 MB", "config.upload.docLimit": "DOC导入最大长度", "config.upload.docLimit.tooltip": "为 0 则不限制。单位 MB", "config.upload.fileLimit": "文件最大长度", "config.upload.fileLimit.tooltip": "为 0 则不限制。单位 MB", "config.upload.imageMaxWidth": "图片最大宽度", "config.upload.imageMaxWidth.tooltip": "在编辑器上传的图片，如超过此宽度，会自动压缩。为 0 则不限制。", "config.upload.imageMaxHeight": "图片最大高度", "config.upload.imageMaxHeight.tooltip": "在编辑器上传的图片，如超过此高度，会自动压缩。为 0 则不限制。", "config.upload.error.extensionNotAllowd": "不允许的后缀名", "config.register.enabled": "是否启用", "config.register.verifyMode": "验证方式", "config.register.verifyMode.1": "不验证", "config.register.verifyMode.2": "人工验证", "config.register.verifyMode.3": "邮箱地址验证", "config.register.verifyMode.4": "手机号码验证", "config.register.usernameMinLength": "用户名最小长度", "config.register.usernameMaxLength": "用户名最大长度", "config.register.usernameRegex": "用户名正则表达式", "config.register.usernameRegex.tooltip": "默认允许为 中文 数字 字符 . - _", "config.register.smallAvatarSize": "小头像尺寸", "config.register.mediumAvatarSize": "中头像尺寸", "config.register.largeAvatarSize": "大头像尺寸", "config.register.avatar": "默认头像", "config.security.passwordMinLength": "密码最小长度", "config.security.passwordMinLength.tooltip": "0-16。0不限制，常用值8", "config.security.passwordMaxLength": "密码最大长度", "config.security.passwordMaxLength.tooltip": "16-64。常用值64", "config.security.passwordMinDays": "密码最短使用天数", "config.security.passwordMinDays.tooltip": "密码使用时间少于此天数，不可以再次修改密码。0-998。0不限制，常用值15", "config.security.passwordMaxDays": "密码最长使用天数", "config.security.passwordMaxDays.tooltip": "密码使用时间超过此天数，需修改密码后才可登录。0-999。0不限制，常用值90", "config.security.passwordWarnDays": "密码过期警告天数", "config.security.passwordWarnDays.tooltip": "密码有效期少于此天数，登录时提示密码剩余有效期。0-90。0不警告，常用值7", "config.security.passwordMaxHistory": "强制密码历史", "config.security.passwordMaxHistory.tooltip": "限定多少次内的历史密码不能重复。0-24。0不限制，常用值5", "config.security.userMaxAttempts": "用户允许尝试次数", "config.security.userMaxAttempts.tooltip": "用户登录错误超过该次数，将锁定用户登录。0-100。0不限制，常用值5", "config.security.userLockMinutes": "用户登录锁定时间", "config.security.userLockMinutes.tooltip": "用户登录错误超过次数，锁定用户登录的时间。单位分钟。1-1440，常用值30", "config.security.ipCaptchaAttempts": "IP验证码次数", "config.security.ipCaptchaAttempts.tooltip": "某IP地址登录错误超过该次数，需要输入验证码。0-100。为0则总是需要输入验证码，常用值3", "config.security.ipMaxAttempts": "IP允许尝试次数", "config.security.ipMaxAttempts.tooltip": "某IP地址登录错误超过该次数，将锁定该IP登录2小时。0-999。0不限制，常用值10", "config.security.passwordStrength": "密码强度", "config.security.passwordStrength.0": "不限制", "config.security.passwordStrength.1": "大小写字母+数字", "config.security.passwordStrength.2": "大写字母+小写字母+数字", "config.security.passwordStrength.3": "大小写字母+数字+特殊字符", "config.security.passwordStrength.4": "大写字母+小写字母+数字+特殊字符", "config.security.twoFactor": "双因子验证", "config.security.twoFactor.tooltip": "开启后，需要同时提供密码和短信验证码才可登录", "config.security.ssrfWhiteList": "SSRF白名单", "config.security.ssrfWhiteList.tooltip": "抓取远程图片或采集时，允许访问的域名。一行一个域名，可以只输入顶级域名，如 example.com。允许访问所有域名可使用 *，此时只禁止访问内部网络", "config.sms.op.testSend": "发送测试短信", "config.sms.provider": "短信服务商", "config.sms.provider.0": "未开启", "config.sms.provider.1": "阿里云短信", "config.sms.provider.2": "腾讯云短信", "config.sms.maxPerIp": "IP每日最大量", "config.sms.codeLength": "验证码长度", "config.sms.codeExpires": "验证码有效时间", "config.sms.codeExpires.tooltip": "单位：分钟", "config.sms.signName": "短信签名", "config.sms.signName.tooltip": "短信签名必须先经过短信服务商审核", "config.sms.test": "短信测试", "config.sms.testMobile": "测试号码", "config.sms.accessKeyId": "accessKeyId", "config.sms.accessKeySecret": "accessKeySecret", "config.sms.templateCode": "模板Code", "config.sms.codeName": "验证码变量名称", "config.sms.secretId": "secretId", "config.sms.secretKey": "secret<PERSON>ey", "config.sms.sdkAppId": "sdkAppId", "config.sms.region": "region", "config.sms.region.tooltip": "地域。可选值: ap-beijing, ap-guangzhou, ap-nanjing", "config.sms.templateId": "模板ID", "config.email.op.testSend": "发送测试邮件", "config.email.host": "SMTP服务器", "config.email.host.tooltip": "根据邮件服务提供商的帮助文档填写，并确认SMTP服务已开启。通常格式为 smtp.xxx.com", "config.email.port": "SMTP端口", "config.email.port.tooltip": "留空为默认端口。一般留空即可，或根据邮件服务提供商的帮助文档填写", "config.email.ssl": "SSL加密", "config.email.ssl.tooltip": "是否使用SSL协议。部分邮件服务提供商会强制要求使用SSL协议，比如QQ邮箱", "config.email.timeout": "SMTP超时时间", "config.email.timeout.tooltip": "留空为默认超时时间。单位：毫秒。一般留空即可", "config.email.from": "Email地址", "config.email.from.tooltip": "完整的邮箱地址。如：{'<EMAIL>'}", "config.email.username": "邮箱用户", "config.email.username.tooltip": "通常与Email地址一致，或者为Email地址{'@'}前面的部分", "config.email.password": "邮箱密码", "config.email.password.tooltip": "部分邮件服务器要求使用独立密码或授权码，请到邮箱管理平台设置", "config.email.maxPerIp": "IP每日最大量", "config.email.codeLength": "验证码长度", "config.email.codeExpires": "验证码有效时间", "config.email.codeExpires.tooltip": "单位：分钟", "config.email.subject": "验证码邮件标题", "config.email.text": "验证码邮件正文", "config.email.text.tooltip": "验证码变量为{'${code}'}", "config.email.testTo": "收件人", "config.email.testEmail": "测试邮件", "config.storage.type": "存储类型", "config.storage.type.0": "本地服务器", "config.storage.type.1": "FTP服务器", "config.storage.type.10": "对象存储（MinIO,阿里云OSS,腾讯云COS,七牛云Kodo,AWS S3）", "config.storage.type.11": "阿里云OSS", "config.storage.type.12": "腾讯云COS", "config.storage.type.13": "七牛云Kodo", "config.storage.endpoint": "Endpoint", "config.storage.region": "Region", "config.storage.bucket": "Bucket", "config.storage.accessKey": "AccessKey", "config.storage.secretKey": "<PERSON><PERSON><PERSON>", "config.storage.hostname": "IP地址", "config.storage.port": "端口号", "config.storage.port.tooltip": "默认端口为21（可留空），TLS隐式加密端口为990", "config.storage.username": "用户名", "config.storage.password": "密码", "config.storage.encoding": "编码", "config.storage.encoding.tooltip": "默认为 UTF-8", "config.storage.passive": "被动模式", "config.storage.passive.tooltip": "一般情况下，均使用被动模式", "config.storage.encryption": "加密方式", "config.storage.encryption.0": "不加密", "config.storage.encryption.1": "TLS隐式加密", "config.storage.encryption.2": "TLS显式加密", "config.storage.path": "存储路径", "config.storage.url": "访问路径", "config.error.channelUrlPattern": "必须以/开头，后面字符支持字母、数字、中划线、下划线", "config.error.articleUrlPattern": "必须以/开头，后面字符支持字母、数字、中划线、下划线", "site.settings.base": "基础设置", "site.settings.watermark": "水印设置", "site.settings.messageBoard": "留言板设置", "site.settings.html": "静态化设置", "site.settings.customs": "自定义设置", "site.watermark.enabled": "是否开启", "site.watermark.enabled.tooltip": "开启水印后，在编辑器、文章图片集中上传的图片会增加水印", "site.watermark.overlay": "水印图片", "site.watermark.position": "水印位置", "site.watermark.position.1": "左上", "site.watermark.position.2": "上", "site.watermark.position.3": "右上", "site.watermark.position.4": "左", "site.watermark.position.5": "中", "site.watermark.position.6": "右", "site.watermark.position.7": "左下", "site.watermark.position.8": "下", "site.watermark.position.9": "右下", "site.watermark.dissolve": "水印透明度", "site.watermark.dissolve.tooltip": "0-100。0: 完全透明; 100: 完全不透明", "site.watermark.minWidth": "最小图片宽度", "site.watermark.minWidth.tooltip": "小于该宽度的图片不加水印", "site.watermark.minHeight": "最小图片高度", "site.watermark.minHeight.tooltip": "小于该高度的图片不加水印", "site.html.enabled": "是否开启", "site.html.enabled.tooltip": "开启静态化后，会自动开始生成静态页，如此时立即访问栏目或文章页，可能出现页面找不到的404错误，需等待静态页生成完成后再访问；关闭静态页后，会自动删除已生成的静态页，如再次访问之前的静态页，会出现页面找不到的404错误", "site.html.auto": "是否自动生成", "site.html.auto.tooltip": "如开启，则添加、修改、删除文章和栏目时，会自动生成静态页；如关闭，则需手动生成", "site.html.listPages": "栏目列表静态化页数", "site.html.listPages.tooltip": "如果开启自动生成静态页，建议只生成 1 页列表页，后面的分页会用动态页代替", "site.html.channel": "栏目静态化路径", "site.html.channel.tooltip": "可用参数有，栏目ID：{'{channel_id}'}，栏目别名：{'{channel_alias}'}", "site.html.article": "文章静态化路径", "site.html.article.tooltip": "可用参数有，栏目ID：{'{channel_id}'}，栏目别名：{'{channel_alias}'}，文章ID：{'{article_id}'}，年：{'{year}'}，月：{'{month}'}，日：{'{day}'}", "site.messageBoard.enabled": "是否开启", "site.messageBoard.loginRequired": "是否需要登录", "model.fun.systemFields": "系统字段", "model.fun.customFields": "自定义字段", "model.name": "名称", "model.type": "类型", "model.type.article": "文章模型", "model.type.channel": "栏目模型", "model.type.user": "用户模型", "model.type.site": "站点设置模型", "model.type.global": "全局设置模型", "model.scope": "共享范围", "model.scope.0": "本站私有", "model.scope.1": "子站点共享", "model.scope.2": "全局共享", "model.mains": "主字段", "model.asides": "右侧字段", "model.customs": "自定义字段", "model.attribute": "字段属性", "model.field.code": "字段代码", "model.field.name": "字段名称", "model.field.show": "是否显示", "model.field.double": "双列布局", "model.field.required": "是否必填", "model.field.attribute": "属性", "model.field.imageWidth": "图片宽", "model.field.imageHeight": "图片高", "model.field.imageMaxWidth": "最大宽度", "model.field.imageMaxHeight": "最大高度", "model.field.imageMode": "压缩模式", "model.field.imageMode.manual": "手动裁剪", "model.field.imageMode.cut": "自动裁剪", "model.field.imageMode.resize": "等比压缩", "model.field.imageMode.none": "原图上传", "model.field.imageListType": "图片列表类型", "model.field.imageListType.pictureCard": "卡片式", "model.field.imageListType.picture": "列表式", "model.field.editorType": "编辑器类型", "model.field.editorType.1": "富文本编辑器", "model.field.editorType.2": "Markdown编辑器", "model.field.editorSwitch": "可切换编辑器", "model.field.minHeight": "最小高度", "model.field.maxHeight": "最大高度", "model.field.rows": "行数", "model.field.placeholder": "占位文本", "model.field.defaultValue": "默认值", "model.field.minlength": "最小长度", "model.field.maxlength": "最大长度", "model.field.min": "最小值", "model.field.max": "最大值", "model.field.precision": "精度", "model.field.step": "步长", "model.field.dateType": "日期类型", "model.field.dateType.date": "日期(date)", "model.field.dateType.datetime": "日期时间(datetime)", "model.field.showInput": "显示输入框", "model.field.activeValue": "开启值", "model.field.inactiveValue": "关闭值", "model.field.dictType": "字典类型", "model.field.checkStyle": "单选样式", "model.field.checkStyle.default": "默认", "model.field.checkStyle.button": "按钮", "model.field.clearable": "可清空选项", "model.field.fileAccept": "文件类型", "model.field.fileAccept.tooltip": "允许的文件类型，必须在全局配置允许的范围内，留空则使用全局配置。格式与HTML的input accept属性一致，如：.doc,.docx,image/png,image/jpeg", "model.field.fileMaxSize": "文件大小", "model.field.fileMaxSize.tooltip": "允许的最大文件，必须在全局配置允许的范围内，留空则使用全局配置。单位 MB", "model.fieldType.text": "单行文本", "model.fieldType.textarea": "多行文本", "model.fieldType.number": "计数器", "model.fieldType.slider": "滑块", "model.fieldType.date": "日期选择器", "model.fieldType.color": "颜色选择器", "model.fieldType.radio": "单选框组", "model.fieldType.checkbox": "多选框组", "model.fieldType.select": "下拉单选", "model.fieldType.multipleSelect": "下拉多选", "model.fieldType.cascader": "级联选择", "model.fieldType.switch": "开关", "model.fieldType.imageUpload": "图片上传", "model.fieldType.videoUpload": "视频上传", "model.fieldType.audioUpload": "音频上传", "model.fieldType.fileUpload": "文件上传", "model.fieldType.tinyEditor": "富文本编辑器", "block.name": "名称", "block.alias": "别名", "block.alias.tooltip": "前台模板标签调用时使用的名称，可以使用汉字、英文字母、数字、下划线、中划线", "block.scope": "共享范围", "block.scope.0": "本站私有", "block.scope.1": "子站共享", "block.scope.2": "全局共享", "block.withLinkUrl": "是否有链接URL", "block.linkUrlRequired": "链接URL是否必填", "block.withSubtitle": "是否有副标题", "block.subtitleRequired": "副标题是否必填", "block.withDescription": "是否有摘要", "block.descriptionRequired": "摘要是否必填", "block.withImage": "是否有图片", "block.imageRequired": "图片是否必填", "block.imageWidth": "图片宽度", "block.imageHeight": "图片高度", "block.withMobileImage": "是否有手机端图片", "block.mobileImageRequired": "手机端图片是否必填", "block.mobileImageWidth": "手机端图片宽度", "block.mobileImageHeight": "手机端图片高度", "block.withVideo": "是否有视频", "block.videoRequired": "视频是否必填", "block.enabled": "是否启用", "block.recommendable": "是否可推荐", "block.recommendable.tooltip": "可推荐的区块会出现在文章列表的推荐选项下", "block.error.aliasPattern": "只能由汉字、英文字母、数字、下划线、中划线组成", "block.error.aliasExist": "别名已存在", "block.error.scopeNotAllowd": "数据已被其它站点使用，不允许设置为本站私有", "dictType.name": "名称", "dictType.alias": "别名", "dictType.remark": "备注", "dictType.scope": "共享范围", "dictType.scope.0": "本站私有", "dictType.scope.1": "子站点共享", "dictType.scope.2": "全局共享", "dictType.sys": "系统字典", "dictType.error.aliasExist": "别名已存在", "": ""}