<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { RtcMeetingDetail, RtcMeetingSetting } from '@/model/rtc_meeting';
import { FormInstance } from 'element-plus';
import { addRtcMeeting, getMeetingById, getOrgList, updateRtcMeeting } from '@/api/rtc/rtc_meeting';
import dayjs from 'dayjs';
import { listDoctorUser } from '@/api/evms/doctor_user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { rtc_meeting_status_enum, rtc_meeting_cate_enum, rtc_meeting_mode_enum, rtc_meeting_type_enum } =
    proxy!.useDict(
        'rtc_meeting_status_enum',
        'rtc_meeting_cate_enum',
        'rtc_meeting_mode_enum',
        'rtc_meeting_type_enum'
    );

// 表单校验规则
const FormRef = ref<FormInstance>();
const validatePassword = (rule: any, value: any, callback: any) => {
    console.log('validatePassword', value);
    if (value && value.length < 3) {
        callback(new Error('密码需要有至少3位数字'));
    } else {
        callback();
    }
};
const rules = {
    // theme: [{ required: true, message: '请输入会议主题', trigger: 'blur' }], // 会议主题改为非必填
    hostUserId: [{ required: true, message: '请输入主持人', trigger: 'change' }],
    type: [{ required: true, message: '请选择会议类型', trigger: 'blur' }],
    cate: [{ required: true, message: '请选择会议分类', trigger: 'blur' }],
    mode: [{ required: true, message: '请选择会议模式', trigger: 'blur' }],
    time: [{ required: true, message: '请选择会议时间', trigger: 'change' }],
    startTime: [{ required: true, message: '请选择会议时间', trigger: 'change' }],
    // nickname: [{ required: true, message: '请输入主持人名称', trigger: 'change' }], // 主持人参会名称改为非必填
    password: [{ validator: validatePassword, trigger: 'blur' }]
};

// 编辑时部分字段禁用
const editDisabled = ref(false);
const state = reactive({
    isYP: false,
    RtcMeetingInfo: {
        personLimit: 50,
        setting: {
            participationRange: false,
            mute: false,
            allowJoinBeforeHost: true,
            allowAllShare: true,
            allowEditNickname: true,
            allowChat: true,
            allowInvitations: true,
            allowShareLink: true
        },
        duration: 30,
        seetingArr: ['allowJoinBeforeHost', 'allowAllShare', 'allowEditNickname', 'allowChat', 'allowShareLink', 'allowInvitations'],
        detail: {
            actualStartTime: 0,
            actualEndTime: 0,
            actualTheme: '',
            authorName: ''
        }
    } as RtcMeetingDetail
});
const props = defineProps(['meetingId']);
let addId = -1;
const emit = defineEmits(['update:meetingId']);
const getMeetingDetail = async (id: number) => {
    const res = await getMeetingById(id);
    if (res.code === 200) {
        state.RtcMeetingInfo = res.data as RtcMeetingDetail;
        state.RtcMeetingInfo.time = [
            new Date(state.RtcMeetingInfo.startTime),
            new Date(state.RtcMeetingInfo.endTime)
        ];
        editDisabled.value = ((state.RtcMeetingInfo.status || 0) - 0) !== 1 && ((state.RtcMeetingInfo.status || 0) - 0) !== 0;

        const setting: RtcMeetingSetting = (res.data as RtcMeetingDetail).setting;
        const arr = [];
        for (const key in setting) {
            if (setting[key] && key !== 'participationRange') {
                arr.push(key);
            }
        }
        state.RtcMeetingInfo.seetingArr = arr;
        searchText.value = (res.data as RtcMeetingDetail).nickname;
    }
};
// 格式化密码输入
const formatNumber = value => {
    console.log(value);
    state.RtcMeetingInfo.password = value.replace(/[^0-9]/g, '');
    if (value.length > 6) {
        state.RtcMeetingInfo.password = value.slice(0, 6);
    }
};
// 默认选中时间
// 默认开始时间为当前时间后面最近的一个半点 例如现在是 10:20 则默认开始时间为 10:30 现在是 10:40 则默认开始时间为 11:00
// 默认结束时间为开始时间后面的半小时
const defaultTimes = ref([new Date(), new Date()]);
const defaultTime = () => {
    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    if (minute < 30) {
        defaultTimes.value[0] = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour, 30);
    } else {
        defaultTimes.value[0] = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour + 1, 0);
    }
    defaultTimes.value[1] = new Date(defaultTimes.value[0].getTime() + 30 * 60 * 1000);
};
if (props.meetingId) {
    // 获取设备信息
    getMeetingDetail(props.meetingId);
} else {
    defaultTime();
    state.RtcMeetingInfo.time = defaultTimes.value;
}
const datePickerTimes = ref([]);
// 返回
const router = useRouter();
const cancel = () => {
    console.log('返回');
    router.back();
};

// 保存
const save = async (fn?: () => void) => {
    console.log('保存', state.RtcMeetingInfo);
    if (addId > -1) {
        if (fn) {
            fn();
            return;
        } else {
            proxy!.$modal.msgWarning('已经保存了');
            return;
        }
    }
    if (!FormRef.value) return;

    FormRef.value?.validate(async (valid: boolean, invalidFields) => {
        console.log('valid', valid, invalidFields);
        if (valid) {
            // 处理权限
            const settingArr = state.RtcMeetingInfo.seetingArr;
            for (const key in state.RtcMeetingInfo.setting) {
                if (key === 'participationRange') continue;
                state.RtcMeetingInfo.setting[key] = false;
            }
            settingArr?.forEach(item => {
                state.RtcMeetingInfo.setting![item] = true;
            });
            if (!state.RtcMeetingInfo.detail.actualStartTime) {
                delete state.RtcMeetingInfo.detail.actualStartTime;
            } else {
                state.RtcMeetingInfo.detail.actualStartTime = new Date(state.RtcMeetingInfo.detail.actualStartTime).getTime();
            }
            if (!state.RtcMeetingInfo.detail.actualEndTime) {
                delete state.RtcMeetingInfo.detail.actualEndTime;
            } else {
                state.RtcMeetingInfo.detail.actualEndTime = new Date(state.RtcMeetingInfo.detail.actualEndTime).getTime();
            }
            if (!state.RtcMeetingInfo.detail.actualTheme) {
                delete state.RtcMeetingInfo.detail.actualTheme;
            }
            if (!state.RtcMeetingInfo.detail.authorName) {
                delete state.RtcMeetingInfo.detail.authorName;
            }

            // 处理参会名称：如果为空，则使用主持人姓名
            if (!state.RtcMeetingInfo.nickname || state.RtcMeetingInfo.nickname.trim() === '') {
                // 从searchText中提取主持人姓名（格式：姓名--用户名）
                if (searchText.value && searchText.value.includes('--')) {
                    const hostName = searchText.value.split('--')[0];
                    state.RtcMeetingInfo.nickname = hostName;
                }
            }

            // 处理时间
            if (state.RtcMeetingInfo.time && state.RtcMeetingInfo.time.length === 2) {
                state.RtcMeetingInfo.startTime = state.RtcMeetingInfo.time[0].getTime();
                state.RtcMeetingInfo.endTime = state.RtcMeetingInfo.time[1].getTime();
                state.RtcMeetingInfo.hostUserId = state.RtcMeetingInfo.hostUserId - 0;
            }
            // 删除无用字段
            // delete state.RtcMeetingInfo.seetingArr;
            // delete state.RtcMeetingInfo.time;
            delete state.RtcMeetingInfo.meetingNumber;
            delete state.RtcMeetingInfo.createBy;
            delete state.RtcMeetingInfo.createTime;
            delete state.RtcMeetingInfo.linkUrl;
            delete state.RtcMeetingInfo.imConversationId;
            delete state.RtcMeetingInfo.qrCode;
            delete state.RtcMeetingInfo.reason;
            delete state.RtcMeetingInfo.rtcRoomId;
            delete state.RtcMeetingInfo.status;
            delete state.RtcMeetingInfo.updateBy;
            delete state.RtcMeetingInfo.updateTime;
            delete state.RtcMeetingInfo.wbRoomId;
            if (props.meetingId) {
                // 编辑
                try {
                    const res = await updateRtcMeeting(state.RtcMeetingInfo);
                    if (res.code === 200) {
                        proxy!.$modal.msgSuccess('保存成功');
                        if (fn) {
                            fn();
                            return;
                        }
                        // state.RtcMeetingInfo = (res.data || {}) as DmsRtcMeetingInfo
                        // state.RtcMeetingInfo.hospitalId = (res.data as DmsRtcMeetingInfo)?.hospitalVo?.id
                        // if ((res.data as DmsRtcMeetingInfo)?.rabbitmqConfigVo?.id) {
                        //     state.RtcMeetingInfo.rabbitmqConfigId = (res.data as DmsRtcMeetingInfo)?.rabbitmqConfigVo?.id!
                        // }
                        getMeetingDetail((res.data as RtcMeetingDetail).id);
                    }
                } catch (error) {
                }
            } else {
                // 新增
                try {
                    const res = await addRtcMeeting(state.RtcMeetingInfo);
                    if (res.code === 200) {
                        proxy!.$modal.msgSuccess('保存成功');
                        emit('update:meetingId', (res.data as RtcMeetingDetail)?.id!);

                        if (fn) {
                            fn();
                            return;
                        }
                        // state.RtcMeetingInfo = (res.data || {}) as DmsRtcMeetingInfo
                        // state.RtcMeetingInfo.hospitalId = (res.data as DmsRtcMeetingInfo)?.hospitalVo?.id
                        addId = (res.data as RtcMeetingDetail)?.id || -1;
                        // if ((res.data as DmsRtcMeetingInfo)?.rabbitmqConfigVo?.id) {
                        //     state.RtcMeetingInfo.rabbitmqConfigId = (res.data as DmsRtcMeetingInfo)?.rabbitmqConfigVo?.id!
                        // }
                        getMeetingDetail((res.data as RtcMeetingDetail).id);
                    }
                } catch (error) {
                }
            }
        } else {
            return false;
        }
    });
};
// 保存并返回
const saveAndBack = () => {
    save(cancel);
};

// 禁用时间：不能选择当前时间之前的时间
const disabledDate = (time: any) => {
    return dayjs(time).isBefore(dayjs());
};

const handleDateChange = (val: Date[]) => {
    console.log('time change', val);
    if (!val || !val.length) return;

    // 检查开始时间是否在当前时间之前
    if (dayjs(val[0]).isBefore(dayjs())) {
        // 显示警告信息
        proxy!.$modal.msgWarning('开始时间不能选择已经过去的时间');
        // 重新设置默认时间
        defaultTime();
        state.RtcMeetingInfo.time = defaultTimes.value;
        return;
    }
};
// watch(() => state.RtcMeetingInfo.startTime, (nv, ov) => {
//
// })
const orgList = ref([]);
const orgLoading = ref(false);
const getOrgListSel = async (query: string) => {
    orgLoading.value = true;
    const res = await getOrgList({
        query: query,
        pageNum: 1,
        pageSize: 10
    });
    if (res.code === 200) {
        orgList.value = res.data?.rows || [];
    }
    orgLoading.value = false;
    // const res = await getOrgList();
    // if (res.code === 200) {
    //     orgList.value = res.data || [];
    // }
};
// 获取医院名称搜索结果
const querySearch = async (queryString: string, cb: any) => {
    const results = await listDoctorUser({ q_type: 0, q_like_realname: queryString });
    // call callback function to return suggestions
    if (results.code === 200) {
        console.log(results.data, '--1');
        // 判断results.data是一个数组并且长度大于0 使用type

        cb(
            results?.data && (results.data.rows as any[])?.length > 0
                ? (results.data.rows as any[]).map((item: any) => {
                    return {
                        value: item.realname + '--' + item.username,
                        id: item.userid
                    };
                })
                : []
        );
    }
};
const searchText = ref('');
const handleSelect = (item: any) => {
    console.log(item);
    searchText.value = item.value;
    state.RtcMeetingInfo.hostUserId = item.id;
};
const handleClear = () => {
    searchText.value = '';
    state.RtcMeetingInfo.hostUserId = null;
};
const oldType = ref(-1);
const handleTypeChange = () => {
    console.log('handleTypeChange', state.RtcMeetingInfo.cate);
    if (state.RtcMeetingInfo.cate == 5) {
        state.RtcMeetingInfo.seetingArr = state.RtcMeetingInfo.seetingArr?.filter(
            item => item !== 'allowEditNickname' && item !== 'allowInvitations' && item !== 'allowShareLink'
        );
    } else if (oldType.value == 5) {
        state.RtcMeetingInfo.seetingArr = [
            'allowJoinBeforeHost',
            'allowAllShare',
            'allowEditNickname',
            'allowChat',
            'allowShareLink',
            'allowInvitations'
        ];
    }
    oldType.value = state.RtcMeetingInfo.cate;
};
</script>

<template>
    <div class="rtc_meeting_info_base_container">
        <el-form ref="FormRef" :model="state.RtcMeetingInfo" :rules="rules" label-position="top">
            <el-row class="form-con" justify="space-between">
                <el-col :span="24">
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="会议主题" prop="theme">
                                <el-input
                                    v-model="state.RtcMeetingInfo.theme"
                                    maxlength="100"
                                    placeholder="请输入会议主题"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="会议计划时间" prop="time" style="font-weight: 700">
                                <el-date-picker
                                    v-model="state.RtcMeetingInfo.time"
                                    :default-time="defaultTimes"
                                    :disabled="editDisabled"
                                    :disabledDate="disabledDate"
                                    end-placeholder="计划结束时间"
                                    format="YYYY-MM-DD HH:mm"
                                    start-placeholder="计划开始时间"
                                    style="font-weight: 400; width: 100%"
                                    type="datetimerange"
                                    @change="handleDateChange"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="props.meetingId" :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="会议号" prop="type">
                                <el-input
                                    v-model="state.RtcMeetingInfo.meetingNumber"
                                    disabled
                                    placeholder="请输入设备编号"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="会议状态" prop="nativeId">
                                <el-select
                                    v-model="state.RtcMeetingInfo.status"
                                    disabled
                                    placeholder="请选择会议状态"
                                    size="large"
                                >
                                    <el-option
                                        v-for="item in rtc_meeting_status_enum"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="parseInt(item.value)"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="发起人" prop="hostUserId">
                                <el-autocomplete
                                    v-model="searchText"
                                    :fetch-suggestions="querySearch"
                                    :disabled="editDisabled"
                                    clearable
                                    placeholder="请输入发起人名称"
                                    style="width: 240px"
                                    @clear="handleClear"
                                    @select="handleSelect"
                                />
                            </el-form-item>
                        </el-col>

                        <!--                    <el-col :span="9">-->
                        <!--                        <el-form-item label="会议类型" prop="type">-->
                        <!--                            <el-select-->
                        <!--                                v-model="state.RtcMeetingInfo.type"-->
                        <!--                                placeholder="请选择会议类型"-->
                        <!--                                size="large"-->

                        <!--                            >-->
                        <!--                                <el-option-->
                        <!--                                    v-for="item in rtc_meeting_type_enum"-->
                        <!--                                    :key="item.value"-->
                        <!--                                    :label="item.label"-->
                        <!--                                    :value="parseInt(item.value)"-->
                        <!--                                />-->
                        <!--                            </el-select>-->
                        <!--                        </el-form-item>-->
                        <!--                    </el-col>-->

                        <el-col :span="9">
                            <el-form-item label="发起人参会名称" prop="nickname">
                                <el-input
                                    v-model="state.RtcMeetingInfo.nickname"
                                    maxlength="20"
                                    placeholder="请输入发起人参会名称"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="会议模式" prop="mode">
                                <el-radio-group v-model="state.RtcMeetingInfo.mode">
                                    <el-radio
                                        v-for="item in rtc_meeting_mode_enum"
                                        :key="item.value"
                                        :label="parseInt(item.value)"
                                        size="large"
                                    >
                                        {{ item.label }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="会议密码" prop="password">
                                <el-input
                                    v-model="state.RtcMeetingInfo.password"
                                    placeholder="请输入会议密码"
                                    @input="formatNumber"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="会议分类" prop="cate">
                                <el-radio-group
                                    v-model="state.RtcMeetingInfo.cate"
                                    @change="handleTypeChange"
                                >
                                    <el-radio
                                        v-for="item in rtc_meeting_cate_enum"
                                        :key="item.value"
                                        :label="parseInt(item.value)"
                                        size="large"
                                    >
                                        {{ item.label }}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="9">
                            <el-form-item label="参会人数限制" prop="personLimit">
                                <el-input
                                    v-model="state.RtcMeetingInfo.personLimit"
                                    placeholder="请输入参会人数限制"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="入会范围" prop="participationRange">
                                <el-radio-group v-model="state.RtcMeetingInfo.setting!.participationRange">
                                    <el-radio :label="false" size="large">所有人可入会</el-radio>
                                    <el-radio :label="true" size="large">只有添加的参会人可入会</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <!--                        <el-col :span="9">-->
                        <!--                            <el-form-item label="会议组织" prop="orgIds">-->
                        <!--                                <el-select-->
                        <!--                                    v-model="state.RtcMeetingInfo.orgIds"-->
                        <!--                                    :loading="orgLoading"-->
                        <!--                                    :remote-method="getOrgListSel"-->
                        <!--                                    clearable-->
                        <!--                                    filterable-->
                        <!--                                    multiple-->
                        <!--                                    placeholder="请选择组织"-->
                        <!--                                    remote-->
                        <!--                                    reserve-keyword-->
                        <!--                                    style="width: 240px"-->
                        <!--                                >-->
                        <!--                                    <el-option-->
                        <!--                                        v-for="dict in orgList"-->
                        <!--                                        :key="dict.id"-->
                        <!--                                        :label="dict.name"-->
                        <!--                                        :value="dict.id"-->
                        <!--                                    />-->
                        <!--                                </el-select>-->
                        <!--                            </el-form-item>-->
                        <!--                        </el-col>-->

                        <el-col :span="9">
                            <el-form-item label="参会人权限">
                                <el-checkbox-group v-model="state.RtcMeetingInfo.seetingArr">
                                    <el-checkbox label="mute"> 入会时静音</el-checkbox>
                                    <el-checkbox label="allowJoinBeforeHost">
                                        允许在主持人之前入会
                                    </el-checkbox>
                                    <el-checkbox label="allowAllShare"> 所有人可以共享</el-checkbox>
                                    <el-checkbox label="allowEditNickname"> 允许在会中修改姓名</el-checkbox>
                                    <el-checkbox label="allowChat"> 允许在会中聊天</el-checkbox>
                                    <el-checkbox label="allowInvitations"> 允许邀请参会人</el-checkbox>
                                    <el-checkbox label="allowShareLink"> 允许分享会议</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="商务">
                                <el-input
                                    v-model="state.RtcMeetingInfo.businessPerson"
                                    maxlength="50"
                                    placeholder="请输入商务"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="9">
                            <el-form-item label="工程师">
                                <el-input
                                    v-model="state.RtcMeetingInfo.engineer"
                                    maxlength="50"
                                    placeholder="请输入工程师"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="state.RtcMeetingInfo.cate == 5" :gutter="30" justify="start">
                        <el-col :span="9">
                            <el-form-item label="讲者姓名">
                                <el-input
                                    v-model="state.RtcMeetingInfo.detail.authorName"
                                    placeholder="请输入讲者姓名"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="雅培PR号">
                                <el-input
                                    v-model="state.RtcMeetingInfo.detail.actualTheme"
                                    placeholder="请输入雅培PR号"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="state.RtcMeetingInfo.cate == 5" :gutter="30" justify="start">
                        <el-col :span="9">
                            <el-form-item label="实际开始时间">
                                <el-date-picker
                                    v-model="state.RtcMeetingInfo.detail.actualStartTime"
                                    format="YYYY-MM-DD HH:mm"
                                    placeholder="开始时间"
                                    style="font-weight: 400"
                                    type="datetime"
                                    value-format="x"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="实际结束时间">
                                <el-date-picker
                                    v-model="state.RtcMeetingInfo.detail.actualEndTime"
                                    format="YYYY-MM-DD HH:mm"
                                    placeholder="结束时间"
                                    style="font-weight: 400"
                                    type="datetime"
                                    value-format="x"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="30">
                        <el-col :span="18">
                            <el-form-item label="描述">
                                <el-input
                                    v-model="state.RtcMeetingInfo.description"
                                    maxlength="500"
                                    placeholder="请输入描述"
                                    type="textarea"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="24" style="height: 100px">
                    <el-row justify="end">
                        <el-button @click="cancel">返回</el-button>
                        <el-button type="primary" @click="() => save()">保存</el-button>
                        <el-button type="primary" @click="() => saveAndBack()">保存并返回</el-button>
                    </el-row>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
.rtc_meeting_info_base_container {
    :deep(.el-card__body) {
        padding: 0 !important;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0 0 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        height: 40px !important;
    }

    :deep(.el-input__wrapper) {
        width: 100% !important;
    }

    :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
        width: 100% !important;
    }
}
</style>
