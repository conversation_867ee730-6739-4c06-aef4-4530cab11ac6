import { defineComponent, getCurrentInstance, onMounted, reactive } from 'vue';
import { ElButton, ElCol, ElDialog, ElForm, ElFormItem, ElInput, ElRow, ElTable, ElTableColumn } from 'element-plus';
import { RefsKes } from '@/type/common';
import RightToolar from '@/components/RightToolbar/index.vue';
import moment from 'moment';
import Pagination from '@/components/Pagination/index.vue';
import { EvmsConsultExpert, listExpert } from '@/api/evms/expert';
import { EvmsConsultHospitalExpert, hospitalUnbindingDoc } from '@/api/evms/consult_hospital';
import ExpertDoc from './expertDoc';

interface QueryParams extends Partial<EvmsConsultExpert> {
    pageNum: number;
    pageSize: number;
    hospitalId: number;
    hospitalBinding: boolean;
}

export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        let instance: RefsKes;

        const state = reactive({
            expertList: [] as EvmsConsultExpert[],
            visible: false,
            showSearch: true,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                realName: '',
                hospitalBinding: true,
                hospitalId: 0
            } as QueryParams,
            loading: false,
            total: 0,
            single: true,
            multiple: false,
            currentRow: null as EvmsConsultExpert | null,
            oldCurrentRow: null as EvmsConsultExpert | null
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function close() {
            state.visible = false;
        }

        function show(record: EvmsConsultHospitalExpert) {
            state.visible = true;
            state.queryParams.hospitalId = record.hospitalId;
            handleQuery();
        }

        function handleDelete(record: EvmsConsultExpert) {
            proxy?.$modal
                .confirm('是否确认删除？')
                .then(() => {
                    return hospitalUnbindingDoc({
                        expertId: record.id,
                        hospitalId: state.queryParams.hospitalId
                    });
                })
                .then(() => {
                    getList();
                    proxy?.$modal.msgSuccess('删除成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        const slots = {
            column: {
                serviceCount({ row: record }: { row: EvmsConsultExpert }) {
                    return <p style="color:#0581ce;">{record.serviceCount}</p>;
                },
                signingStartTimeStamp({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingStartTimeStamp
                                ? moment(record.signingStartTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingEndTimeStamp({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingEndTimeStamp
                                ? moment(record.signingEndTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingStatus({ row: record }: { row: EvmsConsultExpert }) {
                    return <p>{record.signingStatus ? '已签约' : '未签约'}</p>;
                }
            },
            actionSlots({ row: record }: { row: EvmsConsultExpert }) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:consult_hospital:remove']]}
                            link
                            type="primary"
                            onClick={() => handleDelete(record)}
                        >
                            删除
                        </ElButton>
                    </>
                );
            },
            modalFooter: {}
        };

        function getList() {
            state.loading = true;
            listExpert(state.queryParams).then(response => {
                state.expertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        function resetQuery() {
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleAdd() {
            instance.refs.ExpertDocRef.show({ hospitalId: state.queryParams.hospitalId, type: 'binding' });
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="已绑定的专家列表"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="专家名称" prop="expertName">
                        <ElInput
                            v-model={state.queryParams.expertName}
                            placeholder="请输入专家名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}
                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['sys:test:add']]}
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={handleAdd}
                        >
                            添加
                        </ElButton>
                    </ElCol>
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable v-loading={state.loading} data={state.expertList}>
                    <ElTableColumn label="专家名称" align="center" prop="realName" />
                    <ElTableColumn
                        label="签约状态"
                        align="center"
                        prop="signingStatus"
                        v-slots={slots.column.signingStatus}
                    />
                    <ElTableColumn
                        label="服务总次数"
                        align="center"
                        v-slots={slots.column.serviceCount}
                        prop=""
                    />
                    <ElTableColumn
                        label="签约时间"
                        align="center"
                        prop="signingStartTimeStamp"
                        width="180"
                        v-slots={slots.column.signingStartTimeStamp}
                    />
                    <ElTableColumn
                        label="到期时间"
                        align="center"
                        prop="signingEndTimeStamp"
                        width="180"
                        v-slots={slots.column.signingEndTimeStamp}
                    />
                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    ></ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
                <ExpertDoc onOk={() => handleQuery()} ref="ExpertDocRef"></ExpertDoc>
            </ElDialog>
        );
    }
});
