import auth from '@/plugins/auth';
import router, { constantRoutes, dynamicRoutes } from '@/router';
import { getRouters } from '@/api/menu';
import Layout from '@/layout/index.vue';
import ParentView from '@/components/ParentView/index.vue';
import InnerLink from '@/layout/components/InnerLink/index.vue';
import { defineStore } from 'pinia';
import { RouteRecordRaw } from 'vue-router';

// 匹配views里面所有的.vue文件
let modules = import.meta.glob('./../../views/**/*.vue');
let tsxModules = import.meta.glob('./../../views/**/*.tsx');
modules = Object.assign(modules, tsxModules);
const usePermissionStore = defineStore('permission', {
    state: (): {
        routes: RouteRecordRaw[];
        addRoutes: RouteRecordRaw[];
        defaultRoutes: RouteRecordRaw[];
        topbarRouters: RouteRecordRaw[];
        sidebarRouters: RouteRecordRaw[];
    } => ({
        routes: [],
        addRoutes: [],
        defaultRoutes: [],
        topbarRouters: [],
        sidebarRouters: [],
    }),
    actions: {
        setRoutes(routes: RouteRecordRaw[]) {
            this.addRoutes = routes;
            this.routes = constantRoutes.concat(routes);
        },
        setDefaultRoutes(routes: RouteRecordRaw[]) {
            this.defaultRoutes = constantRoutes.concat(routes);
        },
        setTopbarRoutes(routes: RouteRecordRaw[]) {
            this.topbarRouters = routes;
        },
        setSidebarRouters(routes: RouteRecordRaw[]) {
            this.sidebarRouters = routes;
        },
        generateRoutes(routes?: RouteRecordRaw[]) {
            return new Promise<any[]>(resolve => {
                // 向后端请求路由数据
                getRouters().then(res => {
                    console.log('resolve', res.data);
                    const sdata = JSON.parse(JSON.stringify(res.data));
                    const rdata = JSON.parse(JSON.stringify(res.data));
                    const defaultData = JSON.parse(JSON.stringify(res.data));
                    const sidebarRoutes = filterAsyncRouter(sdata);
                    const rewriteRoutes = filterAsyncRouter(rdata, false, true);
                    const defaultRoutes = filterAsyncRouter(defaultData);
                    const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
                    asyncRoutes.forEach(route => {
                        router.addRoute(route);
                    });
                    console.log('rewriteRoutes', rewriteRoutes);
                    this.setRoutes(rewriteRoutes);
                    this.setSidebarRouters(constantRoutes.concat(sidebarRoutes));
                    this.setDefaultRoutes(sidebarRoutes);
                    this.setTopbarRoutes(defaultRoutes);
                    resolve(rewriteRoutes);
                });
            });
        },
    },
});

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap: any[], lastRouter = false, type = false) {
    return asyncRouterMap.filter(route => {
        if (type && route.children) {
            route.children = filterChildren(route.children, false, route.path);
        }
        if (route.component) {
            // Layout ParentView 组件特殊处理
            if (route.component === 'Layout') {
                route.component = Layout;
            } else if (route.component === 'ParentView') {
                route.component = ParentView;
            } else if (route.component === 'InnerLink') {
                route.component = InnerLink;
            } else {
                route.component = loadView(route.component);
            }
        }
        if (route.children != null && route.children && route.children.length) {
            route.children = filterAsyncRouter(route.children, route, type);
        } else {
            delete route['children'];
            delete route['redirect'];
        }
        return true;
    });
}

function filterChildren(childrenMap: any[], lastRouter: any = false, previousPath = '') {
    let children: any[] = [];
    childrenMap.forEach((el, index) => {
        if (el.children && el.children.length) {
            if (el.component === 'ParentView' && !lastRouter) {
                el.children.forEach((c: any) => {
                    c.path = el.path + '/' + c.path;
                    if (c.children && c.children.length) {
                        children = children.concat(filterChildren(c.children, false, c));
                        return;
                    }
                    children.push(c);
                });
                el.children.forEach((c: any) => {
                    el.children.forEach(d => {
                        if (c.path.includes('_info') && c.path.startsWith(d.path) && c.path !== d.path) {
                            c.meta = Object.assign({}, c.meta, {
                                activeMenu: previousPath + '/' + d.path,
                            });
                        }
                    });
                });
                return;
            }
        }
        if (lastRouter) {
            el.path = lastRouter.path + '/' + el.path;
        }
        // 如果某个子菜单地址前面的部分和某个同级菜单的地址相同 则将其认为是那个同级菜单的子菜单
        // if (el?.children?.length === 0 && !lastRouter) {
        //     childrenMap.forEach((c => {
        //         if (el.path.includes('_info') && el.path.startsWith(c.path) && el.path !== c.path) {
        //             el.meta = Object.assign({}, el.meta, {
        //                 activeMenu: previousPath + '/' + c.path,
        //             });
        //         }
        //     }))
        // }
        children = children.concat(el);
    });
    return children;
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes: any[]) {
    const res: any[] = [];
    routes.forEach(route => {
        if (route.permissions) {
            if (auth.hasPermiOr(route.permissions)) {
                res.push(route);
            }
        } else if (route.roles) {
            if (auth.hasRoleOr(route.roles)) {
                res.push(route);
            }
        }
    });
    return res;
}

export const loadView = (view: any) => {
    let res;
    for (const path in modules) {
        const dir = path.split('views/')[1].split('.vue')[0];
        const dir1 = path.split('views/')[1].split('.tsx')[0];
        if (dir === view || dir1 === view) {
            res = () => modules[path]();
        }
    }
    return res;
};

export default usePermissionStore;
