import { createI18n } from 'vue-i18n';
import { Language } from 'element-plus/es/locale';
import ElZhCn from 'element-plus/es/locale/lang/zh-cn';
import ElEn from 'element-plus/es/locale/lang/en';
import zhCn from './locales/zh-cn';

const messages = {
    'zh-cn': {
        ...zhCn
    }
};

const numberFormats = {
    'zh-cn': {
        decimal: {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }
    },
    en: {
        decimal: {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }
    }
};

const elMessages: Record<string, Language> = {
    'zh-cn': ElZhCn,
    en: ElEn
};

export const languages: Record<string, string> = { 'zh-cn': '中文', en: 'English' };

const i18nFallbackLocale = 'zh-cn';

export function getElementPlusLocale(lang: string): Language {
    return elMessages[lang] ?? elMessages[i18nFallbackLocale] ?? ElZhCn;
}

export function getLanguage(): string {
    return 'zh-cn';
}

export default createI18n({
    legacy: false,
    __VUE_I18N_LEGACY_API__: false,
    __VUE_I18N_FULL_INSTALL__: false,
    locale: getLanguage(),
    fallbackLocale: i18nFallbackLocale,
    globalInjection: true,
    numberFormats,
    messages
});
