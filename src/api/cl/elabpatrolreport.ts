import request from '@/utils/request';

// 查询线下巡检报告列表
export function listElabpatrolreport(query: any) {
    return request({
        url: '/cl/elab/patrol/report/list',
        method: 'get',
        params: query,
    });
}

// 查询线下巡检报告详细
export function getElabpatrolreport(id: any) {
    return request({
        url: '/cl/elab/patrol/report/' + id,
        method: 'get',
    });
}

// 新增线下巡检报告
export function addElabpatrolreport(data: any) {
    return request({
        url: '/cl/elab/patrol/report',
        method: 'post',
        data: data,
    });
}

// 修改线下巡检报告
export function updateElabpatrolreport(data: any) {
    return request({
        url: '/cl/elab/patrol/report',
        method: 'put',
        data: data,
    });
}

// 删除线下巡检报告
export function delElabpatrolreport(id: any) {
    return request({
        url: '/cl/elab/patrol/report/' + id,
        method: 'delete',
    });
}
