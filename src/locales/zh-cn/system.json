{"site.name": "名称", "site.copyFrom": "复制站点", "site.copyFrom.tooltip": "新增站点将以此站点为基础，复制该站点的模板、模型、栏目、文章等数据", "site.copyData": "复制数据", "site.copyData.tooltip": "选择要复制的数据", "site.copyData.template": "模板", "site.copyData.model": "模型", "site.copyData.channel": "栏目", "site.copyData.article": "文章", "site.parent": "上级站点", "site.org": "所属组织", "site.org.tooltip": "如组织有市政府、区政府，则可使市政府网站归属市政府组织，区政府网站归属区政府组织", "site.model": "自定义设置模型", "site.model.tooltip": "“配置 - 站点设置 - 自定义设置” 功能中使用的模型", "site.protocol": "协议", "site.protocol.tooltip": "此协议仅用于生成网站的链接地址，即链接地址以 http:// 开头或以 https:// 开头。如使用https协议，请在服务器上进行相应设置以支持https协议", "site.domain": "域名", "site.domain.tooltip": "不同站点可使用不同域名，如多个站点使用同一域名，需用“子目录”区分各个站点。要确保使用的域名已解析到服务器。注意：不要加http或https协议前缀。如 www.example.com、localhost、127.0.0.1 等。", "site.subDir": "子目录", "site.subDir.tooltip": "如多个站点使用同一域名，则必须用子目录区分站点。如子目录为：abc，则站点的访问路径类似为: https://www.example.com/abc。留空则使用域名区分站点", "site.url": "URL", "site.theme": "模板主题", "site.theme.tooltip": "网站前台页面的模板，位于程序的 /templates 目录，切换不同模板可以改变网站前台显示的页面", "site.mobileTheme": "手机端模板主题", "site.mobileTheme.tooltip": "使用手机浏览器（不含平板）访问时，网站前台页面使用的模板。如与“模板主题”不一致，可使手机端访问和PC端访问呈现不同页面", "site.created": "创建日期", "site.logo": "LOGO", "site.logo.tooltip": "模板调用代码：{'${site.logo}'}", "site.seoTitle": "SEO标题", "site.seoTitle.tooltip": "模板调用代码：{'${site.title}'}。如该值未设置，则获取站点名称", "site.seoKeywords": "SEO关键词", "site.seoKeywords.tooltip": "模板调用代码：{'${site.seoKeywords!}'}", "site.seoDescription": "SEO描述", "site.seoDescription.tooltip": "模板调用代码：{'${site.seoDescription!}'}", "site.pageSize": "每页条数", "site.pageSize.tooltip": "首页分页的每页条数，或者首页下拉时加载的文章条数", "site.status": "状态", "site.status.tooltip": "站点的状态", "site.status.0": "正常", "site.status.1": "关闭", "site.error.domainPattern": "域名只能由小写字母、数字、中划线、点号组成", "site.error.excludesPattern": "不能为uploads、templates、WEB-INF、cp", "site.error.subDirPattern": "子目录只能由字母、数字、中划线、下划线组成", "processModel.op.design": "流程设计", "processModel.op.definition": "部署列表", "processModel.op.deploy": "部署", "processModel.op.xmlPreview": "XML预览", "processModel.op.xmlValidate": "检查流程", "processModel.key": "标识", "processModel.key.placeholder": "新增后不可修改", "processModel.category": "类别", "processModel.category.tooltip": "新增后不可修改", "processModel.name": "名称", "processModel.version": "版本", "processModel.createTime": "创建时间", "processModel.lastUpdateTime": "更新时间", "processModel.error.key": "必须以字母开头，由字母、数字、中划线、下划线组成", "processInstance.op.task": "审核过程", "processInstance.history": "审核历史", "processInstance.name": "名称", "processInstance.businessKey": "业务ID", "processInstance.startedBy": "发起人", "processInstance.assignee": "处理人", "processInstance.status": "状态", "processInstance.status.complete": "通过", "processInstance.status.reject": "拒绝", "processInstance.status.cancel": "取消", "processInstance.opinion": "意见", "processInstance.started": "开始时间", "processInstance.ended": "结束时间", "processInstance.deleteReason": "原因", "processActivity.name": "名称", "processActivity.type": "类型", "processActivity.assignee": "处理人", "processActivity.durationInMillis": "耗时", "task.name": "名称", "task.site": "站点", "task.user": "操作人", "task.beginDate": "开始时间", "task.endDate": "结束时间", "task.current": "完成数", "task.processedIn": "耗时(秒)", "task.total": "总数量", "task.type": "类型", "task.type.1": "生成静态页", "task.type.2": "生成全文索引", "task.status": "状态", "task.status.0": "等待", "task.status.1": "运行中", "task.status.2": "出错", "task.status.3": "停止", "task.status.4": "完成", "task.errorInfo": "错误信息", "license.activated": "是否激活", "license.activated.true": "已激活", "license.activated.false": "未激活", "license.status": "许可状态", "license.status.0": "已激活", "license.status.1": "无许可证书", "license.status.2": "许可证已过期", "license.status.3": "软件版本不匹配", "license.status.4": "软件等级不匹配", "license.status.5": "站点数量超过限制", "license.status.6": "机器码不匹配", "license.status.7": "域名不匹配", "license.reason": "未激活原因", "license.domains": "网站域名", "license.names": "网站名称", "license.limit": "站点数量", "license.limit.0": "不限制", "license.version": "软件版本", "license.rank": "软件等级", "license.expires": "许可期限", "license.expires.never": "永久", "license.subjectName": "主体名称", "license.subjectCode": "主体代码", "license.subjectType": "主体类型", "license.subjectType.1": "公司", "license.subjectType.2": "个人", "": ""}