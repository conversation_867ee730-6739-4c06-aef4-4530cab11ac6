/* eslint-disable */
// 根据你安装路径确定下面两个包的路径
import archiver from 'archiver';
import SSH2 from 'ssh2';
import { exec } from 'child_process';
import path from 'path';
import fs from 'fs';
// 使用的3.0以上的vue环境，不能使用require 所以__dirname不能使用
import { fileURLToPath } from 'url';

const __filenameNew = fileURLToPath(import.meta.url);

const __dirnameNew = path.dirname(__filenameNew);
const Client = SSH2.Client;

console.log(path.join(__dirnameNew, '/a'));
const productionEnv = [
    {
        host: '*************', // 服务器ip地址或域名
        password: 'II@5%@frSyXWmw@H', // 密码，请勿将此密码上传至git服务器
        catalog: '/opt/web/evms-dev', // 前端文件压缩目录 // 最后一级不需要斜线。并且路径必须时真实存在的
        port: '22', // 服务器ssh连接端口号
        username: 'ecs-user' // ssh登录用户
    }
];
// 全局配置
const Config = {
    publishEnv: productionEnv,
    buildDist: 'dist', // 前端文件打包之后的目录，默认dist
    buildCommand: 'yarn run build:test', // 打包前端文件的命令
    readyTimeout: 20000, // ssh连接超时时间
    deleteServerZipFile: true // 是否删除线上上传的dist压缩包
};
let args = process.argv.slice(2);
if (args[0] === 'prod') {
    Config.buildCommand = 'yarn run build:prod';
    Config.publishEnv[0].host = '**************';
    Config.publishEnv[0].password = '$o#WFudQYNLg^Iiv';
    Config.publishEnv[0].catalog = '/opt/web/evms';
}

// console.log(process.env)
/**
 * ssh连接
 */
class SSH {
    constructor({ host, port, username, password, agent }) {
        this.server = {
            host,
            port,
            username,
            password
        };

        this.conn = new Client();
    } // 连接服务器

    connectServer() {
        return new Promise((resolve, reject) => {
            let conn = this.conn;

            conn.on('ready', () => {
                resolve({
                    success: true
                });
            })
                .on('error', err => {
                    reject({
                        success: false,
                        error: err
                    });
                })
                .on('end', () => {
                    console.log('----SSH连接已结束----');
                })
                .on('close', had_error => {
                    console.log('----SSH连接已关闭----');
                })
                .connect(this.server);
        });
    } // 上传文件

    uploadFile({ localPath, remotePath }) {
        return new Promise((resolve, reject) => {
            return this.conn.sftp((err, sftp) => {
                if (err) {
                    reject({
                        success: false,
                        error: err
                    });
                } else {
                    sftp.fastPut(localPath, remotePath, (err, result) => {
                        if (err) {
                            reject({
                                success: false,
                                error: err
                            });
                        }
                        resolve({
                            success: true,
                            result
                        });
                    });
                }
            });
        });
    } // 执行ssh命令

    execSsh(command) {
        return new Promise((resolve, reject) => {
            return this.conn.exec(command, (err, stream) => {
                if (err || !stream) {
                    reject({
                        success: false,
                        error: err
                    });
                } else {
                    stream
                        .on('close', (code, signal) => {
                            resolve({
                                success: true
                            });
                        })
                        .on('data', function(data) {
                            console.log(data.toString());
                        })
                        .stderr.on('data', function(data) {
                        resolve({
                            success: false,
                            error: data.toString()
                        });
                    });
                }
            });
        });
    } // 结束连接

    endConn() {
        this.conn.end();
        if (this.connAgent) {
            this.connAgent.end();
        }
    }
}

/*
 * 本地操作
 * */
class File {
    constructor(fileName) {
        this.fileName = fileName;
    } // 删除本地文件

    deleteLocalFile() {
        return new Promise((resolve, reject) => {
            fs.unlink(this.fileName, function(error) {
                if (error) {
                    reject({
                        success: false,
                        error
                    });
                } else {
                    resolve({
                        success: true
                    });
                }
            });
        });
    } // 压缩文件夹下的所有文件

    zipFile(filePath) {
        return new Promise((resolve, reject) => {
            // 创建文件输出流
            let output = fs.createWriteStream(__dirnameNew + '/' + this.fileName);
            console.log(99);
            let archive = archiver('zip', {
                zlib: {
                    level: 9
                } // 设置压缩级别
            });
            console.log(3); // 文件输出流结束
            output.on('close', function() {
                console.log(`----压缩文件总共 ${archive.pointer()} 字节----`);
                console.log('----压缩文件夹完毕----');
                resolve({
                    success: true
                });
            }); // 数据源是否耗尽
            output.on('end', function() {
                console.error('----压缩失败，数据源已耗尽----');
                reject();
            }); // 存档警告
            archive.on('warning', function(err) {
                if (err.code === 'ENOENT') {
                    console.error('----stat故障和其他非阻塞错误----');
                } else {
                    console.error('----压缩失败----');
                }
                reject(err);
            }); // 存档出错
            archive.on('error', function(err) {
                console.error('----存档错误，压缩失败----');
                console.error(err);
                reject(err);
            });
            console.log(4); // 通过管道方法将输出流存档到文件
            archive.pipe(output);
            console.log(5); // 打包dist里面的所有文件和目录
            archive.directory(filePath, false); // archive.directory(`../${Config.buildDist}/`, false); // 完成归档
            archive.finalize();
        });
    } // 打包本地前端文件

    buildProject() {
        console.log('----开始编译打包文件，请耐心等待----');
        return new Promise((resolve, reject) => {
            exec(Config.buildCommand, async (error, stdout, stderr) => {
                if (error) {
                    console.error(error);
                    reject({
                        error,
                        success: false
                    });
                } else if (stdout) {
                    resolve({
                        stdout,
                        success: true
                    });
                } else {
                    console.error(stderr);
                    reject({
                        error: stderr,
                        success: false
                    });
                }
            });
        });
    } // 停止程序之前需删除本地压缩包文件

    stopProgress() {
        this.deleteLocalFile()
            .catch(e => {
                console.error('----删除本地文件失败，请手动删除----');
                console.error(e);
            })
            .then(() => {
                console.log('----已删除本地压缩包文件----');
            });
    }
}

// SSH连接，上传，解压，删除等相关操作
async function sshUpload(sshConfig, fileName) {
    let sshCon = new SSH(sshConfig);
    let sshRes = await sshCon.connectServer().catch(e => {
        console.error(e);
    });
    if (!sshRes || !sshRes.success) {
        console.error('----连接服务器失败，请检查用户名密码是否正确以及服务器是否已开启远程连接----');
        return false;
    }

    console.info('----连接成功,开始清除目标文件夹中的内容----');

    await sshCon.execSsh(`cd /opt/`).catch(e => {
    });
    await sshCon.execSsh(`mkdir ${sshConfig.catalog}`).catch(e => {
    });
    await sshCon.execSsh(`mkdir ${sshConfig.catalog}-bak`).catch(e => {
    });
    await sshCon.execSsh(`rm -rf ${sshConfig.catalog}-bak/*`).catch(e => {
    });

    console.log('----开始备份文件----');
    await sshCon.execSsh(`cp -r  ${sshConfig.catalog}/*  ${sshConfig.catalog}-bak/`).catch(e => {
    });
    console.log('----备份文件完成----'); // 注意：rm -rf为危险操作，请勿对此段代码做其他非必须更改
    let deleteTargetDir = await sshCon.execSsh(`rm -rf ${sshConfig.catalog + '/*'}`).catch(e => {
    });
    if (!deleteTargetDir || !deleteTargetDir.success) {
        console.error('----清空失败，请手清空----');
        console.error(`----错误原因：${deleteTargetDir.error}----`);
    }
    console.info('----清除目标文件夹中的内容成功----');

    console.log('----开始上传文件----');
    console.log(fileName);
    console.log(sshConfig.catalog + '/' + fileName);

    let uploadRes = await sshCon
        .uploadFile({
            localPath: path.resolve(__dirnameNew, fileName),
            remotePath: sshConfig.catalog + '/' + fileName
        })
        .catch(e => {
            console.error(e);
        });

    if (!uploadRes || !uploadRes.success) {
        console.error('----上传文件失败，请重新上传----');
        return false;
    }
    console.log('----上传文件成功，开始解压文件----');

    let zipRes = await sshCon
        .execSsh(`unzip -o ${sshConfig.catalog + '/' + fileName} -d ${sshConfig.catalog}`)
        .catch(e => {
        });
    if (!zipRes || !zipRes.success) {
        console.error('----解压文件失败，请手动解压zip文件----');
        console.error(`----错误原因：${zipRes.error}----`);
    }
    if (Config.deleteServerZipFile) {
        console.log('----解压文件成功，开始删除上传的压缩包----'); // 注意：rm -rf为危险操作，请勿对此段代码做其他非必须更改

        let deleteZipRes = await sshCon
            .execSsh(`rm -rf ${sshConfig.catalog + '/' + fileName}`)
            .catch(e => {
            });
        if (!deleteZipRes || !deleteZipRes.success) {
            console.error('----删除文件失败，请手动删除zip文件----');
            console.error(`----错误原因：${deleteZipRes.error}----`);
        }
    } // 结束ssh连接
    sshCon.endConn();
}

// 执行自动部署
(async () => {
    // 压缩包的名字
    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    let timeStr = `${year}_${month}_${day}`;
    const fileName = `${Config.buildDist}-` + timeStr + '-' + Math.random().toString(16).slice(2) + '.zip';

    let file = new File(fileName); // 打包文件

    let buildRes = await file.buildProject().catch(e => {
        console.error(e);
    });
    if (!buildRes || !buildRes.success) {
        console.error('----编译打包文件出错----');
        return false;
    }
    console.log(buildRes.stdout);
    console.log('----编译打包文件完成----'); // 压缩文件

    let res = await file.zipFile(`${Config.buildDist}/`).catch(() => {
    });
    console.log(res, 'res');
    if (!res || !res.success) return false;
    console.log('----开始进行SSH连接----');

    if (Config.publishEnv instanceof Array && Config.publishEnv.length) {
        for (let i = 0; i < Config.publishEnv.length; i++) {
            await sshUpload(Config.publishEnv[i], fileName);
        }
    } else {
        await sshUpload(Config.publishEnv, fileName);
    }

    console.log('----部署成功，正在为您删除本地压缩包----');
    file.stopProgress();
})();
