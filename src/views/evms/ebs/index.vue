<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="分组编号" prop="groupId">
                <el-input
                    v-model="queryParams.groupId"
                    placeholder="请输入分组编号"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="病例标题" prop="title">
                <el-input
                    v-model="queryParams.title"
                    placeholder="请输入病例标题"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="是否在回收站" prop="recycleBin">
                <el-select v-model="queryParams.recycleBin" placeholder="请选择是否在回收站" clearable>
                    <el-option
                        v-for="dict in evms_recycle_bin"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="所属用户" prop="userId">
                <el-input
                    v-model="queryParams.userId"
                    placeholder="请输入所属用户"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item label="所属用户名" prop="username">
                <el-input
                    v-model="username"
                    placeholder="请输入所属用户名"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['evms:ebs:add']"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['evms:ebs:edit']"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['evms:ebs:remove']"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handlePhysicallyRemove"
                    v-hasPermi="['evms:ebs:remove']"
                >彻底删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['evms:ebs:export']"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="EbsList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="分组编号" align="center" prop="groupId" />
            <el-table-column label="病例标题" align="center" prop="title" />
            <el-table-column label="病例文件url" align="center" prop="fileUrl" />
            <el-table-column label="是否在回收站" align="center" prop="recycleBin">
                <template #default="scope">
                    <dict-tag :options="evms_recycle_bin" :value="scope.row.recycleBin" />
                </template>
            </el-table-column>
            <el-table-column label="封面" align="center" prop="cover" width="100">
                <template #default="scope">
                    <image-preview :src="scope.row.cover" :width="50" :height="50" />
                </template>
            </el-table-column>
            <el-table-column label="所属用户" align="center" prop="userId" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:ebs:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:ebs:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改EBS病例对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="EbsRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="分组编号" prop="groupId">
                    <el-input v-model="form.groupId" placeholder="请输入分组编号" />
                </el-form-item>
                <el-form-item label="病例标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入病例标题" />
                </el-form-item>
                <el-form-item label="病例文件url" prop="fileUrl">
                    <file-upload v-model="form.fileUrl" />
                </el-form-item>
                <el-form-item label="是否在回收站" prop="recycleBin">
                    <el-select v-model="form.recycleBin" placeholder="请选择是否在回收站">
                        <el-option
                            v-for="dict in evms_recycle_bin"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="封面" prop="cover">
                    <image-upload v-model="form.cover" />
                </el-form-item>
                <el-form-item label="所属用户" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入所属用户" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Ebs" lang="ts">
import { listEbs, getEbs, delEbs, addEbs, updateEbs, physicallyRemove } from '@/api/evms/ebs';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_recycle_bin } = proxy!.useDict('evms_recycle_bin');

const EbsList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const username = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupId: undefined,
        title: undefined,
        fileUrl: undefined,
        recycleBin: undefined,
        cover: undefined,
        userId: undefined,
        username: undefined
    },
    rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询EBS病例列表 */
function getList() {
    loading.value = true;
    let params = queryFormat(queryParams.value);
    params.username = username.value;
    listEbs(params)
        .then((response: any) => {
            EbsList.value = response.data.rows;
            total.value = response.data.total;
            loading.value = false;
        })
        .catch(() => {
            loading.value = false;
        });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        groupId: undefined,
        title: undefined,
        fileUrl: undefined,
        recycleBin: undefined,
        cover: undefined,
        userId: undefined,
        createBy: undefined,
        updateBy: undefined,
        createTime: undefined,
        updateTime: undefined
    };
    proxy!.resetForm('EbsRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加EBS病例';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getEbs(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改EBS病例';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['EbsRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateEbs(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addEbs(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除EBS病例编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delEbs(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 彻底删除按钮操作 */
function handlePhysicallyRemove(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认彻底删除EBS病例编号为"' + _ids + '"的数据项？')
        .then(function() {
            return physicallyRemove(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'evms/Ebs/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `Ebs_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
<style scoped lang="scss">
// 调整样式
:deep(.el-table .el-table__cell) {
    position: inherit;
}
</style>
