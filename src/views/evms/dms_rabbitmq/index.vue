<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="rabbitmq服务器名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入rabbitmq服务器名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="服务器ip地址" prop="ip">
                <el-input
                    v-model="queryParams.ip"
                    placeholder="请输入服务器ip地址"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="端口号" prop="port">
                <el-input
                    v-model="queryParams.port"
                    placeholder="请输入端口号"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <template v-if="isExpand">
                <el-form-item label="服务器用户名" prop="user">
                    <el-input
                        v-model="queryParams.user"
                        placeholder="请输入服务器用户名"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="服务器密码" prop="pwd">
                    <el-input
                        v-model="queryParams.pwd"
                        placeholder="请输入服务器密码"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="虚拟host名称" prop="vhost">
                    <el-input
                        v-model="queryParams.vhost"
                        placeholder="请输入虚拟host名称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
            </template>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="text" @click="isExpand = !isExpand">
                    {{ isExpand ? '收起' : '展开' }}
                    <el-icon class="el-icon--right">
                        <arrow-up v-if="isExpand" />
                        <arrow-down v-else />
                    </el-icon>
                </el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:dms_rabbitmq:add']"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['evms:dms_rabbitmq:edit']"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['evms:dms_rabbitmq:remove']"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['evms:dms_rabbitmq:export']"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="DmsRabbitmqList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="rabbitmq服务器名称" align="center" prop="name" />
            <el-table-column label="服务器ip地址" align="center" prop="ip" />
            <el-table-column label="端口号" align="center" prop="port" />
            <el-table-column label="服务器用户名" align="center" prop="user" />
            <el-table-column label="服务器密码" align="center" prop="pwd" />
            <el-table-column label="虚拟host名称" align="center" prop="vhost" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:dms_rabbitmq:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:dms_rabbitmq:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改RabbitMQ配置对话框 -->
        <el-dialog :title="title" v-model="open" width="600px" append-to-body>
            <el-form ref="DmsRabbitmqRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="服务器名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入rabbitmq服务器名称" />
                </el-form-item>
                <el-form-item label="ip地址" prop="ip">
                    <el-input v-model="form.ip" placeholder="请输入服务器ip地址" />
                </el-form-item>
                <el-form-item label="端口号" prop="port">
                    <el-input v-model="form.port" placeholder="请输入端口号" />
                </el-form-item>
                <el-form-item label="用户名" prop="user">
                    <el-input v-model="form.user" placeholder="请输入服务器用户名" />
                </el-form-item>
                <el-form-item label="密码" prop="pwd">
                    <el-input v-model="form.pwd" placeholder="请输入服务器密码" />
                </el-form-item>
                <el-form-item label="虚拟host名称" prop="vhost">
                    <el-input v-model="form.vhost" placeholder="请输入虚拟host名称" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="DmsRabbitmq" lang="ts">
import {
    listDmsRabbitmq,
    getDmsRabbitmq,
    delDmsRabbitmq,
    addDmsRabbitmq,
    updateDmsRabbitmq
} from '@/api/evms/dms_rabbitmq';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';

// 添加展开折叠控制
const isExpand = ref(false);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const DmsRabbitmqList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        ip: undefined,
        port: undefined,
        user: undefined,
        pwd: undefined,
        vhost: undefined
    },
    rules: {
        name: [{ required: true, message: 'rabbitmq服务器名称不能为空', trigger: 'blur' }],
        ip: [{ required: true, message: '服务器ip地址不能为空', trigger: 'blur' }],
        port: [{ required: true, message: '端口号不能为空', trigger: 'blur' }],
        user: [{ required: true, message: '服务器用户名不能为空', trigger: 'blur' }],
        pwd: [{ required: true, message: '服务器密码不能为空', trigger: 'blur' }],
        vhost: [{ required: true, message: '虚拟host名称不能为空', trigger: 'blur' }],
        createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询RabbitMQ配置列表 */
function getList() {
    loading.value = true;

    listDmsRabbitmq(queryFormat(queryParams.value)).then((response: any) => {
        DmsRabbitmqList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        name: undefined,
        ip: undefined,
        port: undefined,
        user: undefined,
        pwd: undefined,
        vhost: undefined,
        createBy: undefined,
        updateBy: undefined,
        createTime: undefined,
        updateTime: undefined
    };
    proxy!.resetForm('DmsRabbitmqRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加RabbitMQ配置';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getDmsRabbitmq(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改RabbitMQ配置';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['DmsRabbitmqRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateDmsRabbitmq(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addDmsRabbitmq(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除RabbitMQ配置编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delDmsRabbitmq(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'evms/DmsRabbitmq/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `DmsRabbitmq_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
<style lang="scss" scoped>
.app-container {
    background: #f8f9fb;
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
