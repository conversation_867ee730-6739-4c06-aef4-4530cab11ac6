<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import { getTableInfo } from '@/minix/tables';
import { ElMessage } from 'element-plus';
import { DeleteRtcMember, RemoveRtcMember } from '@/assets/constant/isDeleteRtcMember';
import { listDoctorUser } from '@/api/evms/doctor_user';
import { queryFormat, timestampToTime } from '@/utils/ruoyi';
import { debounce } from 'lodash';
import { addRtcMember, getMemberById, kictRtcMember, removeRtcMember, updateRtcMember } from '@/api/rtc/rtc_meeting';
import defaultAvatar from '@/assets/images/default_avatar.png';
import CloseImg from '@/assets/images/close.png';
import { RtcMemberItem, RtcMemberJoinLog } from '@/model/rtc_meeting';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

dayjs.extend(duration);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// 修改用户权限
const props = defineProps(['meetingId']);
const tableInfo = getTableInfo<RtcMemberItem>(
    '/evms/rtc_meeting/member/list',
    {
        meetingId: props.meetingId
    },
    true,
    true
);
tableInfo.load();
// 添加展开折叠控制
const isExpand = ref(false);
// 删除是否禁用
const multiple = ref(true);
const ids = ref<number[]>([]);
const names = ref<string[]>([]);

const { rtc_meeting_member_role } = proxy!.useDict('rtc_meeting_member_role');
console.log(rtc_meeting_member_role);

// 新增
const handleAdd = () => {
    open.value = true;
};

// 删除
const removeBtnLoading = ref(false);
const openRemove = ref(false);
const banned = ref(false);
const removeNames = ref('');
const removeIds = ref('');
const cancelRemove = () => {
    openRemove.value = false;
    banned.value = false;
    removeNames.value = '';
    removeIds.value = '';
};
const submitRemove = async () => {
    if (removeBtnLoading.value) return;
    removeBtnLoading.value = true;
    try {
        const res = await kictRtcMember({
            banned: banned.value ? 0 : 1,
            ids: removeIds.value
        });
        if (res.code === 200) {
            ElMessage.success('移出成功');
            tableInfo.reset();
            cancelRemove();
        } else {
            ElMessage.error(res.msg);
        }
    } finally {
        removeBtnLoading.value = false;
    }
};
const handleDeleteSel = () => {
    if (ids.value.length) {
        deleteUser(ids.value.join(','), names.value.join(','));
    }
};
const handleDelete = async (row: RtcMemberItem) => {
    // deleteUser(row.id.toString(), row.nickname);
    if (row.isOnline === 'T') {
        deleteUser(row.id.toString(), row.nickname);
    } else {
        // 删除用户
        // 询问
        proxy!.$modal
            .confirm('是否确认删除参会人 "' + row.nickname + '"？')
            .then(function() {
                return removeRtcMember(row.id.toString());
            })
            .then(res => {
                if (res.code === 200) {
                    // getList();
                    proxy!.$modal.msgSuccess('删除成功');
                    // 重置列表
                    tableInfo.reset();
                }
            })
            .catch(() => {
            });
    }
};
// 下线用户
const deleteUser = async (id: string, names: string) => {
    if (!id) {
        ElMessage.error('请选择用户');
        return;
    }
    removeIds.value = id;
    removeNames.value = names;
    openRemove.value = true;
};

// 列表选择
const handleSelectionChange = (selection: RtcMemberItem[]) => {
    ids.value = selection.map(item => item.id);
    names.value = selection.map(item => item.nickname);
    multiple.value = !selection.length;
};

const open = ref(false);

// 新增用户确认
const addBtnLoading = ref(false);
const submitForm = async () => {
    console.log('新增用户确认');
    if (!checkList.value.length) {
        ElMessage.error('请选择用户');
        return;
    }
    addBtnLoading.value = true;
    const res = await addRtcMember({
        meetingId: parseInt(props.meetingId),
        memberIds: checkList.value.map(item => item.userid)
    });
    if (res.code === 200) {
        open.value = false;
        ElMessage.success('新增成功');
        tableInfo.reset();
    } else {
        ElMessage.error(res.msg);
    }
    addBtnLoading.value = false;
};
// 新增用户取消
const cancel = () => {
    open.value = false;
    checkList.value = [];
};

// 添加用户 获取用户列表
const addSearchValue = ref('');
const checkList = ref([]);
const userList = ref([]);
const getUserPageNo = ref(1);
const addUserFinished = ref(false);
const addUserLoading = ref(false);
const loadUsers = async () => {
    if (!addSearchValue.value) {
        userList.value = [];
        return;
    }
    if (addUserLoading.value) {
        return;
    }
    addUserLoading.value = true;
    try {
        const res = await listDoctorUser(
            queryFormat({
                pageNum: getUserPageNo.value,
                pageSize: 10,
                realname: addSearchValue.value
            })
        );
        if (res?.code === 200) {
            if (getUserPageNo.value === 1) {
                userList.value = [];
            }
            res.data.rows.forEach(item => (item.selected = false));
            userList.value = userList.value.concat(res.data.rows);
            getUserPageNo.value++;
            if (res.data.rows.length < 10) {
                addUserFinished.value = true;
            }
        }
    } finally {
        addUserLoading.value = false;
    }
};
// 监听输入框的change事件 加上防抖 300ms
const handleSearchUser = () => {
    getUserPageNo.value = 1;
    userList.value = [];
    loadUsers();
};
const handleSearchDebounce = debounce(handleSearchUser, 500);
// 已选用户删除
const handleDeleteUser = (index: number) => {
    const user = checkList.value[index];
    user.selected = false;
    checkList.value.splice(index, 1);
};

// 编辑
// 进出日志
const joinLogList = ref<RtcMemberJoinLog[]>([]);
const editState = reactive({
    id: 0,
    nickname: '',
    role: 0,
    totalDuration: '',
    isOnline: 'F'
});
const openEdit = ref(false);

// 修改权限
const editBtnLoading = ref(false);
// 修改权限取消
const cancelEdit = () => {
    openEdit.value = false;
    joinLogList.value = [];
    editState.id = 0;
    editState.nickname = '';
    editState.role = 0;
    editState.totalDuration = '';
    editState.isOnline = 'F';
    tableInfo.reset();
};
const getMemberDetail = async (id: number) => {
    const res = await getMemberById({
        meetingId: parseInt(props.meetingId),
        userId: id
    });
    if (res.code === 200) {
        joinLogList.value = res.data?.list || [];
    }
};
const handleUpdate = async (obj: RtcMemberItem) => {
    editState.id = obj.id;
    editState.nickname = obj.nickname;
    editState.role = obj.role;
    editState.isOnline = obj.isOnline;
    editState.totalDuration = dayjs.duration(obj.totalDuration || 0).format('HH:mm:ss');
    await getMemberDetail(obj.userId);
    openEdit.value = true;
};
const submitEditForm = async () => {
    if (editBtnLoading.value) return;
    editBtnLoading.value = true;
    // 修改保存
    const obj = {
        nickname: editState.nickname,
        role: editState.role
    };
    try {
        const res = await updateRtcMember({
            id: editState.id - 0,
            ...obj
        });
        if (res.code === 200) {
            ElMessage.success('修改成功');
            cancelEdit();
        } else {
            ElMessage.error(res.msg);
        }
    } finally {
        editBtnLoading.value = false;
    }
};
// 导出
const exportData = () => {
    proxy?.download(
        'evms/rtc_meeting/member/exportExcel',
        {
            meetingId: props.meetingId,
            ...queryFormat({ ...tableInfo.from })
        },
        `member_${new Date().getTime()}.xlsx`,
        null,
        false
    );
};
const checkedHandler = (value: boolean, user: any, index: number) => {
    if (value) {
        checkList.value.push(user);
        user.selected = true;
    } else {
        checkList.value.splice(index, 1);
        user.selected = false;
    }
};
</script>

<template>
    <div class="dms_device_info_user">
        <el-form :inline="true" :model="tableInfo.from" label-width="100" size="default">
            <!-- 始终显示的基础搜索项 -->
            <el-form-item label="参会人ID">
                <el-input
                    v-model="tableInfo.from.userId"
                    placeholder="请输入ID"
                    style="width: 240px"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-form-item label="会中姓名">
                <el-input
                    v-model="tableInfo.from.like_nickname"
                    placeholder="请输入姓名"
                    style="width: 240px"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>

            <!-- 可折叠的高级搜索项 -->
            <template v-if="isExpand">
                <el-form-item label="参会状态">
                    <el-select
                        v-model="tableInfo.from.no_query_isOnline"
                        clearable
                        placeholder="请选择参会状态"
                        style="width: 240px"
                    >
                        <el-option label="在线" value="T" />
                        <el-option label="离线" value="F" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否加入过">
                    <el-select
                        v-model="tableInfo.from.no_query_joined"
                        clearable
                        placeholder="请选择是否加入过"
                        style="width: 240px"
                    >
                        <el-option label="是" value="T" />
                        <el-option label="否" value="F" />
                    </el-select>
                </el-form-item>
                <el-form-item label="参会角色">
                    <el-select
                        v-model="tableInfo.from.role"
                        clearable
                        filterable
                        placeholder="请选择参会角色"
                        style="width: 240px"
                    >
                        <el-option
                            v-for="dict in rtc_meeting_member_role"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value - 0"
                        />
                    </el-select>
                </el-form-item>
            </template>

            <!-- 搜索按钮区域 -->
            <el-row :gutter="30" justify="start" style="margin-top: 10px">
                <el-col :span="12">
                    <el-button icon="Search" type="primary" @click="tableInfo.search">搜索</el-button>
                    <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                    <el-button type="text" @click="isExpand = !isExpand">
                        {{ isExpand ? '收起' : '展开' }}
                        <el-icon class="el-icon--right">
                            <arrow-up v-if="isExpand" />
                            <arrow-down v-else />
                        </el-icon>
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8" style="margin-top: 20px">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['evms:dms_hospital:add']"
                    color="#009dff"
                    icon="Plus"
                    plain
                    @click="handleAdd"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['evms:dms_hospital:remove']"
                    :disabled="multiple"
                    color="#ff5c00"
                    icon="Delete"
                    plain
                    @click="handleDeleteSel"
                >移出
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['evms:dms_hospital:remove']"
                    color="#ff5c00"
                    icon="Delete"
                    plain
                    @click="exportData"
                >导出
                </el-button
                >
            </el-col>
            <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
        </el-row>
        <el-table
            :data="tableInfo.list.value"
            height="450"
            row-key="id"
            style="width: 100%; margin-top: 20px"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"></el-table-column>
            <!-- <el-table-column prop="id" label="ID" width="140"></el-table-column> -->
            <el-table-column label="参会人id" prop="userId" width="120"></el-table-column>
            <el-table-column label="参会人单位" prop="unit" width="120"></el-table-column>
            <el-table-column label="参会角色" prop="role">
                <template #default="scope">
                    <dict-tag :options="rtc_meeting_member_role" :value="parseInt(scope.row.role)" />
                </template>
            </el-table-column>
            <el-table-column label="会中姓名" prop="nickname"></el-table-column>
            <el-table-column label="参会状态" prop="company">
                <template #default="scope">
                    <span
                        :style="{
                            color: scope.row.isOnline === 'T' ? '#67C23A' : '#F56C6C',
                        }"
                    >{{ scope.row.isOnline === 'T' ? '在线' : '离线' }}</span
                    >
                </template>
            </el-table-column>
            <el-table-column label="首次进入时间" prop="firstTime">
                <template #default="scope">
                    <span>{{ scope.row.firstTime ? timestampToTime(scope.row.firstTime) : '--' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="最后退出时间" prop="lastTime">
                <template #default="scope">
                    <span>{{ scope.row.lastTime ? timestampToTime(scope.row.lastTime) : '--' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="参会时长" prop="totalDuration">
                <template #default="scope">
                    <span>{{ dayjs.duration(scope.row.totalDuration || 0, 'second').format('HH:mm:ss') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="是否删除" prop="removed">
                <template #default="scope">
                    <dict-tag :options="DeleteRtcMember" :value="scope.row.removed" />
                </template>
            </el-table-column>
            <el-table-column label="是否允许再次入会" prop="banned">
                <template #default="scope">
                    <dict-tag :options="RemoveRtcMember" :value="scope.row.banned" />
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['rtc:rtc_meeting:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button
                    >
                    <el-button
                        v-if="scope.row.isOnline === 'T'"
                        v-hasPermi="['rtc:rtc_meeting:edit']"
                        :disabled="scope.row.role === 0"
                        icon="Delete"
                        link
                        type="danger"
                        @click="handleDelete(scope.row)"
                    >移出
                    </el-button
                    >
                    <el-button
                        v-else
                        v-hasPermi="['rtc:rtc_meeting:edit']"
                        :disabled="scope.row.role === 0"
                        icon="Delete"
                        link
                        type="danger"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row align="bottom" justify="space-between">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                v-model:limit="tableInfo.pageParam.pageSize"
                v-model:page="tableInfo.pageParam.pageNum"
                :total="tableInfo.total.value"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
    <!--    新增关联用户-->
    <el-dialog v-model="open" append-to-body modal-class="dms_device_info_user_dialog" width="1156px">
        <template #header>
            <div class="my-header">
                <div class="dialog_header">新增参会人</div>
            </div>
        </template>
        <div class="add_dialog_content">
            <div class="add_left">
                <el-input
                    v-model="addSearchValue"
                    placeholder="请输入参会人姓名添加"
                    @input="handleSearchDebounce"
                ></el-input>
                <!--                <el-checkbox-group v-model="checkList">-->
                <ul
                    v-infinite-scroll="loadUsers"
                    :infinite-scroll-immediate="false"
                    class="infinite-list"
                    style="overflow: auto"
                >
                    <li v-for="(user, index) in userList" :key="user.id">
                        <el-checkbox
                            v-model="user.selected"
                            @change="value => checkedHandler(value, user, index)"
                        >
                            <template #default>
                                <div class="infinite-list-item">
                                    <img :src="user.avatar || defaultAvatar" class="item_ava" />
                                    <div class="item_info">
                                        <div class="item_name">{{ user.username }}</div>
                                        <div class="item_company">{{ user.company }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-checkbox>
                    </li>
                </ul>
                <!--                </el-checkbox-group>-->
            </div>
            <div class="add_line"></div>
            <div class="add_right">
                <div class="add_right_title">已选择</div>
                <div v-if="checkList.length" class="add_right_content">
                    <div v-for="(user, index) in checkList" :key="user.id" class="infinite-list-item">
                        <img :src="user.avatar || defaultAvatar" class="item_ava" />
                        <div class="item_info">
                            <div class="item_name">{{ user.username }}</div>
                            <div class="item_company">{{ user.company }}</div>
                        </div>
                        <img :src="CloseImg" alt class="close" @click="handleDeleteUser(index)" />
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button :loading="addBtnLoading" type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
    <!--    修改用户权限-->
    <el-dialog v-model="openEdit" append-to-body modal-class="dms_device_info_user_dialog" width="1156px">
        <template #header>
            <div class="my-header">
                <div class="dialog_header">修改参会人</div>
            </div>
        </template>
        <el-form :label-position="'left'">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="ID">
                        <el-input v-model="editState.id" disabled></el-input>
                    </el-form-item>
                </el-col>
                <!--            <el-form-item label="姓名" style="width: 100%">-->
                <!--                <el-input disabled v-model="tableInfo.from.userId" placeholder="请输入ID"-->
                <!--                          @keyup.enter="tableInfo.search"></el-input>-->
                <!--            </el-form-item>-->
                <el-col :span="6">
                    <el-button
                        v-hasPermi="['evms:dms_hospital:remove']"
                        :disabled="multiple"
                        color="#ff5c00"
                        icon="Delete"
                        plain
                        @click="handleDelete(editState as RtcMemberItem)"
                    >移出
                    </el-button
                    >
                </el-col>
            </el-row>
        </el-form>
        <div class="edit_user_content">
            <el-form :model="editState">
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="会中姓名">
                            <el-input v-model="editState.nickname" placeholder="请输入姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="会议时间" prop="time" style="font-weight: 700">
                            <el-date-picker
                                v-for="(item, index) in joinLogList"
                                :key="index"
                                :model-value="[item.joinTime, item.leaveTime]"
                                disabled
                                range-separator="-"
                                readonly
                                style="font-weight: 400; margin-bottom: 10px"
                                type="datetimerange"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="参会状态">
                            <el-input
                                :value="editState.isOnline === 'T' ? '在线' : '离线'"
                                disabled
                                @keyup.enter="tableInfo.search"
                            >
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="参会角色">
                            <el-select
                                v-model="editState.role"
                                clearable
                                filterable
                                placeholder="请选择参会角色"
                                style="width: 240px"
                            >
                                <!-- <el-option
                                    v-for="dict in rtc_meeting_member_role"
                                    :key="dict.value"
                                    :disabled="
                                        editState.role - 0 === 0 ? dict.value !== '0' : dict.value === '0'
                                    "
                                    :label="dict.label"
                                    :value="parseInt(dict.value)"
                                /> -->
                                <el-option
                                    v-for="dict in rtc_meeting_member_role"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="parseInt(dict.value)"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="参会时长">
                            <el-input v-model="editState.totalDuration" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row :gutter="20" justify="start" style="margin-top: 10px">-->
                <!--                    <el-col :span="10">-->
                <!--                        <el-form-item label="参会状态">-->
                <!--                            <el-input disabled v-model="tableInfo.from.like_nickname"-->
                <!--                            ></el-input>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
            </el-form>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelEdit">取 消</el-button>
                <el-button :loading="editBtnLoading" type="primary" @click="submitEditForm">确 定</el-button>
            </div>
        </template>
    </el-dialog>
    <!--    移出-->
    <el-dialog
        v-model="openRemove"
        :show-close="false"
        append-to-body
        modal-class="dms_device_info_user_dialog"
        style="border-radius: 10px"
        width="300px"
    >
        <div class="remove_container">
            <div class="remove_title">移出参会人 {{ removeNames }}</div>
            <el-checkbox v-model="banned">允许再次加入会议</el-checkbox>
        </div>
        <template #footer>
            <div class="remove_dialog-footer">
                <el-button @click="cancelRemove">取 消</el-button>
                <el-button :loading="removeBtnLoading" type="primary" @click="submitRemove">确 定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
.add_dialog_content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    :deep(.el-checkbox) {
        height: 40px !important;
    }

    & > div {
        width: 49%;
    }

    .infinite-list {
        height: 500px;
        margin-top: 15px;

        li {
            padding: 10px 0;
            margin-bottom: 5px;
        }
    }

    .infinite-list-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .item_ava {
            margin-right: 12px;
            width: 40px;
            height: 40px;
            background: #009dff;
            border-radius: 50%;
        }

        .item_name {
            font-size: 16px;
        }

        .item_company {
            margin-top: 8px;
            font-size: 14px;
            color: #666;
        }

        .close {
            margin-left: auto;
            width: 10px;
            height: 10px;
        }
    }

    .add_right .add_right_content {
        margin-top: 15px;

        .infinite-list-item {
            padding: 10px 0;
            margin-bottom: 5px;

            .item_name,
            .item_company {
                line-height: 1;
            }
        }
    }

    .add_right_title {
        line-height: 30px;
    }

    .add_line {
        width: 1px;
        height: 500px;
        background-color: #f5f5f5;
    }
}

.dms_device_info_user_dialog,
.dms_device_info_user_edit {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}

.dms_device_info_user_edit {
    .el-dialog {
        border-radius: 20px;
        border: 1px solid rgba(204, 204, 204, 0.8);
        background: #f8f9fb;
        box-shadow: 10px 10px 20px 0 rgba(0, 0, 0, 0.15);
        overflow: clip;
    }

    .dialog_edit_header {
        padding: 6.5px 0;
        display: flex;
        justify-content: center;
        color: #333;
        text-align: center;
        font-size: 14px;
        background-color: #fff;
    }

    .el-dialog__headerbtn {
        top: 4.5px;
    }

    .el-form-item__content {
        justify-content: end;
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-form-item {
        padding: 20px 30px 12px;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: none;
        }
    }

    .el-form-item--default {
        margin: 0;
    }

    .el-form-item__label {
        color: #666;
        font-size: 14px;
        line-height: 150%; /* 21px */
        height: 21px;
    }

    .el-switch {
        height: 18px;
    }

    .dialog-footer {
        padding-top: 30px;
        display: flex;
        justify-content: center;

        .el-button {
            padding: 5px 36px;
            border-radius: 15px;
        }
    }
}

.remove_container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.remove_dialog-footer {
    display: flex;
    justify-content: center;

    & button {
        margin: 0 20px;
    }
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
