import { reactive, ref, Ref } from 'vue';
import request, { Http } from '@/utils/request';
import { NList } from '@/model/api';
import { addDateRange, queryFormat } from '@/utils/ruoyi';
import { isArray } from 'lodash';

export interface Response<T> {
    from: Record<string, string | number>;
    TableLoading: Ref<boolean>;
    list: Ref<T[]>;
    total: Ref<number>;
    pageParam: {
        pageNum: number;
        pageSize: number;
    };
    changePage: (pageNum: number, pageSize: number) => void;
    search: () => void;
    reset: () => void;
    pagination: (data: { page: number; limit: number }) => void;
    load: (p?: object) => void;
    sort: (data: { column: any; prop: string; order: string }) => void;
}

export function getTableInfo<T extends object>(
    url: string,
    param?: object,
    openPagination = true,
    addQ: boolean = true
): Response<T> {
    const pageParam = reactive({
        pageNum: 1,
        pageSize: 10
    });
    const loading = ref<boolean>(false);
    const list: Ref<T[]> = ref([]);
    const total = ref<number>(0);
    const from: Record<string, any> = reactive({});
    let loadParam: object | undefined = undefined;
    const load = async (p?: object) => {
        console.log(p, '===p');
        if (p) {
            loadParam = p;
            console.log(loadParam, '===p1');
        }
        if (loading.value) return;
        loading.value = true;
        try {
            let data = loadParam ? { ...loadParam } : {};
            console.log(data, '--data');
            if (from.hasOwnProperty('isDevicePermission')) {
                // 特殊处理参数
                const obj = { ...from };
                const value = from.isDevicePermission;
                delete obj.isDevicePermission;
                data = { ...data, ...queryFormat(obj), ...pageParam, ...param, isDevicePermission: value };
            } else if (from.hasOwnProperty('startTimeUtc')) {
                const obj = { ...from };
                const value = Array.isArray(obj.startTimeUtc)
                    ? obj.startTimeUtc
                    : [obj.startTimeUtc, obj.startTimeUtc];
                delete obj.startTimeUtc;
                data = { ...data, ...addDateRange(obj, value, 'startTimeUtc'), ...pageParam, ...param };
            } else if (from.hasOwnProperty('meeting_startTimeUtc')) {
                const obj = { ...from };
                const value = Array.isArray(obj.meeting_startTimeUtc)
                    ? obj.meeting_startTimeUtc
                    : [obj.meeting_startTimeUtc, obj.meeting_startTimeUtc];
                delete obj.meeting_startTimeUtc;
                data = { ...data, ...addDateRange(obj, value, 'Jmeeting_startTimeUtc'), ...pageParam, ...param };
            } else {
                data = addQ
                    ? { ...data, ...queryFormat(from), ...pageParam, ...param }
                    : { ...data, ...from, ...pageParam, ...param };
            }
            console.log(data, from, '===data');
            const res = await Http.get<NList<T>>(url, data);
            if (res.code === 200) {
                list.value = isArray(res.data) ? (res.data as Array<T>) : res.data!.rows;
                total.value = isArray(res.data) ? res.data.length : res.data!.total;
            }
            loading.value = false;
        } catch (error) {
            loading.value = false;
        }
    };
    const pagination = (data: { page: number; limit: number }) => {
        pageParam.pageNum = data.page;
        pageParam.pageSize = data.limit;
        load();
    };
    const changePage = (pageNum: number, pageSize: number) => {
        pageParam.pageNum = pageNum > 0 ? pageNum : 1;
        pageSize ? (pageParam.pageSize = pageSize) : null;
        load();
    };
    const search = () => {
        pageParam.pageNum = 1;
        load();
    };
    const reset = () => {
        const keys = Object.keys(from);
        for (const key of keys) {
            from[key] = '';
        }
        pageParam.pageNum = 1;
        load();
    };
    const sort = (data: { column: any; prop: string; order: any }) => {
        if (data.order === 'ascending') {
            from.sort = data.prop + ' asc';
        } else if (data.order === 'descending') {
            from.sort = data.prop + ' desc';
        } else {
        }
        load();
    };
    return {
        from,
        list,
        total,
        pageParam,
        changePage,
        TableLoading: loading,
        search,
        reset,
        pagination,
        load,
        sort
    };
}
