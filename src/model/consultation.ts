export interface ConsultationOrder {
    id?: string | number;
    theme?: string;
    type?: string;
    description?: string;
    organizer?: Organizer;
    organizerId?: number;
    device?: Device;
    deviceId?: string | number;
    startTime?: number;
    expert?: Expert;
    expertId?: number;
    linkUrl?: string;
    dateStr?: string;
    status?: number;
    memberList?: MemberList[];
    participants?: number[];
    groupIds?: number[];
    teachingGroups?: ConsultationHospital[];
    userIds?: number[];
    time?: any[]; // 仅前端
    guideType?: string; // 指导类型
    isCancelConflict?: string; // 是否有冲突 前端使用
}

export interface Organizer {
    realname: string;
    avatar: string;
    title: string;
    company: string;
    userid: number;
}

export interface Device {
    id: string;
    name: string;
    fullName?: string;
    type: number;
    description: string;
    permission: Permission;
}

export interface Permission {
}

export interface Expert {
    realname: string;
    avatar: string;
    title: string;
    company: string;
    userid: number;
}

export interface User {
    realname: string;
    avatar: string;
    title: string;
    company: string;
    userid: number;
}

export interface MemberList {
    id: string;
    user: User2;
    expert: boolean;
}

export interface User2 {
    realname: string;
    avatar: string;
    title: string;
    company: string;
    userid: number;
}

export interface ConsultationExpert {
    id: string;
    username: string;
    realname: string;
    avatar: string;
    title: string;
    company: string;
    department: string;
    identity: number;
    statusForPatient: number;
    gender: string;
    role: number;
    auth: string;
    type: number;
    mobile: string;
    status: number;
    multiDepartment: string;
    userid: number;
    nickname: string;
    wxOpenId: string;
    videoExportPermission: number;
    rtcUserId: string;
    wbUserId: string;
    permission: Permission;
}

export interface Permission {
    elab: boolean;
    ebs: boolean;
    outpatient: boolean;
    videoExportPermission: boolean;
}

export interface ConsultationHospital {
    id?: string;
    name?: string;
    offline?: boolean;
    leaders?: Leader[];
    memberCount?: number;
}

export interface ConsultationHospitalParams {
    id?: string;
    name: string;
    leaders?: number[];
}

export interface Leader {
    realname: string;
    avatar: string;
    title: string;
    company: string;
    userid: number;
}

export interface ConsultationMember {
    id: string;
    user: User;
    leader: boolean;
}

export interface User {
    realname: string;
    avatar: string;
    title: string;
    company: string;
    userid: number;
}
