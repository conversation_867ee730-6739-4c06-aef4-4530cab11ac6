import request from '@/utils/request';

// 查询导管室合作信息列表
export function listCooperate(query: any) {
    return request({
        url: '/cl/cooperate/list',
        method: 'get',
        params: query,
    });
}

// 查询导管室合作信息详细
export function getCooperate(id: any) {
    return request({
        url: '/cl/cooperate/' + id,
        method: 'get',
    });
}

// 新增导管室合作信息
export function addCooperate(data: any) {
    return request({
        url: '/cl/cooperate',
        method: 'post',
        data: data,
    });
}

// 修改导管室合作信息
export function updateCooperate(data: any) {
    return request({
        url: '/cl/cooperate',
        method: 'put',
        data: data,
    });
}

// 删除导管室合作信息
export function delCooperate(id: any) {
    return request({
        url: '/cl/cooperate/' + id,
        method: 'delete',
    });
}

/*
 根据医院ID查询医院内容
*/
export function getCooperateByDeviceId(deviceId: any) {
    const data = {
        deviceId: deviceId
    };
    return request({
        url: '/cl/cooperate/getCooperateByDeviceId',
        method: 'get',
        params: data,
    });
}
