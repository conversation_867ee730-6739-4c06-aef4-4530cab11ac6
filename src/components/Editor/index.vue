<template>
    <div>
        <!-- 此处注意写法v-model:content -->
        <QuillEditor
            ref="myQuillEditor"
            theme="snow"
            v-model:content="content"
            :options="data.editorOption"
            contentType="html"
            @update:content="setValue()"
        />
        <!-- 使用自定义图片上传 -->
        <input type="file" hidden accept=".jpg,.png" ref="fileBtn" @change="handleUpload" />
    </div>
</template>

<script setup>
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { reactive, onMounted, ref, toRaw, watch } from 'vue';

// 接受父组件传的context
const props = defineProps(['value', 'content']);
const emit = defineEmits(['updateValue']);
const content = ref('');
const myQuillEditor = ref();
// 通过watch监听回显，笔者这边使用v-model:content 不能正常回显
watch(
    () => props.content,
    content => {
        toRaw(myQuillEditor.value).setHTML(content);
    },
    { deep: true }
);

const fileBtn = ref();
const data = reactive({
    content: '',
    editorOption: {
        modules: {
            toolbar: [
                ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
                [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
                [{ header: ['1', '2', '3', '4', '5', '6', false] }], // 标题
                [{ font: [] }], // 字体种类
                ['clean'], // 清除文本格式
                [{ align: [] }], // 对齐方式
                [{ direction: 'rtl' }], // 文本方向
                [{ script: 'sub' }, { script: 'super' }], // 上标/下标
                [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
                [{ indent: '-1' }, { indent: '+1' }], // 缩进
                [{ color: [] }, { background: [] }] // 字体颜色、字体背景颜色
            ],
        },
        placeholder: '请输入内容...'
    },
});
const imgHandler = state => {
    if (state) {
        fileBtn.value.click();
    }
};
// 抛出更改内容，此处避免出错直接使用文档提供的getHTML方法
const setValue = () => {
    const text = toRaw(myQuillEditor.value).getHTML();
    // emit('updateValue', text)
    // 这边做出更改bug 鼠标打字会出现字母在上面的效果
    sessionStorage.setItem('text', text);
};
const handleUpload = e => {
    const files = Array.prototype.slice.call(e.target.files);
    if (!files) {
        return;
    }
    const formdata = new FormData();
    formdata.append('file', files[0]);
    backsite
        .uploadFile(formdata) // 此处使用服务端提供上传接口
        .then(res => {
            if (res.data.url) {
                const quill = toRaw(myQuillEditor.value).getQuill();
                const length = quill.getSelection().index;
                quill.insertEmbed(length, 'image', res.data.url);
                quill.setSelection(length + 1);
            }
        });
};

// 初始化编辑器
onMounted(() => {
    toRaw(myQuillEditor.value).setHTML(props.content);
});
</script>

<style scoped lang="scss">
// 调整样式
:deep(.ql-editor) {
    min-height: 180px;
}
:deep(.ql-formats) {
    height: 21px;
    line-height: 21px;
}
</style>
