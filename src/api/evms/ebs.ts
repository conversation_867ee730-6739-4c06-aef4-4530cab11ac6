import request from '@/utils/request';

// 查询EBS病例列表
export function listEbs(query: any) {
    return request({
        url: '/evms/ebs/list',
        method: 'get',
        params: query,
    });
}

// 查询EBS病例详细1
export function getEbs(id: any) {
    return request({
        url: '/evms/ebs/' + id,
        method: 'get',
    });
}

// 新增EBS病例
export function addEbs(data: any) {
    return request({
        url: '/evms/ebs',
        method: 'post',
        data: data,
    });
}

// 修改EBS病例
export function updateEbs(data: any) {
    return request({
        url: '/evms/ebs',
        method: 'put',
        data: data,
    });
}

// 删除EBS病例
export function delEbs(id: any) {
    return request({
        url: '/evms/ebs/' + id,
        method: 'delete',
    });
}

// 物理删除EBS病例
export function physicallyRemove(id: any) {
    return request({
        url: '/evms/ebs/physicallyRemove/' + id,
        method: 'delete',
    });
}
