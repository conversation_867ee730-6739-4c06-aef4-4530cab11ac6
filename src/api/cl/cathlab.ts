import request from '@/utils/request';

/*
 查询导管室列表
*/
export function listCathlab(query: any) {
    return request({
        url: '/cl/cath_lab/list',
        method: 'get',
        params: query
    });
}

/*
 查询导管室列表(不分页)
*/
export function allCathlab(query: any) {
    return request({
        url: '/cl/cath_lab/all',
        method: 'get',
        params: query
    });
}

/*
 查询导管室详细
*/
export function getCathlab(id: any) {
    return request({
        url: '/cl/cath_lab/' + id,
        method: 'get'
    });
}

/*
 新增导管室
*/
export function addCathlab(data: any) {
    return request({
        url: '/cl/cath_lab',
        method: 'post',
        data: data
    });
}

/*
 修改导管室
*/
export function updateCathlab(data: any) {
    return request({
        url: '/cl/cath_lab',
        method: 'put',
        data: data
    });
}

/*
 删除导管室
*/
export function delCathlab(id: any) {
    return request({
        url: '/cl/cath_lab/' + id,
        method: 'delete'
    });
}

/*
 根据医院ID查询医院内容
*/
export function getHospitalById(hospitalId: any) {
    const data = {
        hospitalId: hospitalId
    };
    return request({
        url: '/cl/cath_lab/getHospitalById',
        method: 'get',
        params: data
    });
}

/*
 根据医院名称查询医院列表
*/
export function getHospitalByName(hospitalName: any) {
    const data = {
        hospitalName: hospitalName
    };
    return request({
        url: '/cl/cath_lab/getHospitalListByName',
        method: 'get',
        params: data
    });
}
