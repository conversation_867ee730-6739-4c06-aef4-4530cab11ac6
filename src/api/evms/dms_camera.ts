import request, { Http } from '@/utils/request';
import { CameraInfo } from '@/model/dms_device';

// 查询DMS摄像头列表
export async function listDmsCamera(query: string) {
    return await Http.get<CameraInfo[]>('/evms/dms_camera/list?' + query);
}

// 查询DMS摄像头详细
export async function getDmsCamera(id: number) {
    return await Http.get<CameraInfo>('/evms/dms_camera/' + id);
}

// 查询设备
export async function getDeviceListByKeywords(query: any) {
    return await Http.get<CameraInfo>('/evms/dms_device/getListByKeywords', query);
}

// 新增DMS摄像头
export const addDmsCamera = async (data: CameraInfo) => {
    return await Http.post<CameraInfo>('/evms/dms_camera', data);
};

// 修改DMS摄像头
export async function updateDmsCamera(data: CameraInfo) {
    return await Http.put<CameraInfo>('/evms/dms_camera', data);
}

// 删除DMS摄像头
export async function delDmsCamera(id: number) {
    return await Http.delete('/evms/dms_camera/' + id);
}

export function listCamera(query: any) {
    return request({
        url: '/evms/dms_camera/list',
        method: 'get',
        params: query,
    });
}
