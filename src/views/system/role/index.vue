<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
            <el-form-item label="角色名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    clearable
                    placeholder="请输入角色名称"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="权限字符" prop="code">
                <el-input
                    v-model="queryParams.code"
                    clearable
                    placeholder="请输入权限字符"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" clearable placeholder="角色状态" style="width: 240px">
                    <el-option
                        v-for="dict in sys_normal_disable"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
                <el-date-picker
                    v-model="dateRange"
                    end-placeholder="结束日期"
                    range-separator="-"
                    start-placeholder="开始日期"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:role:add']" icon="Plus" plain type="primary" @click="handleAdd"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:role:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:role:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:role:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格数据 -->
        <el-table v-loading="loading" :data="roleList" @selectionChange="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column label="角色编号" prop="id" width="120" />
            <el-table-column :show-overflow-tooltip="true" label="角色名称" prop="name" width="150" />
            <el-table-column :show-overflow-tooltip="true" label="权限字符" prop="code" width="150" />
            <el-table-column label="显示顺序" prop="sort" width="100" />
            <el-table-column align="center" label="状态" width="100">
                <template #default="scope">
                    <el-switch
                        v-model="scope.row.status"
                        active-value="0"
                        inactive-value="1"
                        @change="handleStatusChange(scope.row)"
                    ></el-switch>
                </template>
            </el-table-column>
            <el-table-column align="center" label="创建时间" prop="createTime">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-tooltip v-if="scope.row.id !== '1'" content="修改" placement="top">
                        <el-button
                            v-hasPermi="['sys:role:edit']"
                            icon="Edit"
                            link
                            type="primary"
                            @click="handleUpdate(scope.row)"
                        ></el-button>
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.id !== '1'" content="删除" placement="top">
                        <el-button
                            v-hasPermi="['sys:role:remove']"
                            icon="Delete"
                            link
                            type="primary"
                            @click="handleDelete(scope.row)"
                        ></el-button>
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.id !== '1'" content="数据权限" placement="top">
                        <el-button
                            v-hasPermi="['sys:role:edit']"
                            icon="CircleCheck"
                            link
                            type="primary"
                            @click="handleDataScope(scope.row)"
                        ></el-button>
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.id !== '1'" content="分配用户" placement="top">
                        <el-button
                            v-hasPermi="['sys:role:edit']"
                            icon="User"
                            link
                            type="primary"
                            @click="handleAuthUser(scope.row)"
                        ></el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改角色配置对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="roleRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="角色名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入角色名称" />
                </el-form-item>
                <el-form-item prop="code">
                    <template #label>
                        <span>
                            <el-tooltip
                                content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"
                                placement="top"
                            >
                                <el-icon><question-filled /></el-icon>
                            </el-tooltip>
                            权限字符
                        </span>
                    </template>
                    <el-input v-model="form.code" placeholder="请输入权限字符" />
                </el-form-item>
                <el-form-item label="角色顺序" prop="sort">
                    <el-input-number v-model="form.sort" :min="0" controls-position="right" />
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
                            dict.label
                            }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="菜单权限">
                    <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')"
                    >展开/折叠
                    </el-checkbox
                    >
                    <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')"
                    >全选/全不选
                    </el-checkbox
                    >
                    <el-checkbox
                        v-model="form.menuCheckStrictly"
                        @change="handleCheckedTreeConnect($event, 'menu')"
                    >父子联动
                    </el-checkbox
                    >
                    <el-tree
                        ref="menuRef"
                        :check-strictly="!form.menuCheckStrictly"
                        :data="menuOptions"
                        :props="{ label: 'label', children: 'children' }"
                        class="tree-border"
                        empty-text="加载中，请稍候"
                        node-key="id"
                        show-checkbox
                    ></el-tree>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="form.remark" placeholder="请输入内容" type="textarea"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 分配角色数据权限对话框 -->
        <el-dialog v-model="openDataScope" :title="title" append-to-body width="500px">
            <el-form :model="form" label-width="80px">
                <el-form-item label="角色名称">
                    <el-input v-model="form.name" :disabled="true" />
                </el-form-item>
                <el-form-item label="权限字符">
                    <el-input v-model="form.code" :disabled="true" />
                </el-form-item>
                <el-form-item label="权限范围">
                    <el-select v-model="form.dataScope" @change="dataScopeSelectChange">
                        <el-option
                            v-for="item in dataScopeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="form.dataScope == 2" label="数据权限">
                    <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')"
                    >展开/折叠
                    </el-checkbox
                    >
                    <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')"
                    >全选/全不选
                    </el-checkbox
                    >
                    <el-checkbox
                        v-model="form.deptCheckStrictly"
                        @change="handleCheckedTreeConnect($event, 'dept')"
                    >父子联动
                    </el-checkbox
                    >
                    <el-tree
                        ref="deptRef"
                        :check-strictly="!form.deptCheckStrictly"
                        :data="deptOptions"
                        :props="{ label: 'label', children: 'children' }"
                        class="tree-border"
                        default-expand-all
                        empty-text="加载中，请稍候"
                        node-key="id"
                        show-checkbox
                    ></el-tree>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitDataScope">确 定</el-button>
                    <el-button @click="cancelDataScope">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="Role" setup>
/* eslint-disable camelcase */
import {
    addRole,
    changeRoleStatus,
    dataScope,
    delRole,
    getRole,
    listRole,
    updateRole,
    deptTreeSelect,
    roleMenuTree
} from '@/api/system/role';
import { treeselect as menuTreeAll } from '@/api/system/menu';
import { deptTreeSelect as deptTree } from '@/api/system/user';
import { parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs, nextTick } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_normal_disable } = proxy!.useDict('sys_normal_disable');

const roleList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const dateRange = ref<any[]>([]);
const menuOptions = ref<any[]>([]);
const menuExpand = ref(false);
const menuNodeAll = ref(false);
const deptExpand = ref(true);
const deptNodeAll = ref(false);
const deptOptions = ref<any[]>([]);
const openDataScope = ref(false);
const menuRef = ref<any>(null);
const deptRef = ref<any>(null);

/** 数据范围选项*/
const dataScopeOptions = ref([
    { value: '1', label: '全部数据权限' },
    { value: '2', label: '自定数据权限' },
    { value: '3', label: '本部门数据权限' },
    { value: '4', label: '本部门及以下数据权限' },
    { value: '5', label: '仅本人数据权限' }
]);

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 3,
        name: undefined,
        code: undefined,
        status: undefined
    },
    rules: {
        name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
        code: [{ required: true, message: '权限字符不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '角色顺序不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询角色列表 */
function getList() {
    loading.value = true;
    listRole(proxy!.addDateRange(queryParams.value, dateRange.value)).then((response: any) => {
        roleList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 全部部门树 */
function getAllDeptTree() {
    deptTree().then((response: any) => {
        deptOptions.value = response.data;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = [];
    proxy!.resetForm('queryRef');
    handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const roleIds = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项?')
        .then(function() {
            return delRole(roleIds);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch((e: any) => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'system/role/export',
        {
            ...queryParams.value
        },
        `role_${new Date().getTime()}.xlsx`
    );
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 角色状态修改 */
function handleStatusChange(row: any) {
    let text = row.status === '0' ? '启用' : '停用';
    proxy!.$modal
        .confirm('确认要"' + text + '""' + row.name + '"角色吗?')
        .then(function() {
            return changeRoleStatus(row.id, row.status);
        })
        .then(() => {
            proxy!.$modal.msgSuccess(text + '成功');
        })
        .catch(function() {
            row.status = row.status === '0' ? '1' : '0';
        });
}

/** 更多操作 */
function handleCommand(command: any, row: any) {
    switch (command) {
        case 'handleDataScope':
            handleDataScope(row);
            break;
        case 'handleAuthUser':
            handleAuthUser(row);
            break;
        default:
            break;
    }
}

/** 分配用户 */
function handleAuthUser(row: any) {
    router.push('/system/role-auth/user/' + row.id);
}

/** 查询菜单树结构 */
function getAllMenuTree() {
    menuTreeAll().then(response => {
        menuOptions.value = response.data;
    });
}

/** 所有部门节点数据 */
function getDeptAllCheckedKeys() {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value.getCheckedKeys();
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value.getHalfCheckedKeys();
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
}

/** 重置新增的表单以及其他数据  */
function reset() {
    if (menuRef.value) {
        menuRef.value.setCheckedKeys([]);
    }
    menuExpand.value = false;
    menuNodeAll.value = false;
    deptExpand.value = true;
    deptNodeAll.value = false;
    form.value = {
        id: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        status: '0',
        menuIds: [],
        deptIds: [],
        menuCheckStrictly: true,
        deptCheckStrictly: true,
        remark: undefined
    };
    proxy!.resetForm('roleRef');
}

/** 添加角色 */
function handleAdd() {
    reset();
    getAllMenuTree();
    open.value = true;
    title.value = '添加角色';
}

/** 修改角色 */
function handleUpdate(row: any) {
    reset();
    const id = row.id || ids.value;
    const roleMenu = getRoleMenuTreeselect(id);
    getRole(id).then(response => {
        form.value = response.data;
        form.value.sort = Number(form.value.sort);
        form.value.menuCheckStrictly = true;
        form.value.deptCheckStrictly = true;
        open.value = true;
        nextTick(() => {
            roleMenu.then((res: any) => {
                let selectedMenus: any[] = res.data;
                let checkedKeys = selectedMenus.map(m => m.id);
                checkedKeys.forEach((v: any) => {
                    nextTick(() => {
                        menuRef.value.setChecked(v, true, false);
                    });
                });
            });
        });
        title.value = '修改角色';
    });
}

/** 根据角色ID查询菜单树结构 */
function getRoleMenuTreeselect(roleId: any) {
    return roleMenuTree(roleId).then((response: any) => {
        return response;
    });
}

/** 根据角色ID查询部门树结构 */
function getDeptTree(roleId: any) {
    return deptTreeSelect(roleId).then((response: any) => {
        // deptOptions.value = response.data;
        return response;
    });
}

/** 树权限（展开/折叠）*/
function handleCheckedTreeExpand(value: any, type: any) {
    if (type === 'menu') {
        let treeList = menuOptions.value;
        for (let i = 0; i < treeList.length; i++) {
            menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
        }
    } else if (type === 'dept') {
        let treeList = deptOptions.value;
        for (let i = 0; i < treeList.length; i++) {
            deptRef.value.store.nodesMap[treeList[i].id].expanded = value;
        }
    }
}

/** 树权限（全选/全不选） */
function handleCheckedTreeNodeAll(value: any, type: string) {
    if (type === 'menu') {
        menuRef.value.setCheckedNodes(value ? menuOptions.value : []);
    } else if (type === 'dept') {
        deptRef.value.setCheckedNodes(value ? deptOptions.value : []);
    }
}

/** 树权限（父子联动） */
function handleCheckedTreeConnect(value: any, type: string) {
    if (type === 'menu') {
        form.value.menuCheckStrictly = value ? true : false;
    } else if (type === 'dept') {
        form.value.deptCheckStrictly = value ? true : false;
    }
}

/** 所有菜单节点数据 */
function getMenuAllCheckedKeys() {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value.getCheckedKeys();
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value.getHalfCheckedKeys();
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['roleRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id !== undefined) {
                form.value.menuIds = getMenuAllCheckedKeys();
                updateRole(form.value).then(response => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                form.value.menuIds = getMenuAllCheckedKeys();
                addRole(form.value).then(response => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 选择角色权限范围触发 */
function dataScopeSelectChange(value: any) {
    if (value !== '2') {
        deptRef.value.setCheckedKeys([]);
    }
}

/** 分配数据权限操作 */
function handleDataScope(row: any) {
    reset();
    const deptTreeSelect = getDeptTree(row.id);
    getRole(row.id).then(response => {
        form.value = response.data;
        form.value.menuCheckStrictly = true;
        form.value.deptCheckStrictly = true;
        openDataScope.value = true;
        nextTick(() => {
            deptTreeSelect.then(res => {
                nextTick(() => {
                    if (deptRef.value) {
                        let depts: any[] = res.data;
                        deptRef.value.setCheckedKeys(depts.map(r => r.id));
                    }
                });
            });
        });
        title.value = '分配数据权限';
    });
}

/** 提交按钮（数据权限） */
function submitDataScope() {
    if (form.value.id) {
        form.value.deptIds = getDeptAllCheckedKeys();
        dataScope(form.value).then(response => {
            proxy!.$modal.msgSuccess('修改成功');
            openDataScope.value = false;
            getList();
        });
    }
}

/** 取消按钮（数据权限）*/
function cancelDataScope() {
    openDataScope.value = false;
    reset();
}

getList();
getAllDeptTree();
getAllMenuTree();
</script>
