<template>
    <div class="app-container">
        <el-card shadow="never" style="padding-right: 50px; border-radius: 12px">
            <el-form :model="tableInfo.from" :inline="true" label-width="100">
                <el-form-item label="ID" prop="id">
                    <el-input
                        v-model="tableInfo.from.id"
                        placeholder="请输入ID"
                        clearable
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="用户名" prop="userId">
                    <el-input
                        v-model="tableInfo.from.like_username"
                        placeholder="请输入用户名"
                        clearable
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="姓名" prop="deviceId">
                    <el-input
                        v-model="tableInfo.from.like_realname"
                        placeholder="请输入姓名"
                        clearable
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <template v-if="isExpand">
                    <el-form-item label="手机号" prop="deviceId">
                        <el-input
                            v-model="tableInfo.from.like_mobile"
                            placeholder="请输入手机号"
                            clearable
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="单位" prop="deviceId">
                        <el-input
                            v-model="tableInfo.from.like_company"
                            placeholder="请输入单位"
                            clearable
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="科室" prop="deviceId">
                        <el-input
                            v-model="tableInfo.from.like_department"
                            placeholder="请输入科室"
                            clearable
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="有设备用户" prop="deviceId">
                        <el-select v-model="tableInfo.from.isDevicePermission" style="width: 240px">
                            <el-option label="是" value="T" />
                            <el-option label="否" value="F" />
                        </el-select>
                    </el-form-item>
                </template>

                <!-- 搜索按钮区域 -->
                <el-row justify="start" :gutter="20" style="width: 100%; margin-top: 10px">
                    <el-col :span="12" style="padding-left: 50px">
                        <el-button type="primary" icon="Search" @click="tableInfo.search">搜索</el-button>
                        <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                        <el-button type="text" @click="isExpand = !isExpand">
                            {{ isExpand ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <arrow-up v-if="isExpand" />
                                <arrow-down v-else />
                            </el-icon>
                        </el-button>
                    </el-col>
                </el-row>
                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
        </el-card>
        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <!--            <el-row :gutter="10" class="mb8">-->
            <!--                <el-col :span="1.5">-->
            <!--                    <el-button-->
            <!--                        type="primary"-->
            <!--                        plain-->
            <!--                        icon="Plus"-->
            <!--                        @click="handleAdd"-->
            <!--                        v-hasPermi="['evms:dms_permission:add']"-->
            <!--                    >新增-->
            <!--                    </el-button>-->
            <!--                </el-col>-->
            <!--            </el-row>-->

            <el-table v-loading="tableInfo.TableLoading.value" height="450" :data="tableInfo.list.value">
                <el-table-column label="ID" align="center" prop="id" />
                <el-table-column label="用户名" align="center" prop="username" />
                <el-table-column label="姓名" align="center" prop="realname" />
                <el-table-column label="单位" align="center" prop="company" />
                <el-table-column label="科室" align="center" prop="department" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button
                            link
                            type="primary"
                            icon="Edit"
                            @click="handleUpdate(scope.row)"
                            v-hasPermi="['evms:dms_permission:edit']"
                        >修改
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-row justify="space-between" align="bottom">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    :total="tableInfo.total.value"
                    v-model:page="tableInfo.pageParam.pageNum"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
    </div>
</template>
<script lang="ts">
export default {
    name: 'Dms_permission'
};
</script>
<script setup lang="ts">
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import { getCurrentInstance, ComponentInternalInstance, ref } from 'vue';
import { getTableInfo } from '@/minix/tables';
import { useRouter } from 'vue-router';
import { DmsUserInfo } from '@/model/dms_user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const router = useRouter();

// 列表
const tableInfo = getTableInfo<DmsUserInfo>('/evms/dms_device/getAllUser');
tableInfo.load();

/** 修改按钮操作 */
function handleUpdate(row: any) {
    router.push({
        path: '/evms/dms/dms_permission_info/' + row.id
    });
}

// 添加展开折叠控制
const isExpand = ref(false);
</script>
<style scoped lang="scss">
.app-container {
    background: #f8f9fb;
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
