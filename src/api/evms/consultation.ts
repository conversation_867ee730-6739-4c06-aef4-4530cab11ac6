/*
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-03-11 16:40:21
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-03-24 17:47:48
 * @FilePath: /evms-web/src/api/evms/consultation.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Http } from '@/utils/request';
import { ConsultationHospital, ConsultationHospitalParams, ConsultationOrder } from '@/model/consultation';
import { BaseApi } from '@/model/api';

// 删除会诊
export function delCns(id: string): Promise<BaseApi<null>> {
    return Http.delete(`/cns/consultation/${id}`);
}

// 取消会诊
export function cancelConsultation(ids: string): Promise<BaseApi<null>> {
    return Http.post('/cns/consultation/cancel', { ids }, false, 'form');
}

// 结束会诊
export function closeConsultation(ids: string): Promise<BaseApi<null>> {
    return Http.post('/cns/consultation/close', { ids }, false, 'form');
}

// 获取订单详情
export function getOrderDetail(id: string): Promise<BaseApi<ConsultationOrder>> {
    return Http.get(`/cns/consultation/${id}`);
}

// 新增会诊
export function addCnsOrder(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.post('/cns/consultation/add', data);
}

// 修改会诊
export function updateCnsOrder(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.put('/cns/consultation/update', data);
}

// 新增群组会诊
export function addCnsOrderGroup(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.post('/cns/consultation/add_teaching_group', data);
}

// 修改群组会诊
export function updateCnsOrderGroup(data: ConsultationOrder): Promise<BaseApi<null>> {
    return Http.put('/cns/consultation/update_teaching_group', data);
}

// 删除专家
export function deleteExpert(id: string): Promise<BaseApi<null>> {
    return Http.delete(`/evms/doctor_expert/${id}`);
}

// 添加专家
export function addExpert(data: string): Promise<BaseApi<null>> {
    return Http.post('/evms/doctor_expert/batchAdd', { ids: data }, false, 'form');
}

// 删除群组
export function deleteGroup(ids: string): Promise<BaseApi<null>> {
    return Http.delete(`/cns/teaching_group/${ids}`);
}

// 获取群组信息
export function getGroupDetail(id: string): Promise<BaseApi<ConsultationHospital>> {
    return Http.get(`/cns/teaching_group/${id}`);
}

// 新增群组
export function addGroup(data: ConsultationHospitalParams): Promise<BaseApi<null>> {
    return Http.post('/cns/teaching_group/add', data);
}

// 修改群组
export function updateGroup(data: ConsultationHospitalParams): Promise<BaseApi<null>> {
    return Http.put('/cns/teaching_group/update', data);
}

// 删除群组成员
export function deleteGroupMem(ids: string): Promise<BaseApi<null>> {
    return Http.delete(`/cns/teaching_group/removeMember/${ids}`);
}

// 添加群组成员
export interface GroupMemberData {
    id: number;
    userIds: number[];
}

export function addGroupMem(data: GroupMemberData): Promise<BaseApi<null>> {
    return Http.post('/cns/teaching_group/addMember', data);
}
