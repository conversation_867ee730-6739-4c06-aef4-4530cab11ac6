import type { ConsultationOrder } from '@/model/consultation';

export interface TeachingGroup {
    name: string;
}

export interface Group {
    name: string;
}

export interface Device {
    id: string;
    name: string;
    type: number;
    description: string;
    permission: {
        elab: boolean;
        ebs: boolean;
        outpatient: boolean;
        videoExportPermission: boolean;
    };
    hospitalVo?: {
        name: string;
    };
}

export interface ExtendedConsultationOrder extends Omit<ConsultationOrder, 'device'> {
    teachingGroups?: TeachingGroup[];
    groups?: Group[];
    device: Device;
}

export interface DeviceOption {
    value: string;
    id: number;
}

export interface BaseResponse<T = any> {
    code: number;
    message?: string;
    data: T;
}
