<template>
    <el-form v-show="open" ref="firstactivereportRef" :model="form" :rules="rules" label-position="top">
        <el-row class="form-con" justify="space-between">
            <el-col :span="24">
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="填写人" prop="writer">
                            <el-input v-model="form.writer" placeholder="请输入填写人" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="所在办事处" prop="office">
                            <el-input v-model="form.office" placeholder="请输入所在办事处" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="专家" prop="expert">
                            <el-input v-model="form.expert" placeholder="请输入专家" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="反馈" prop="feedback">
                            <el-input
                                type="textarea"
                                :rows="3"
                                v-model="form.feedback"
                                placeholder="请输入反馈"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="ipad画面" prop="ipadImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.ipadImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="E-lab画面" prop="elabImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.elabImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="E-bs画面" prop="ebsImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.ebsImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>

            <el-col :span="24" style="height: 100px">
                <el-row justify="end">
                    <el-button @click="cancel">返回</el-button>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                </el-row>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup name="Firstactivereport" lang="ts">
import {
    addFirstactivereport,
    updateFirstactivereport,
    getFirstActiveReportByDeviceId
} from '@/api/cl/firstactivereport';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ImageUpload from '@/components/ImageUpload/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const open = ref(false);
const loading = ref(true);
const ids = ref<any[]>([]);
const title = ref('');
const route = useRoute();
const router = useRouter();

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        device_id: undefined,
        office: undefined,
        expert: undefined,
        feedback: undefined,
        ipadImage: undefined,
        elabImage: undefined,
        ebsImage: undefined
    },
    rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 取消按钮 */
function cancel() {
    console.log('返回');
    router.back();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        deviceId: undefined,
        writer: undefined,
        office: undefined,
        expert: undefined,
        feedback: undefined,
        hospital: undefined,
        writeTime: undefined,
        ipadImage: undefined,
        elabImage: undefined,
        ebsImage: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
    };
    proxy?.resetForm('firstactivereportRef');
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['firstactivereportRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateFirstactivereport(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('修改成功');
                    form.value = response.data;
                    open.value = true;
                });
            } else {
                addFirstactivereport(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('新增成功');
                    form.value = response.data;
                    open.value = true;
                });
            }
        }
    });
}

function getCLFirstactivereports(id: any) {
    if (id) {
        queryParams.value.device_id = id;
        getFirstActiveReportByDeviceId(id).then((response: any) => {
            form.value = response.data;
            console.log('123' + response.data);
            form.value.deviceId = id;
            open.value = true;
        });
    } else {
        reset();
        open.value = true;
    }
}

getCLFirstactivereports(route.params.id);
</script>
