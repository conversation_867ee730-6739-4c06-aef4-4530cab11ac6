<script lang="ts" setup>
import { getDmsCamera, delDmsCamera, addDmsCamera, updateDmsCamera } from '@/api/evms/dms_camera';

import { getTableInfo } from '@/minix/tables';
import { CameraInfo } from '@/model/dms_device';
import { useRoute } from 'vue-router';
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { FormInstance } from 'element-plus';
import { switchType } from '@/assets/constant/switchType';
import { timestampToTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_camera_type } = proxy!.useDict('evms_dms_camera_type');

const props = defineProps(['meetingId']);
const tableInfo = getTableInfo<CameraInfo>('/evms/rtc_meeting/record/list', {
    meetingId: props.meetingId
});
tableInfo.load();
const ids = ref<any[]>([]);

// 删除
const handleDelete = (row: any) => {
    const _id = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除视频通道编号为"' + _id + '"的数据项？')
        .then(function() {
            return delDmsCamera(_id);
        })
        .then(res => {
            if (res.code === 200) {
                tableInfo.reset();
                proxy!.$modal.msgSuccess('删除成功');
            } else {
                proxy!.$modal.msgError(res.msg);
            }
        })
        .catch(() => {
        });
};
</script>

<template>
    <div class="dms_hospital_info_video">
        <el-table
            v-loading="tableInfo.TableLoading.value"
            :data="tableInfo.list.value"
            height="450"
            row-key="id"
            style="margin-top: 20px"
        >
            <el-table-column align="center" label="ID" prop="id" />
            <el-table-column align="center" label="视频链接" prop="url" />
            <el-table-column align="center" label="开始时间" prop="startTime">
                <template #default="scope">
                    <span>{{ timestampToTime(scope.row.startTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="结束时间" prop="duration">
                <template #default="scope">
                    <span>{{ timestampToTime(scope.row.startTime + scope.row.duration) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['rtc:rtc_meeting:remove']"
                        icon="Delete"
                        link
                        type="danger"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-row align="bottom" justify="space-between">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                v-model:limit="tableInfo.pageParam.pageSize"
                v-model:page="tableInfo.pageParam.pageNum"
                :total="tableInfo.total.value"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
</template>

<style lang="scss" scoped></style>
