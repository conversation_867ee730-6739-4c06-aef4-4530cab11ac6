import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn,
    ElUpload,
    UploadRawFile,
    UploadProps,
    genFileId
} from 'element-plus';
import { RefsKes } from '@/type/common';
import RightToolar from '@/components/RightToolbar/index.vue';
import moment from 'moment';
import Pagination from '@/components/Pagination/index.vue';
import {
    getContractList,
    getExpert,
    delExpert,
    addExpert,
    updateExpert,
    ContractListType
} from '@/api/evms/expert';
interface QueryParams extends Partial<ContractListType> {
    pageNum: number;
    pageSize: number;
}
export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;

        const state = reactive({
            expertList: [] as ContractListType[],
            visible: false,
            showSearch: true,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                realName: ''
            } as QueryParams,
            loading: false,
            dateRange: [] as string[],
            total: 0,
            ids: [] as Array<string | number>,
            single: true,
            multiple: false,
            uploadAction: import.meta.env.VITE_APP_BASE_API + '/evms/contract/uploadFile',
            currentRow: null as ContractListType | null,
            oldCurrentRow: null as ContractListType | null
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });
        function handleOk() {
            emit('ok', state.currentRow);
            close();
        }
        function close() {
            state.visible = false;
        }

        function show() {
            state.visible = true;
            handleQuery();
        }

        const slots = {
            column: {
                uploadTimeStamp({ row: record }: { row: ContractListType }) {
                    return (
                        <span>
                            {record.uploadTimeStamp
                                ? moment(record.uploadTimeStamp).format('YYYY-MM-DD HH:mm:ss')
                                : '--'}
                        </span>
                    );
                },
            },
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                },
            },
            upload() {
                return (
                    <ElButton type="primary" icon="Upload">
                        上传合同
                    </ElButton>
                );
            },
        };
        function getList() {
            state.loading = true;
            getContractList(proxy?.addDateRange(state.queryParams, state.dateRange)).then(response => {
                state.expertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }
        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }
        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }
        function resetQuery() {
            state.dateRange = [];
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleExceed(files: any) {
            let uploadRef = instance.refs.uploadRef;
            uploadRef.value!.clearFiles();
            const file = files[0] as UploadRawFile;
            file.uid = genFileId();
            uploadRef.value!.handleStart(file);
        }
        function onCurrentChange(currentRow: ContractListType, oldCurrentRow: ContractListType) {
            state.currentRow = currentRow;
            state.oldCurrentRow = oldCurrentRow;
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="上传合同"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem>
                        <ElUpload
                            ref="uploadRef"
                            class="upload-demo"
                            action={state.uploadAction}
                            limit={1}
                            on-exceed={handleExceed}
                            auto-upload={true}
                            v-slots={{ trigger: slots.upload }}
                            show-file-list={false}
                            on-success={handleQuery}
                        ></ElUpload>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}
                <ElRow gutter={10} class="mb8">
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable
                    v-loading={state.loading}
                    data={state.expertList}
                    highlightCurrentRow={true}
                    onCurrent-change={onCurrentChange}
                >
                    <ElTableColumn
                        label="上传时间"
                        align="center"
                        prop="uploadTimeStamp"
                        width="180"
                        v-slots={slots.column.uploadTimeStamp}
                    />
                    <ElTableColumn label="文件名" align="center" prop="fileOriginalName" />
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
            </ElDialog>
        );
    },
});
