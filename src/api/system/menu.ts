import request from '@/utils/request';

// 查询菜单列表
export function listMenu(query?: any) {
    return request({
        url: '/sys/menu/all',
        method: 'get',
        params: query,
    });
}

// 查询菜单详细
export function getMenu(menuId: any) {
    return request({
        url: '/sys/menu/' + menuId,
        method: 'get',
    });
}

// 查询菜单下拉树结构
export function treeselect() {
    return request({
        url: '/sys/menu/tree',
        method: 'get',
    });
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId: any) {
    return request({
        url: '/sys/menu/roleMenuTreeselect/' + roleId,
        method: 'get',
    });
}

// 新增菜单
export function addMenu(data: any) {
    return request({
        url: '/sys/menu',
        method: 'post',
        data: data,
    });
}

// 修改菜单
export function updateMenu(data: any) {
    return request({
        url: '/sys/menu',
        method: 'put',
        data: data,
    });
}

// 删除菜单
export function delMenu(menuId: any) {
    return request({
        url: '/sys/menu/' + menuId,
        method: 'delete',
    });
}
