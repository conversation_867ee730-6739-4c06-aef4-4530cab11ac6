import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { RefsKes } from '@/type/common';
import RightToolar from '@/components/RightToolbar/index.vue';
import moment from 'moment';
import Pagination from '@/components/Pagination/index.vue';
import DictTag from '@/components/DictTag/index.vue';
import { hospitalBindingDoc } from '@/api/evms/consult_hospital';
import {
    listExpert,
    getExpert,
    delExpert,
    addExpert,
    updateExpert,
    EvmsConsultExpert,
    saveExpert
} from '@/api/evms/expert';
import { EvmsConsultHospitalExpert } from '@/api/evms/consult_hospital';
import AddDoc from '@/views/evms/expert/model/addDoc';
import { instanceOfObj, uniqueByKey } from '@/utils/ruoyi';

interface QueryParams extends Partial<EvmsConsultExpert> {
    pageNum: number;
    pageSize: number;
    displayUnbound: boolean;
}
export default defineComponent({
    props: {
        selectType: {
            type: String,
            require: false,
            default: 'single' // multiple 多选 single 单选
        },
    },
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const { sys_yes_no } = proxy?.useDict('sys_yes_no')!;
        let instance: RefsKes;
        const state = reactive({
            expertList: [] as EvmsConsultExpert[],
            visible: false,
            showSearch: true,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                realName: '',
                displayUnbound: false
            } as QueryParams,
            loading: false,
            total: 0,
            ids: [] as Array<string | number>, // 多选的id集合
            selectionData: [] as EvmsConsultExpert[], // 多选的data
            single: true,
            multiple: false,
            currentRow: null as EvmsConsultExpert | null, // 单选的data
            hospitalId: 0,
            pageType: 'binding'
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });
        function handleOk() {
            if (props.selectType === 'single' && state.currentRow) {
                if (state.pageType === 'binding') {
                    hospitalBindingDoc({
                        expertId: state.currentRow.id,
                        hospitalId: state.hospitalId
                    }).then(res => {
                        close();
                        emit('ok');
                        proxy?.$modal.msgSuccess('绑定成功');
                    });
                }
            } else if (props.selectType === 'multiple' && state.ids.length > 0) {
                if (state.pageType === 'orderSelect') {
                    close();
                    if (props.selectType === 'multiple') {
                        emit('ok', {
                            type: props.selectType,
                            ids: state.ids,
                            selectionData: state.selectionData
                        });
                    }
                }
            } else {
                proxy?.$modal.msgWarning('请选择专家');
            }
        }
        function close() {
            state.visible = false;
        }

        // type binding:绑定专家 orderSelect:会诊订单选择专家
        function show(data: { hospitalId: number; type: string; ids: number[] }) {
            state.visible = true;
            state.pageType = data.type;
            if (data.type === 'binding') {
                state.hospitalId = data.hospitalId;
            }
            if (data.type === 'orderSelect') {
                state.queryParams.displayUnbound = true;
                state.ids = instanceOfObj(data.ids);
            }
            handleQuery();
        }

        const slots = {
            column: {
                serviceCount({ row: record }: { row: EvmsConsultExpert }) {
                    return <p style="color:#0581ce;">{record.serviceCount}</p>;
                },
                signingStartTimeStamp({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingStartTimeStamp
                                ? moment(record.signingStartTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingEndTimeStamp({ row: record }: { row: EvmsConsultExpert }) {
                    return (
                        <span>
                            {record.signingEndTimeStamp
                                ? moment(record.signingEndTimeStamp).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                },
                signingStatus({ row: record }: { row: EvmsConsultExpert }) {
                    return <p>{record.signingStatus ? '已签约' : '未签约'}</p>;
                },
            },
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                },
            },
        };
        function getList() {
            state.loading = true;
            listExpert(state.queryParams).then(response => {
                loadDataAndUpdateSelection(response.data.rows);
                state.expertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }
        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }
        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }
        function resetQuery() {
            proxy?.resetForm('queryRef');
            handleQuery();
        }
        function onCurrentChange(currentRow: EvmsConsultExpert) {
            if (props.selectType === 'single') {
                state.currentRow = currentRow;
            }
        }

        function handleSelectionChange(selection: EvmsConsultExpert[]) {
            state.ids = selection.map(item => item.id);
            state.selectionData = instanceOfObj(selection);
            // state.single = selection.length !== 1;
            // state.multiple = !selection.length;
        }
        function loadDataAndUpdateSelection(rows: EvmsConsultExpert[]) {
            if (state.ids.length === 0) {
                instance.refs.elTableRef.clearSelection();
            }
            const selectionRows: EvmsConsultExpert[] = instance.refs.elTableRef.getSelectionRows();
            rows.map(item => {
                if (selectionRows.length > 0) {
                    selectionRows.map(m => {
                        if (!state.ids.includes(m.id)) {
                            instance.refs.elTableRef.toggleRowSelection(item, true);
                        }
                    });
                } else {
                    if (state.ids.includes(item.id)) {
                        instance.refs.elTableRef.toggleRowSelection(item, true);
                    }
                }
            });
        }

        expose({
            show
        });
        return () => (
            <ElDialog
                title="专家列表"
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="专家名称" prop="expertName">
                        <ElInput
                            v-model={state.queryParams.expertName}
                            placeholder="请输入专家名称"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}
                <ElRow gutter={10} class="mb8">
                    <RightToolar
                        v-model={[state.showSearch, 'showSearch']}
                        onQueryTable={getList}
                    ></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable
                    ref="elTableRef"
                    v-loading={state.loading}
                    data={state.expertList}
                    highlightCurrentRow={props.selectType === 'single' ? true : false}
                    onCurrent-change={onCurrentChange}
                    onSelection-change={handleSelectionChange}
                    rowKey={row => {
                        return row.id;
                    }}
                >
                    {props.selectType === 'multiple' ? (
                        <ElTableColumn reserveSelection={true} type="selection" width="55" align="center" />
                    ) : (
                        ''
                    )}

                    <ElTableColumn label="专家ID" align="center" prop="id" />
                    <ElTableColumn label="专家名称" align="center" prop="realName" />
                    <ElTableColumn
                        label="签约状态"
                        align="center"
                        prop="signingStatus"
                        v-slots={slots.column.signingStatus}
                    />
                    <ElTableColumn
                        label="服务总次数"
                        align="center"
                        v-slots={slots.column.serviceCount}
                        prop=""
                    />
                    <ElTableColumn
                        label="签约时间"
                        align="center"
                        prop="signingStartTimeStamp"
                        width="180"
                        v-slots={slots.column.signingStartTimeStamp}
                    />
                    <ElTableColumn
                        label="到期时间"
                        align="center"
                        prop="signingEndTimeStamp"
                        width="180"
                        v-slots={slots.column.signingEndTimeStamp}
                    />
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
            </ElDialog>
        );
    },
});
