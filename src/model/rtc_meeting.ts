export interface RtcMeeting {
    id: string;
    rtcRoomId: string;
    meetingNumber: string;
    cate: number;
    type: number;
    mode: number;
    theme: string;
    nickname: string;
    location: Location;
    description: string;
    reason: string;
    hostUserId: number;
    createBy: any;
    createTime: Date;
    updateBy: any;
    updateTime: string;
    personLimit: number;
    businessPerson: string;
    engineer: string;
    qrCode: string;
    setting: RtcMeetingSetting;
    status: number;
    startTime: number;
    linkUrl: string;
    wbRoomId: string;
    endTime: number;
    imConversationId: number;
}

export interface RtcMeetingDetail {
    detail: {
        actualEndTime?: number;
        authorName?: string; // 详情字段
        actualStartTime?: number;
        actualTheme?: string; // 前端自定义字段
    };
    businessPerson: string;
    cate: number;
    createBy?: string;
    createTime?: string;
    description: string;
    endTime: number;
    engineer: string;
    hostUserId: number;
    id: number;
    imConversationId?: number;
    linkUrl?: string;
    location: string;
    meetingNumber?: string; // 详情字段
    memberIds?: number[]; // 新增字段
    mode: number;
    nickname: string;
    orgIds?: number[]; // 新增字段
    password?: string; // 新增字段
    personLimit: number;
    qrCode?: string; // 详情字段
    reason?: string; // 详情字段
    rtcRoomId?: string; // 详情字段
    seetingArr?: string[]; // 前端自定义字段
    setting: RtcMeetingSetting;
    startTime: number;
    status?: number; // 详情字段
    theme: string;
    time?: Date[]; // 前端自定义字段
    duration?: number; // 会议时长 前端自定义字段
    type: number;
    updateBy?: string; // 详情字段
    updateTime?: string; // 详情字段
    wbRoomId?: string; // 详情字段
    deviceLinkUrl?: { // 设备链接URL字段
        brainmed?: string; // BrainMed链接
        link?: string; // 普通链接
    };
}

export interface RtcMeetingSetting {
    allowInvitations: boolean;
    allowShareLink: boolean;
    allowAllShare: boolean;
    allowChat: boolean;
    allowEditNickname: boolean;
    allowJoinBeforeHost: boolean;
    mute: boolean;
    participationRange: boolean;

    [key: string]: boolean;
}

export interface RtcMemberItem {
    avatar: string;
    banned: boolean;
    fistTime: number;
    id: number;
    lastTime: number;
    meeting: object;
    nickname: string;
    removed: 0 | 1;
    role: 0 | 1 | 2 | 3;
    rtcUserId: string;
    title: string;
    totalDuration: number;
    unit: string;
    userId: number;
    isOnline: 'T' | 'F';
}

// 进出日志
export interface RtcMemberJoinLog {
    joinTime: number;
    leaveTime: number;
}

export interface RtcChat {
    id: number;
    imMessageId: number;
    rtcRoomId: string;
    imConversationId: number;
    conversationType: number;
    fromId: number;
    fromName: string;
    fromAvatar: string;
    toId: number;
    type: number;
    message: string;
    index: number;
    status: number;
    onlineOperator: null;
    eventTime: string;
}

export interface RtcMessage {
    text?: string;
    video?: string;
    audio?: string;
    file?: string;
    __files?: {
        media: {
            remoteURL: string;
            ext: {
                's:file_ext_key_origin_height': number;
                's:file_ext_key_origin_width': number;
            };
        };
    };
}

export interface RtcOrgItem {
    id: number;
    createBy: string;
    updateBy: string;
    createTime: string;
    updateTime: string;
    name: string;
    type: string;
    defaults: boolean;
}
