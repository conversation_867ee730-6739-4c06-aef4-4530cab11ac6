import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { RefsKes } from '@/type/common';
import { EvmsCousultOrderDetail, OrderParams } from '@/api/evms/ordert';
import SelectDevice from './selectDevice';
import { DeviceVos } from '@/api/evms/consult_hospital';
import { addOrdert, updateOrdert } from '@/api/evms/ordert';
import { EvmsConsultExpert } from '@/api/evms/expert';
import ExpertDoc from '@/views/evms/consult_hospital/model/expertDoc';
import moment from 'moment';
import { instanceOfObj } from '@/utils/ruoyi';

export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const state = reactive({
            visible: false,
            form: {} as OrderParams,
            rules: {
                deviceName: [{ required: true, message: '请选择医院下的导管室', trigger: 'change' }],
                cousultType: [{ required: true, message: '请选择会诊类型', trigger: 'change' }],
                expertNameStr: [{ required: true, message: '请选择邀请专家', trigger: 'change' }],
                surgicalTime: [{ required: true, message: '请选择手术时间', trigger: 'change' }] as any[]
            },
            record: null as EvmsCousultOrderDetail | null,
            currentRow: null as DeviceVos | null,
            ids: [] as number[]
        });

        let instance: RefsKes;
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });
        function handleOk() {
            console.log(1111);
            instance.refs.formRef.validate((valid: any, fields: any) => {
                if (valid) {
                    if (state.form.cousultType === 1) {
                        state.form.surgicalTime = moment().add(1, 'minutes').format('YYYY-MM-DD HH:mm:ss');
                        state.form.surgicalTimeStamp = moment().add(1, 'minutes').valueOf();
                    } else {
                        state.form.surgicalTime = moment(state.form.surgicalTime).format(
                            'YYYY-MM-DD HH:mm:ss'
                        );
                        state.form.surgicalTimeStamp = moment(state.form.surgicalTime).valueOf();
                    }

                    if (!state.record) {
                        state.form.surgicalType = '';
                        addOrdert(state.form).then(res => {
                            close();
                            proxy?.$modal.msgSuccess('添加成功');
                            emit('ok');
                        });
                    } else {
                        updateOrdert(state.form).then(res => {
                            close();
                            proxy?.$modal.msgSuccess('修改成功');
                            emit('ok');
                        });
                    }
                }
            });
        }
        function close() {
            state.visible = false;
        }

        function add(record?: DeviceVos) {
            state.visible = true;
            state.record = null;
            state.ids = [];
            state.form = {} as OrderParams;
            if (record) {
                state.form.deviceId = record.deviceId;
                state.form.deviceName = record.deviceName;
            }
            instance.refs.formRef && instance.refs.formRef.resetFields();
        }
        function edit(record: EvmsCousultOrderDetail) {
            state.record = record;
            state.form = {
                cousultType: state.record.cousultType,
                description: state.record.description,
                deviceId: state.record.deviceId,
                experts: state.record.experts,
                mainDescription: state.record.mainDescription,
                surgicalTimeStamp: state.record.surgicalTimeStamp,
                surgicalType: state.record.surgicalType,
                cancelReason: state.record.cancelReason,
                orderNumber: state.record.orderNumber,
                orderStatus: state.record.orderStatus,
                deviceName: state.record.deviceName,
                realName: state.record.expertName,
                surgicalTime: state.record.surgicalTime,
                expertNameStr: ''
            };
            state.ids = [];
            state.record.experts.map((item, index) => {
                state.ids.push(item.expertId);
                state.form.expertNameStr += item.expertName + (index + 1 < record.experts.length ? ',' : '');
            });
            state.visible = true;
            instance.refs.formRef && instance.refs.formRef.resetFields();
        }
        expose({
            add,
            edit
        });
        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                },
            },
            appendDevice() {
                return (
                    <ElButton type="primary" onClick={handleHospitalShow}>
                        选择
                    </ElButton>
                );
            },
            appendDoc() {
                return (
                    <ElButton type="primary" onClick={handleDocShow}>
                        选择
                    </ElButton>
                );
            },
        };

        function handleHospitalShow() {
            if (!state.record) {
                instance.refs.SelectDeviceRef.show();
            }
        }

        function handleDocShow() {
            if (!state.record) {
                instance.refs.ExpertDocRef.show({ type: 'orderSelect', ids: state.ids });
            }
        }

        function onSelected(currentRow: DeviceVos) {
            state.currentRow = currentRow;
            state.form.deviceName = state.currentRow.deviceName;
            state.form.deviceId = state.currentRow.deviceId;
        }

        // 确认选择专家后的处理
        function expertDocOk(data: { type: string; ids: number[]; selectionData: EvmsConsultExpert[] }) {
            state.ids = instanceOfObj(data.ids);
            state.form.experts = [];
            state.form.expertNameStr = '';
            data.selectionData.map((item, index) => {
                state.form.experts.push({ expertId: item.id, expertName: item.realName });
                state.form.expertNameStr +=
                    item.realName + (index + 1 < data.selectionData.length ? ',' : '');
            });
        }

        return () => (
            <ElDialog
                title={state.record ? '编辑会诊订单' : '添加会诊订单'}
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                <ElForm ref="formRef" model={state.form} rules={state.rules} labelWidth="80px">
                    <ElRow>
                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="导管室" prop="deviceName">
                                <ElInput
                                    readonly
                                    v-model={state.form.deviceName}
                                    placeholder="请选择医院下的导管室"
                                    v-slots={{ append: slots.appendDevice }}
                                    disabled={state.record ? true : false}
                                />
                            </ElFormItem>
                        </ElCol>
                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="会诊类型" prop="cousultType">
                                <ElSelect
                                    placeholder="请选择会诊类型"
                                    v-model={state.form.cousultType}
                                    clearable
                                >
                                    <ElOption key={1} label="急诊" value={1}></ElOption>
                                    <ElOption key={2} label="预约" value={2}></ElOption>
                                </ElSelect>
                            </ElFormItem>
                        </ElCol>
                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="邀请专家" prop="expertNameStr">
                                <ElInput
                                    readonly
                                    v-model={state.form.expertNameStr}
                                    placeholder="请选择邀请专家"
                                    v-slots={{ append: slots.appendDoc }}
                                    disabled={state.record ? true : false}
                                />
                            </ElFormItem>
                        </ElCol>
                        {state.form.cousultType !== 1 ? (
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="手术时间" prop="surgicalTime">
                                    <ElDatePicker
                                        v-model={state.form.surgicalTime}
                                        valueFormat="YYYY-MM-DD HH:mm"
                                        format="YYYY-MM-DD HH:mm"
                                        type="datetime"
                                        placeholder="请选择手术时间"
                                    ></ElDatePicker>
                                </ElFormItem>
                            </ElCol>
                        ) : (
                            ''
                        )}

                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="主诉" prop="mainDescription">
                                <ElInput
                                    v-model={state.form.mainDescription}
                                    type="textarea"
                                    autosize={{ minRows: 2, maxRows: 4 }}
                                    placeholder="请输入主诉"
                                ></ElInput>
                            </ElFormItem>
                        </ElCol>
                        <ElCol span={12}>
                            <ElFormItem labelWidth="150px" label="备注" prop="description">
                                <ElInput
                                    v-model={state.form.description}
                                    type="textarea"
                                    autosize={{ minRows: 2, maxRows: 4 }}
                                    placeholder="请输入备注"
                                ></ElInput>
                            </ElFormItem>
                        </ElCol>
                    </ElRow>
                </ElForm>
                <SelectDevice ref="SelectDeviceRef" onSelected={onSelected}></SelectDevice>
                <ExpertDoc selectType="multiple" ref="ExpertDocRef" onOk={expertDocOk}></ExpertDoc>
            </ElDialog>
        );
    },
});
