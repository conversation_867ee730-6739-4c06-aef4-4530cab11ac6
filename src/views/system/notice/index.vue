<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="公告标题" prop="title">
                <el-input
                    v-model="queryParams.title"
                    placeholder="请输入公告标题"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="操作人员" prop="createBy">
                <el-input
                    v-model="queryParams.createBy"
                    placeholder="请输入操作人员"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="公告类型" clearable style="width: 200px">
                    <el-option
                        v-for="dict in sys_notice_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:notice:add']" type="primary" plain icon="Plus" @click="handleAdd"
                    >新增</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:notice:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    >修改</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:notice:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    >删除</el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="noticeList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" align="center" prop="id" width="100" />
            <el-table-column label="公告标题" align="center" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="公告类型" align="center" prop="type" width="100">
                <template #default="scope">
                    <dict-tag :options="sys_notice_type" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status" width="100">
                <template #default="scope">
                    <dict-tag :options="sys_notice_status" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column label="创建者" align="center" prop="createBy" width="100" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['sys:notice:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        >修改</el-button
                    >
                    <el-button
                        v-hasPermi="['sys:notice:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改公告对话框 -->
        <el-dialog v-model="open" :title="title" width="780px" append-to-body>
            <el-form ref="noticeRef" :model="form" :rules="rules" label-width="80px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="公告标题" prop="title">
                            <el-input v-model="form.title" placeholder="请输入公告标题" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="公告类型" prop="type">
                            <el-select v-model="form.type" placeholder="请选择">
                                <el-option
                                    v-for="dict in sys_notice_type"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="状态">
                            <el-radio-group v-model="form.status">
                                <el-radio
                                    v-for="dict in sys_notice_status"
                                    :key="dict.value"
                                    :label="dict.value"
                                    >{{ dict.label }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="内容">
                            <el-input
                                v-model="form.content"
                                :rows="6"
                                type="textarea"
                                placeholder="请输入内容"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Notice" lang="ts">
/* eslint-disable camelcase */
import { listNotice, getNotice, delNotice, addNotice, updateNotice } from '@/api/system/notice';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_notice_status, sys_notice_type } = proxy!.useDict('sys_notice_status', 'sys_notice_type');

const noticeList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        createBy: undefined,
        status: undefined,
    },
    rules: {
        title: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
    },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
    loading.value = true;
    listNotice(queryFormat(queryParams.value)).then((response: any) => {
        noticeList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}
/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}
/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        title: undefined,
        type: undefined,
        content: undefined,
        status: '0',
    };
    proxy!.resetForm('noticeRef');
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加公告';
}
/**修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const id = row.id || ids.value;
    getNotice(id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = '修改公告';
    });
}
/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['noticeRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id !== undefined) {
                updateNotice(form.value).then(response => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addNotice(form.value).then(response => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row: any) {
    const noticeIds = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除公告编号为"' + noticeIds + '"的数据项？')
        .then(function () {
            return delNotice(noticeIds);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch((e: any) => {
            console.log(e);
        });
}

getList();
</script>
