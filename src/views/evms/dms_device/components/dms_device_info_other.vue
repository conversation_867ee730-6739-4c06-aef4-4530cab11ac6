<template>
    <el-form v-show="open" ref="equipmentRef" :model="form" :rules="rules" label-position="top">
        <el-row class="form-con" justify="space-between">
            <el-col :span="24">
                <b>其他设备信息</b><br /><br />
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="主产品" prop="main">
                            <el-input v-model="form.main" placeholder="请输入设备名称" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="摄像头数量" prop="camera">
                            <el-input v-model="form.camera" placeholder="请输入摄像头数量" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="降频仪数量" prop="frequency">
                            <el-input v-model="form.frequency" placeholder="请输入降频仪数量" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="DSA设备数量" prop="dsa">
                            <el-input v-model="form.dsa" placeholder="请输入DSA设备数量" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="3D设备数量" prop="stereoscopic">
                            <el-input v-model="form.stereoscopic" placeholder="请输入3D设备数量" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="ipad设备数量" prop="ipad">
                            <el-input v-model="form.ipad" placeholder="请输入ipad设备数量" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="其他设备" prop="others">
                            <el-input
                                type="textarea"
                                v-model="form.others"
                                placeholder="请输入其他设备信息"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="病例保存期限" prop="preservation">
                            <el-input v-model="form.preservation" placeholder="请输入病例保存期限(天)" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="花生壳到期时间" prop="expire">
                            <el-date-picker
                                clearable
                                v-model="form.expire"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择花生壳到期时间"
                                :disabled-date="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="网络状态" prop="network">
                            <el-input v-model="form.network" placeholder="请输入网络状态" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="费用信息" prop="expense">
                            <el-input v-model="form.expense" placeholder="请输入费用信息" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <br /><br /><b>医院对接信息</b><br /><br />
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="合作专家/医生" prop="cooperator">
                            <el-input v-model="form.cooperator" placeholder="请输入专家/医生姓名" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="专家联系方式" prop="coopContact">
                            <el-input v-model="form.coopContact" placeholder="请输入专家联系方式" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="对接医生" prop="dockingDoctor">
                            <el-input v-model="form.dockingDoctor" placeholder="请输入对接医生姓名" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="对接医生联系方式" prop="dockContact">
                            <el-input v-model="form.dockContact" placeholder="请输入对接医生联系方式" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="专家办公室地址" prop="office">
                            <el-input v-model="form.office" placeholder="请输入专家办公室地址" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="辐射中心地址" prop="radiationCenter">
                            <el-input v-model="form.radiationCenter" placeholder="请输入辐射中心地址" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <br /><br /><b>脑医汇合作信息</b><br /><br />
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="脑医汇负责人" prop="director">
                            <el-input v-model="form.director" placeholder="请输入脑医汇负责人" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="合作形式" prop="ways">
                            <el-input v-model="form.ways" placeholder="请输入合作形式" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="产权方" prop="equity">
                            <el-input v-model="form.equity" placeholder="请输入产权方" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="18">
                        <el-form-item label="合作信息备注" prop="remark">
                            <el-input
                                type="textarea"
                                :rows="3"
                                v-model="form.remark"
                                placeholder="请输入合作信息备注"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>

            <el-col :span="24" style="height: 100px">
                <el-row justify="end">
                    <el-button @click="cancel">返回</el-button>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                </el-row>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup name="Equipment" lang="ts">
import { addOtherInfo, getOtherInfoById } from '@/api/evms/dms_other_info';
import { ComponentInternalInstance, getCurrentInstance, reactive, ref, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const open = ref(false);
const route = useRoute();
const router = useRouter();
const deviceId = useRoute().params.id;
const props = defineProps(['deviceId']);

const data = reactive<{
    form: any;
    rules: any;
}>({
    form: {},
    rules: {}
});

const { form, rules } = toRefs(data);

/** 日期限制为 今天之前日期*/
function pickerOptions(time: Date) {
    const today = new Date();
    today.setHours(23, 59, 59, 59);
    return time.getTime() > today.getTime();
}

/** 取消按钮 */
function cancel() {
    console.log('返回');
    router.back();
}

/** 表单重置 */
function reset() {
    form.value = {
        deviceId: undefined,
        equipmentId: undefined,
        main: undefined,
        camera: undefined,
        frequency: undefined,
        dsa: undefined,
        stereoscopic: undefined,
        ipad: undefined,
        others: undefined,
        preservation: undefined,
        expire: undefined,
        network: undefined,
        expense: undefined,

        cooperatorId: undefined,
        cooperator: undefined,
        coopContact: undefined,
        dockingDoctor: undefined,
        dockContact: undefined,
        office: undefined,
        radiationCenter: undefined,

        contactInfoId: undefined,
        director: undefined,
        ways: undefined,
        equity: undefined,
        remark: undefined
    };
    proxy?.resetForm('equipmentRef');
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['equipmentRef'] as any).validate((valid: boolean) => {
        if (valid) {
            addOtherInfo(form.value).then((response: any) => {
                proxy?.$modal.msgSuccess('保存成功');
                form.value = response.data;
                open.value = true;
            });
        }
    });
}

function getOtherInfo(id: any) {
    if (id && id != 0) {
        getOtherInfoById(id).then((response: any) => {
            form.value = response.data;
            form.value.deviceId = id;
            open.value = true;
        });
    }
}

getOtherInfo(props.deviceId);
</script>
