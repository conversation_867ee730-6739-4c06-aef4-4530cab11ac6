<script lang="ts" setup>
import { getHospitalListByKeywords, listDmsDevice } from '@/api/evms/dms_device';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ref } from 'vue';
import { DmsHospitalInfo } from '@/model/dms_hospital';

const props = defineProps({
    formModel: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['search', 'reset', 'update:device-id', 'update:hospital-name']);

// 展开折叠控制
const isExpand = ref(false);

// 设备选择状态
const deviceName = ref('');
const hospitalName = ref('');
const resetHandler = () => {
    deviceName.value = '';
    hospitalName.value = '';

    emit('reset');
};
// 查询设备列表
const querySearchDevice = async (queryString: string, cb: (suggestions: any[]) => void) => {
    try {
        const response = await listDmsDevice({ q_like_name: queryString });
        const results = response as unknown as BaseResponse<{ rows: Device[] }>;

        if (results.code === 200 && results.data?.rows?.length > 0) {
            const suggestions = results.data.rows.map(item => ({
                value: item.name,
                id: Number(item.id)
            }));
            cb(suggestions);
        } else {
            cb([]);
        }
    } catch (error) {
        console.error('查询设备列表失败:', error);
        cb([]);
    }
};

// 选择设备
const handleSelectDevice = (item: any) => {
    deviceName.value = item.value;
    emit('update:device-id', item.id);
};

const querySearchHospital = async (queryString: string, cb: any) => {
    const results = await getHospitalListByKeywords({ keywords: queryString });
    // call callback function to return suggestions
    if (results.code === 200) {
        console.log(results.data, '--1');
        // 判断results.data是一个数组并且长度大于0 使用type

        cb(
            results?.data && (results.data as DmsHospitalInfo[])?.length > 0
                ? (results.data as DmsHospitalInfo[]).map((item: any) => {
                    return {
                        value: item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};
const handleSelectHospital = (item: HopitalItem) => {
    console.log(item);
    emit('update:hospital-name', item.value);
    hospitalName.value = item.value;
};
</script>

<template>
    <el-card shadow="never" style="border-radius: 12px">
        <el-form :inline="true" :model="formModel" label-width="100" size="default">
            <!-- 始终显示的基础搜索项 -->
            <el-form-item label="ID" prop="id">
                <el-input
                    v-model="formModel.id"
                    clearable
                    placeholder="请输入"
                    style="width: 240px"
                    @keyup.enter="$emit('search')"
                />
            </el-form-item>
            <el-form-item label="示教主题" prop="theme">
                <el-input
                    v-model="formModel.like_theme"
                    clearable
                    placeholder="请输入主题"
                    style="width: 240px"
                    @keyup.enter="$emit('search')"
                />
            </el-form-item>
            <el-form-item label="发起人" prop="organizer">
                <el-input
                    v-model="formModel.like_Jorganizer_realname"
                    clearable
                    placeholder="请输入发起人"
                    style="width: 240px"
                    @keyup.enter="$emit('search')"
                />
            </el-form-item>

            <!-- 可折叠的高级搜索项 -->
            <template v-if="isExpand">
                <el-form-item label="所属医院" prop="device">
                    <el-autocomplete
                        v-model="hospitalName"
                        :fetch-suggestions="querySearchHospital"
                        clearable
                        placeholder="请选择医院"
                        style="width: 240px"
                        @clear="$emit('update:hospital-name', '')"
                        @select="handleSelectHospital"
                        @keyup.enter="$emit('search')"
                    />
                </el-form-item>
                <el-form-item label="导管室" prop="device">
                    <el-autocomplete
                        v-model="deviceName"
                        :fetch-suggestions="querySearchDevice"
                        clearable
                        placeholder="请选择导管室"
                        style="width: 240px"
                        @clear="$emit('update:device-id', '')"
                        @select="handleSelectDevice"
                        @keyup.enter="$emit('search')"
                    />
                </el-form-item>
                <el-form-item label="预约时间" prop="dateStr">
                    <el-date-picker
                        v-model="formModel.startTimeUtc"
                        format="YYYY-MM-DD"
                        placeholder="请选择时间"
                        style="width: 240px"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                    />
                </el-form-item>
            </template>

            <!-- 搜索按钮区域 -->
            <el-row :gutter="20" justify="start" style="width: 100%; margin-top: 10px">
                <el-col :span="12" style="padding-left: 50px">
                    <el-button v-hasPermi="['cns:cns_consultation:query']" icon="Search" type="primary"
                               @click="$emit('search')">搜索
                    </el-button>
                    <el-button icon="Refresh" @click="resetHandler">重置</el-button>
                    <el-button type="text" @click="isExpand = !isExpand">
                        {{ isExpand ? '收起' : '展开' }}
                        <el-icon class="el-icon--right">
                            <arrow-up v-if="isExpand" />
                            <arrow-down v-else />
                        </el-icon>
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
    </el-card>
</template>

<style lang="scss" scoped>
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
