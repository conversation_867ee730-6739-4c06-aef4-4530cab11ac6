<template>
    <div class="app-container">
        <el-card shadow="never" style="padding-right: 50px; border-radius: 12px">
            <el-form
                v-show="showSearch"
                :inline="true"
                :model="tableInfo.from"
                label-width="100"
                size="default"
            >
                <!-- 始终显示的基础搜索项 -->
                <el-form-item label="ID" prop="id">
                    <el-input
                        v-model="tableInfo.from.id"
                        clearable
                        placeholder="请输入ID"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="会议名称" prop="theme">
                    <el-input
                        v-model="tableInfo.from.like_theme"
                        clearable
                        placeholder="请输入会议名称"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="会议号" prop="meetingNumber">
                    <el-input
                        v-model="tableInfo.from.meetingNumber"
                        clearable
                        placeholder="请输入会议号"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="主持人" prop="nickname">
                    <el-input
                        v-model="tableInfo.from.like_nickname"
                        clearable
                        placeholder="请输入主持人"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>

                <!-- 可折叠的高级搜索项 -->
                <template v-if="isExpand">
                    <el-form-item label="会议分类" prop="cate">
                        <el-select
                            v-model="tableInfo.from.cate"
                            clearable
                            filterable
                            placeholder="请选择分类"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in rtc_meeting_cate_enum"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="会议模式" prop="mode">
                        <el-select
                            v-model="tableInfo.from.mode"
                            clearable
                            filterable
                            placeholder="请选择模式"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in rtc_meeting_mode_enum"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="组织" prop="like_JmeetingOrgs_Jorg_name">
                        <el-input
                            v-model="tableInfo.from.like_JmeetingOrgs_Jorg_name"
                            clearable
                            placeholder="请输入组织"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="商务" prop="type">
                        <el-input
                            v-model="tableInfo.from.like_businessPerson"
                            clearable
                            placeholder="请输入"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="工程师" prop="engineer">
                        <el-input
                            v-model="tableInfo.from.like_engineer"
                            clearable
                            placeholder="请输入"
                            style="width: 240px"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="会议开始时间" prop="type" style="font-weight: 700">
                        <el-date-picker
                            v-model="tableInfo.from.startTimeUtc"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            range-separator="To"
                            start-placeholder="起始日期"
                            style="width: 240px"
                            type="daterange"
                            value-format="YYYY-MM-DD"
                        />
                    </el-form-item>
                </template>

                <!-- 搜索按钮区域 -->
                <el-row :gutter="20" justify="start" style="width: 100%; margin-top: 10px">
                    <el-col :span="12" style="padding-left: 50px">
                        <el-button
                            v-hasPermi="['rtc:rtc_meeting:query']"
                            icon="Search"
                            type="primary"
                            @click="tableInfo.search"
                        >搜索
                        </el-button
                        >
                        <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                        <el-button type="text" @click="isExpand = !isExpand">
                            {{ isExpand ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <arrow-up v-if="isExpand" />
                                <arrow-down v-else />
                            </el-icon>
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['rtc:rtc_meeting:add']"
                        color="#009dff"
                        icon="Plus"
                        plain
                        @click="handleAdd"
                    >新增
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['rtc:rtc_meeting:edit']"
                        :disabled="single"
                        color="#01c064"
                        icon="Edit"
                        plain
                        @click="handleUpdate"
                    >修改
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['rtc:rtc_meeting:remove']"
                        :disabled="multiple"
                        color="#ff5c00"
                        icon="Delete"
                        plain
                        @click="handleDelete"
                    >删除
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['rtc:rtc_meeting:export']"
                        :disabled="single"
                        color="#ff5c00"
                        icon="Export"
                        plain
                        @click="handleExport"
                    >导出签到表
                    </el-button>
                </el-col>
                <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
            </el-row>

            <el-table
                :data="tableInfo.list.value"
                height="450"
                row-key="id"
                style="margin-top: 20px"
                @selection-change="handleSelectionChange"
            >
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="ID" prop="id" />
                <el-table-column align="center" label="状态" prop="status">
                    <template #default="scope">
                        <dict-tag :options="rtc_meeting_status_enum" :value="scope.row.status.toString()" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="会议名称" prop="theme" />

                <el-table-column align="center" label="会议号" prop="meetingNumber" />
                <el-table-column align="center" label="主持人" prop="nickname" />
                <el-table-column align="center" label="开始时间" prop="startTime">
                    <template #default="scope">
                        <span>{{ timestampToTime(scope.row.startTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="结束时间" prop="endTime">
                    <template #default="scope">
                        <span>{{ timestampToTime(scope.row.endTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="会议时长" prop="city">
                    <template #default="scope">
                        <span>{{ getTimeDifference(scope.row.startTime, scope.row.endTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                    <template #default="scope">
                        <el-button
                            v-hasPermi="['rtc:rtc_meeting:edit']"
                            icon="Edit"
                            link
                            type="primary"
                            @click="handleUpdate(scope.row)"
                        >修改
                        </el-button>
                        <!--                        <el-button link type="primary" icon="Delete"-->
                        <!--                                   v-hasPermi="['rtc:rtc_meeting:edit']">静音-->
                        <!--                        </el-button>-->

                        <el-button
                            v-hasPermi="['rtc:rtc_meeting:remove']"
                            icon="Delete"
                            link
                            type="danger"
                            @click="handleDelete(scope.row)"
                        >删除
                        </el-button>
                        <el-button
                            v-if="scope.row.status === 0 || scope.row.status === 1 || scope.row.status === 2"
                            icon="Share"
                            link
                            type="warning"
                            @click="handleShare(scope.row)"
                        >分享
                        </el-button>
                        <!--                        <el-button-->
                        <!--                            v-if="scope.row.status === 1 || scope.row.status === 2"-->
                        <!--                            v-hasPermi="['rtc:rtc_meeting:qr']"-->
                        <!--                            icon="View"-->
                        <!--                            link-->
                        <!--                            type="warning"-->
                        <!--                            @click="handleQrCode(scope.row)"-->
                        <!--                        >二维码-->
                        <!--                        </el-button>-->
                    </template>
                </el-table-column>
            </el-table>

            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    v-model:page="tableInfo.pageParam.pageNum"
                    :total="tableInfo.total.value"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
    </div>
</template>
<script lang="ts">
export default {
    name: 'Rtc_meeting'
};
</script>
<script lang="ts" setup>
import { getCurrentInstance, ComponentInternalInstance, ref } from 'vue';

import { getTableInfo } from '@/minix/tables';
import router from '@/router';
import { getTimeDifference, timestampToTime } from '../../../utils/ruoyi';
import { deleteRtcMeeting } from '@/api/rtc/rtc_meeting';
import { RtcMeeting, RtcMeetingDetail } from '@/model/rtc_meeting';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { rtc_meeting_status_enum, rtc_meeting_cate_enum, rtc_meeting_mode_enum } =
    proxy!.useDict(
        'rtc_meeting_status_enum',
        'rtc_meeting_cate_enum',
        'rtc_meeting_mode_enum'
    );

const showSearch = ref(true);
const ids = ref<any[]>([]);
const selects = ref<RtcMeeting[]>([]);
const single = ref(true);
const multiple = ref(true);

const tableInfo = getTableInfo<RtcMeeting>('/evms/rtc_meeting/list', { sort: 'id desc' });
tableInfo.load();
console.log('tableInfo', tableInfo);

// 添加展开折叠控制
const isExpand = ref(false);

// 医院名称推荐选择

/** 重置按钮操作 */

// 多选框选中数据
const handleSelectionChange = (selection: RtcMeeting[]) => {
    ids.value = selection.map(item => item.id);
    selects.value = selection;
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
    tableInfo.reset();
    router.push('/rtc/rtc_meeting_info');
};

/** 修改按钮操作 */
const handleUpdate = (row: RtcMeeting) => {
    // tableInfo.reset();
    const _id = row.id || ids.value;
    router.push('/rtc/rtc_meeting_info/' + _id);
};

/** 删除按钮操作 */
const handleDelete = (row: RtcMeeting) => {
    const _ids = row?.id || ids.value.join(',');
    proxy!.$modal
        .confirm('是否确认删除编号为"' + _ids + '"的数据项？')
        .then(function() {
            return deleteRtcMeeting(_ids);
        })
        .then(res => {
            if (res.code === 200) {
                // getList();
                proxy!.$modal.msgSuccess('删除成功');
                // 重置列表
                tableInfo.reset();
            }
        })
        .catch(() => {
        });
};

// 分享
const handleShare = (row: RtcMeetingDetail) => {
    console.log('分享', row);

    // 检查是否存在 deviceLinkUrl 字段，如果存在则显示选择对话框
    if (row.deviceLinkUrl) {
        // 显示选择对话框 - 使用 BrainMed 链接
        proxy!.$modal.confirm('是否使用 link纯净版 链接分享？点击"确定"使用 link纯净版 链接，点击"取消"使用普通链接。')
            .then(() => {
                // 用户选择了 BrainMed 链接
                shareWithLinkType(row, 'link');
            })
            .catch(() => {
                // 用户选择了普通链接
                shareWithLinkType(row, 'brainmed');
            });
    } else {
        // 如果没有 deviceLinkUrl 字段，直接使用默认链接
        shareWithLinkType(row, 'linkUrl');
    }
};

// 根据链接类型分享
const shareWithLinkType = (row: RtcMeetingDetail, linkType: 'brainmed' | 'link' | 'linkUrl') => {
    let shareUrl = row.linkUrl || '';

    // 如果存在 deviceLinkUrl 字段，根据用户选择处理
    if (row.deviceLinkUrl) {
        const deviceLinkUrl = row.deviceLinkUrl;
        if (linkType === 'brainmed' && deviceLinkUrl.brainmed) {
            shareUrl = deviceLinkUrl.brainmed;
        } else if (linkType === 'link' && deviceLinkUrl.link) {
            shareUrl = deviceLinkUrl.link;
        }
    }

    const str = `
    会议主题：${row.theme}
    会议时间：${timestampToTime(row.startTime)}
    会议 ID：${row.meetingNumber}
    ${row.password ? `会议密码：${row.password}` : ''}
    会议链接：${shareUrl}
    `;

    // 复制到剪切板
    navigator.clipboard.writeText(str).then(
        function() {
            proxy!.$modal.msgSuccess('复制成功');
        },
        function() {
            proxy!.$modal.msgError('复制失败');
        }
    );
};

// // 二维码
// const handleQrCode = (row: RtcMeetingDetail) => {
//     console.log('二维码', row);
//
//     // 检查是否存在 deviceLinkUrl 字段，如果存在则显示选择对话框
//     if (row.deviceLinkUrl) {
//         // 显示选择对话框 - 使用 BrainMed 链接
//         proxy!.$modal.confirm('是否使用 BrainMed 链接生成二维码？点击"确定"使用 BrainMed 链接，点击"取消"使用普通链接。')
//             .then(() => {
//                 // 用户选择了 BrainMed 链接
//                 showQrCodeWithLinkType(row, 'brainmed');
//             })
//             .catch(() => {
//                 // 用户选择了普通链接
//                 showQrCodeWithLinkType(row, 'link');
//             });
//     } else {
//         // 如果没有 deviceLinkUrl 字段，直接使用默认二维码
//         showQrCodeWithLinkType(row, 'link');
//     }
// };

// 根据链接类型显示二维码
// const showQrCodeWithLinkType = (row: RtcMeetingDetail, linkType: 'brainmed' | 'link') => {
//     let qrCodeUrl = row.qrCode || '';
//
//     // 如果存在 deviceLinkUrl 字段，根据用户选择处理
//     if (row.deviceLinkUrl) {
//         const deviceLinkUrl = row.deviceLinkUrl;
//         if (linkType === 'brainmed' && deviceLinkUrl.brainmedQr) {
//             qrCodeUrl = deviceLinkUrl.brainmedQr;
//         } else if (linkType === 'link' && deviceLinkUrl.linkQr) {
//             qrCodeUrl = deviceLinkUrl.linkQr;
//         }
//     }
//
//     // 显示二维码弹窗 - 使用简单的alert显示二维码URL，实际项目中可能需要自定义弹窗组件
//     if (qrCodeUrl) {
//         // 创建一个新窗口显示二维码
//         const newWindow = window.open('', '_blank', 'width=400,height=400');
//         if (newWindow) {
//             newWindow.document.write(`
//                 <html>
//                     <head><title>会议二维码</title></head>
//                     <body style="display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0;">
//                         <img src="${qrCodeUrl}" alt="二维码" style="width: 300px; height: 300px;" />
//                     </body>
//                 </html>
//             `);
//             newWindow.document.close();
//         }
//     } else {
//         proxy!.$modal.msgWarning('二维码不存在');
//     }
// };

// 导出
const handleExport = async () => {
    const _id = selects.value[0].meetingNumber;

    window.open(location.origin + import.meta.env.VITE_APP_BASE_API + '/evms/rtc_meeting/exportAttendance?meetingNumber=' + _id);

};
// getList();
</script>
<style scoped>
.app-container {
    background: #f8f9fb;
}

/* 添加展开按钮样式 */
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

/* 调整表单项间距 */
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
