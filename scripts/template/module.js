import {
    createDaoType,
    createEdit,
    createList,
    createSearch,
    createSlots,
    getColumnName,
    typeEnum,
    javaTypeEnum
} from './createTemplate.js';

export function createPage(genTable) {
    if (!genTable || !genTable.columns) return '';
    let BusinessName = genTable.BusinessName || '';
    let businessName = genTable.businessName || '';
    genTable.columns.forEach(item => (item.columnName = getColumnName(item)));
    let isBetweenTime = genTable.columns.find(
        column => column.htmlType === 'datetime' && column.queryType === 'BETWEEN'
    );
    let pkField = 'id';
    let functionName = genTable.functionName;
    let hasButton = `${genTable.moduleName}:${businessName}:`;
    return `
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from "element-plus";
import { defineComponent, getCurrentInstance, ref, reactive,toRefs } from "vue";
import RightToolar from "@/components/RightToolbar/index.vue"
import ImagePreview from "@/components/ImagePreview/index.vue"
import DictTag from "@/components/DictTag/index.vue"
import FileUpload from "@/components/FileUpload/index.vue"
import Pagination from "@/components/Pagination/index.vue"
import { list${BusinessName}, get${BusinessName}, del${BusinessName}, add${BusinessName}, update${BusinessName},${genTable.className} } from "@/api/${genTable.moduleName}/${businessName}";
import moment from "moment"
import { queryFormat, parseTime } from '@/utils/ruoyi';
interface QueryParams extends Partial<${genTable.className}> {
    pageNum:number,
    pageSize:number,
    name:any
}
export default defineComponent({
    setup() {
        const { proxy } = getCurrentInstance()!
        ${genTable.dicts ? `const { ${genTable.dicts.replaceAll('\'', '')} } = proxy?.useDict(${genTable.dicts})!;` : ''}
        const state = reactive({
            ${businessName}List:[] as ${genTable.className}[],
            open:false,
            loading:true,
            showSearch:true,
            ids:[] as Array<string|number>,
            single:true,
            total:0,
            title:"",
            multiple:false,
            daterange:[] as string[],
            form: {} as unknown as ${genTable.className},
            queryParams:{
                pageNum: 1,
                pageSize: 10,
                name: undefined,
                ${genTable.columns.reduce((sub, column, index) => {
        let taps = '                ';
        if (column.isQuery == 1) {
            if (sub.length > 0) {
                sub += taps;
            }
            sub += `${column.javaField}:${javaTypeEnum[column.javaType] === 'string' ? '""' : 'null as unknown as number'},
`;
        }
        return sub;
    }, '')}
            } as QueryParams,
            rules: {
                ${genTable.columns.reduce((sub, column, index) => {
        if (column.isRequired == 1) {
            if (index !== 0 && sub.length > 0) {
                sub += ',\r\n                ';
            }
            sub += `${column.javaField}:[{ required: true, message: "${column.columnName}不能为空", trigger: ${column.htmlType == 'select' || column.htmlType == 'radio' ? '"change"' : '"blur"'}}]`;
        }
        if (index === genTable.columns.length - 1 && sub.length) {
            sub += 'as any[]';
        }
        return sub;
    }, '')}
            }
        })
        ${isBetweenTime ? 'const daterange = ref<any[]>([]);' : ''}
        function getList() {
            state.loading = true;
            ${
        isBetweenTime
            ? 'list' +
            BusinessName +
            '(proxy?.addDateRange(state.queryParams, state.daterange)).then((response)=>{'
            : `list${BusinessName}(queryFormat(state.queryParams)).then((response)=>{`
    }
                state.${businessName}List = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            })
        }
        function cancel() {
            state.open = false;
            reset();
        }
        // 重置区域
        function reset() {
            state.form = {} as unknown as ${genTable.className}
            proxy?.resetForm("${businessName}Ref");
        }
        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }
        function resetQuery() {
            ${isBetweenTime ? 'state.daterange = []' : ''}
            proxy?.resetForm('queryRef');
            handleQuery()
        }
        function handleSelectionChange(selection: ${genTable.className}[]) {
            state.ids = selection.map(item => item.${pkField});
            state.single  = selection.length !== 1;
            state.multiple = !selection.length;
        }
        function handleAdd() {
            reset();
            state.open = true;
            state.title = "添加${functionName}";
        }
        function handleUpdate(row?: ${genTable.className}) {
            reset();
            const _${pkField} = row ? row.${pkField} : state.ids
            get${BusinessName}(_${pkField}).then((response) => {
                state.form = response.data;
                ${genTable.columns.reduce((sub, column) => {
        if (column.htmlType === typeEnum.checkbox) {
            sub += `state.form.${column.javaField} = state.form.${column.javaField}.split(",");
`;
        }
        return sub;
    }, '')}
                state.open = true;
                state.title = "修改${functionName}";
            })
        }
        function submitForm() {
            (proxy?.$refs["${businessName}Ref"] as any).validate((valid: boolean) => {
                if (valid) {
                    ${genTable.columns.reduce((sub, column) => {
        if (column.htmlType === typeEnum.checkbox) {
            sub += `state.form.${column.javaField} = state.form.${column.javaField}.join(",");
`;
        }
        return sub;
    }, '')}
                    if (state.form.${pkField} != null) {
                        update${BusinessName}(state.form).then(() => {
                            proxy?.$modal.msgSuccess("修改成功");
                            state.open = false;
                            getList();
                        });
                    } else {
                        add${BusinessName}(state.form).then(() => {
                            proxy?.$modal.msgSuccess("新增成功");
                            state.open = false;
                            getList();
                        });
                    }
                }
            });
        }
        function handleDelete(row?: ${genTable.className}) {
            const _${pkField}s = row ? row.${pkField} : state.ids;
            proxy?.$modal.confirm("是否确认删除${functionName}编号为" + _${pkField}s + "的数据项？").then(()=> {
                return del${BusinessName}(_${pkField}s);
            }).then(() => {
                getList();
                proxy?.$modal.msgSuccess("删除成功");
            }).catch((e) => {console.log(e)});
        }
        function handleExport() {
            proxy?.download('${genTable.moduleName}/${businessName}/exportExcel', {
                ...queryFormat(state.queryParams)
            }, \`${businessName}_\${new Date().getTime()}}#.xlsx\`);
        }
        function triggerQuery(evt: KeyboardEvent | Event){
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery()
            }
        }
        const slots = {
            ${createSlots(genTable.columns, genTable.className) || ''},
            actionSlots(record:${genTable.className}){
                return <>
                    <ElButton
                        v-hasPermi={[['${hasButton}edit']]}
                        link
                        type="primary"
                        icon="Edit"
                        onClick={()=>handleUpdate(record)}
                    >
                        修改
                    </ElButton>
                    <ElButton
                        v-hasPermi={[['${hasButton}remove']]}
                        link
                        type="primary"
                        icon="Delete"
                        onClick={()=>handleDelete(record)}
                    >
                        删除
                    </ElButton>
                </>
            },
            modalFooter:{
                footer(){
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={submitForm}>确 定</ElButton>
                            <ElButton onClick={cancel}>取 消</ElButton>
                        </div>
                    )
                }
            }
        }
        getList();
        return () => (
            <div class="app-container">
                {/* 查询区域 */}
                <ElForm
                    v-show={state.showSearch}
                    ref="queryRef"
                    model={state.queryParams}
                    inline={true}
                >
                    ${createSearch(genTable.columns)}
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>搜索</ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>重置</ElButton>
                    </ElFormItem>
                </ElForm >
                {/* 编辑区域 */}

                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['${hasButton}add']]}
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={handleAdd}
                        >新增
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['${hasButton}edit']]}
                            type="success"
                            plain
                            icon="Edit"
                            disabled={state.single}
                            onClick={()=>handleUpdate()}
                        >修改
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['${hasButton}remove']]}
                            type="danger"
                            plain
                            icon="Delete"
                            disabled={state.multiple}
                            onClick={()=>handleDelete()}
                        >删除</ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['${hasButton}export']]}
                            type="warning"
                            plain
                            icon="Download"
                            onClick={handleExport}
                        >导出</ElButton>
                    </ElCol >
                    <RightToolar v-model:showSearch={state.showSearch} onQueryTable={getList}></RightToolar>
                </ElRow >
                {/* 表格区域 */}
                <ElTable v-loading={state.loading} data={state.${businessName}List} onSelection-change={handleSelectionChange}>
                    <ElTableColumn
                        type="selection"
                        width="55"
                        align="center"
                    />
                    ${createList(genTable.columns)}
                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    >
                    </ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total>0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
                <ElDialog
                    v-model={state.open}
                    title={state.title}
                    width="1000px"
                    appendToBody
                    v-slots={slots.modalFooter}
                >
                    <ElForm
                        ref="${businessName}Ref"
                        model={state.form}
                        rules={state.rules}
                        labelWidth="80px"
                    >
                    ${createEdit(genTable.columns)}
                    </ElForm>
                </ElDialog>
            </div >
        )
    }
})`;
}
