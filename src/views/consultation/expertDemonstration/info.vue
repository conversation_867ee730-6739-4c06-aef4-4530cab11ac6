<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import { DeviceUserItem } from '@/model/dms_device';
import { ConsultationHospital, ConsultationOrder } from '@/model/consultation';
import { listDmsDevice } from '@/api/evms/dms_device';
import type { FormInstance } from 'element-plus/es/components/form';
import type { TableInstance } from 'element-plus/es/components/table';
import { useRoute } from 'vue-router';
import type { BaseResponse } from '@/types/consultation';
import { addCnsOrderGroup, getOrderDetail, updateCnsOrderGroup } from '@/api/evms/consultation';
import { getTableInfo } from '@/minix/tables';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { cns_consultation_type_enum } = proxy!.useDict('cns_consultation_type_enum');
const { cns_consultation_status_enum } = proxy!.useDict('cns_consultation_status_enum');
const { cns_consultation_guide_type_enum } = proxy!.useDict('cns_consultation_guide_type_enum');
const formRef = ref<FormInstance>();
// 校验规则
const rules = {
    theme: [{ required: true, message: '请输入主题', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    organizerId: [{ required: true, message: '请选择发起人', trigger: 'change' }],
    startTime: [
        { required: true, message: '请选择预约时间', trigger: 'blur' },
        {
            validator: (rule: any, value: number) => {
                if (!value) return Promise.resolve();
                const selectedTime = value;
                const now = Date.now();
                if (!orderId && selectedTime < now) {
                    return Promise.reject('预约时间不能早于当前时间');
                }
                return Promise.resolve();
            },
            trigger: 'change'
        }
    ],
    guideType: [{ required: true, message: '请选择指导类型', trigger: 'blur' }],
    deviceId: [{ required: true, message: '请选择导管室', trigger: 'change' }],
    groupIds: [{ required: true, message: '请选择示教群组', trigger: 'change' }],
    expertId: [
        {
            validator: (rule: any, value: any) => {
                // 当类型为自选专家(S)时才进行必选校验
                if (state.cnsInfo.type === 'S' && !value) {
                    return Promise.reject('请选择自选专家');
                }
                return Promise.resolve();
            },
            trigger: 'change'
        }
    ]
};
const activeName = ref('first');

// 添加 DialogEnum 枚举
enum DialogEnum {
    FQR = 'FQR',
    ZXZJ = 'ZXZJ',
}

// 修改 state 定义，添加 zxzj
const state = reactive({
    cnsInfo: {
        type: 'P',
        status: 1,
        guideType: 'SD'
    } as ConsultationOrder,
    otherInfo: {
        fqr: '',
        zdjb: '',
        zxzj: '' // 添加自选专家字段
    },
    // 示教群组列表，用于标签显示
    groupList: [] as { id: number; name: string }[]
});

interface Leader {
    realname: string;
    id: string;
}

interface ApiResponse<T = any> extends BaseResponse<T> {
    code: number;
    data: T;
    msg?: string;
}

// 导管室列表
const querySearchDevice = async (queryString: string, cb: any) => {
    const results = await listDmsDevice({ q_like_name: queryString });
    const response = results as unknown as ApiResponse<{
        rows: Array<{ id: string; name: string; hospitalVo: { name: string } }>;
    }>;
    // call callback function to return suggestions
    if (response.code === 200) {
        console.log(response.data, '--1');
        cb(
            response.data && response.data.rows?.length > 0
                ? response.data.rows.map(item => {
                    return {
                        value: item.hospitalVo.name + '-' + item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};
const handleSelectDevice = (item: any) => {
    console.log(item);
    state.cnsInfo.deviceId = item.value;
    state.cnsInfo.device = item;
};
const selectable = (row: DeviceUserItem) => {
    console.log(row);
    return !state.cnsInfo.groupIds?.includes(Number(row.id)); // 转换为数字进行比较
};
// 返回
const handleBack = () => {
    state.cnsInfo = {} as ConsultationOrder;
    history.back();
};
// 保存
// 记录新增的医院id
let newOrderId: string = '';
const save = async () => {
    if (!formRef.value) return;
    if (newOrderId) {
        // 新增已经保存过了
        proxy!.$modal.msgWarning('已经保存了');
        return;
    }
    await formRef.value.validate(async (valid: boolean) => {
        console.log(valid, '---4');
        if (!valid) {
            return false;
        } else {
            const data: ConsultationOrder = {} as ConsultationOrder;
            data.description = state.cnsInfo.description;
            data.deviceId = state.cnsInfo.device?.id;
            data.groupIds = state.cnsInfo.groupIds;
            data.organizerId = state.cnsInfo.organizerId;
            data.startTime = state.cnsInfo.startTime;
            data.theme = state.cnsInfo.theme;
            data.status = state.cnsInfo.status;

            if (orderId) {
                // 编辑
                data.id = parseInt(state.cnsInfo.id as string);

                const res = (await updateCnsOrderGroup(data)) as unknown as ApiResponse;
                if (res.code === 200) {
                    proxy!.$modal.msgSuccess('保存成功');
                } else if (res.code === -3003) {
                    //对话确认框 提示用户是否继续
                    proxy!.$modal
                        .confirm(res.msg as string)
                        .then(() => {
                            // 用户点击确定，执行更新操作
                            data.isCancelConflict = 'T';
                            return updateCnsOrderGroup({ ...data, isCancelConflict: 'T' });
                        })
                        .then(res => {
                            if (res.code === 200) {
                                // getList();
                                proxy!.$modal.msgSuccess('保存成功');
                                // 重置列表
                            }
                        });
                } else {
                    proxy!.$modal.msgError(res.msg as string);
                }
            } else {
                // 新增
                const res = (await addCnsOrderGroup(data)) as unknown as ApiResponse<ConsultationOrder>;
                if (res.code === 200) {
                    newOrderId = String(res.data.id); // 确保转换为字符串
                    proxy!.$modal.msgSuccess('保存成功');
                    console.log(newOrderId, 'newOrderId');
                } else if (res.code === -3003) {
                    //对话确认框 提示用户是否继续
                    proxy!.$modal
                        .confirm(res.msg as string)
                        .then(() => {
                            // 用户点击确定，执行更新操作
                            return addCnsOrderGroup({ ...data, isCancelConflict: 'T' });
                        })
                        .then(res => {
                            if (res.code === 200) {
                                // getList();
                                proxy!.$modal.msgSuccess('保存成功');
                                // 重置列表
                            }
                        });
                } else {
                    proxy!.$modal.msgError(res.msg as string);
                }
            }
        }
    });
};
// 保存并返回
const saveAndBack = async () => {
    if (!formRef.value) return;
    if (newOrderId) {
        // 新增已经保存过了
        history.back();
        return;
    }
    await formRef.value.validate(async (valid: boolean) => {
        if (!valid) {
            return false;
        } else {
            const data: ConsultationOrder = {} as ConsultationOrder;
            data.description = state.cnsInfo.description;
            data.deviceId = state.cnsInfo.device?.id;
            data.groupIds = state.cnsInfo.groupIds;
            data.organizerId = state.cnsInfo.organizerId;
            data.startTime = state.cnsInfo.startTime;
            data.theme = state.cnsInfo.theme;
            data.status = state.cnsInfo.status;

            if (orderId) {
                data.id = parseInt(state.cnsInfo.id as string);

                // 编辑
                const res = (await updateCnsOrderGroup(data)) as unknown as ApiResponse;
                if (res.code === 200) {
                    proxy!.$modal.msgSuccess('保存成功');
                    history.back();
                } else if (res.code === -3003) {
                    //对话确认框 提示用户是否继续
                    proxy!.$modal
                        .confirm(res.msg as string)
                        .then(() => {
                            // 用户点击确定，执行更新操作
                            data.isCancelConflict = 'T';
                            return updateCnsOrderGroup({ ...data, isCancelConflict: 'T' });
                        })
                        .then(res => {
                            if (res.code === 200) {
                                // getList();
                                proxy!.$modal.msgSuccess('保存成功');
                                // 重置列表
                            }
                        });
                } else {
                    proxy!.$modal.msgError(res.msg as string);
                }
            } else {
                // 新增
                const res = (await addCnsOrderGroup(data)) as unknown as ApiResponse;
                if (res.code === 200) {
                    proxy!.$modal.msgSuccess('保存成功');
                    history.back();
                } else if (res.code === -3003) {
                    //对话确认框 提示用户是否继续
                    proxy!.$modal
                        .confirm(res.msg as string)
                        .then(() => {
                            // 用户点击确定，执行更新操作
                            return addCnsOrderGroup({ ...data, isCancelConflict: 'T' });
                        })
                        .then(res => {
                            if (res.code === 200) {
                                // getList();
                                proxy!.$modal.msgSuccess('保存成功');
                                // 重置列表
                            }
                        });
                } else {
                    proxy!.$modal.msgError(res.msg as string);
                }
            }
        }
    });
};

// 获取参数 分辨是新增还是编辑
const route = useRoute();
const orderId = route.params.id as string;
// 根据id 获取医院详情
const getOrderInfo = async (id: string) => {
    const res = (await getOrderDetail(id)) as unknown as ApiResponse<ConsultationOrder>;
    if (res.code === 200) {
        state.cnsInfo = res.data || ({} as ConsultationOrder);
        state.cnsInfo.organizerId = state.cnsInfo.organizer?.userid;
        state.otherInfo.fqr = state.cnsInfo.organizer?.realname || '';
        state.cnsInfo.deviceId = state.cnsInfo.device?.name;

        // 初始化群组列表用于标签显示
        state.groupList =
            state.cnsInfo.teachingGroups?.map(i => ({
                id: parseInt(i.id!),
                name: i.name || ''
            })) || [];

        // 保持原有字符串格式，用于兼容性
        state.otherInfo.zdjb = state.cnsInfo.teachingGroups?.map(i => i.name).join('、') || '';
        state.cnsInfo.groupIds = state.cnsInfo.teachingGroups?.map(i => parseInt(i.id!)) || [];
        console.log(state.cnsInfo);
    }
};
if (orderId) {
    // 获取医院详情
    getOrderInfo(orderId);
} else {
    // 新增医院
}

// 选择弹框
const tableInfoAdd = getTableInfo('/evms/doctor_user/list', {
    q_type: 0
});
const tableInfoAddGroup = getTableInfo('/cns/teaching_group/list');
const open = ref(false);
const openMore = ref(false);
const closeDialog = () => {
    open.value = false;
    openMore.value = false;
    tableInfoAdd.reset();
    tableInfoAddGroup.reset();
};
const handleClick = (type: DialogEnum) => {
    tableInfoAdd.load();
    open.value = true;
    currentSelectType.value = type; // 添加一个 ref 来跟踪当前选择类型
};
const handleClickGroup = () => {
    tableInfoAddGroup.load();
    openMore.value = true;
};
const idsForAdd = ref<DeviceUserItem[]>([]);
const idsForAddGroup = ref<ConsultationHospital[]>([]);
const handleSelectionChangeForAdd = (selection: ConsultationHospital[]) => {
    idsForAddGroup.value = selection;
};
const multipleTableRef = ref<TableInstance>();
const handleCurrentChange = (row: DeviceUserItem | undefined) => {
    console.log('触发这里');
    if (!row) return;
    if (
        ![state.cnsInfo.expertId, state.cnsInfo.organizerId]
            .concat(state.cnsInfo.userIds || [])
            .includes(row.id - 0)
    ) {
        console.log('1');
        idsForAdd.value = [row];
    } else {
        proxy!.$modal.msgWarning('该用户已经选择');

        multipleTableRef.value!.setCurrentRow(null);
    }
};
// 添加当前选择类型的 ref
const currentSelectType = ref<DialogEnum>(DialogEnum.FQR);

// 修改 submitForm 函数
const submitForm = () => {
    if (!idsForAdd.value.length) {
        proxy!.$modal.msgError('请选择用户');
        return;
    }

    const selectedUser = idsForAdd.value[0];

    if (currentSelectType.value === DialogEnum.FQR) {
        state.cnsInfo.organizerId = selectedUser.id;
        state.otherInfo.fqr = selectedUser.realname;
    }

    idsForAdd.value = [];
    open.value = false;
};
const submitFormAdd = () => {
    console.log(idsForAddGroup, 'idsforadd');
    if (!idsForAddGroup.value.length) {
        proxy!.$modal.msgError('请选择群组');
        return;
    }

    // 更新groupIds
    state.cnsInfo.groupIds = [
        ...(state.cnsInfo.groupIds || []),
        ...idsForAddGroup.value.map(i => parseInt(i.id!))
    ];

    // 更新群组列表用于标签显示
    const newGroups = idsForAddGroup.value.map(i => ({
        id: parseInt(i.id!),
        name: i.name || ''
    }));
    state.groupList = [...state.groupList, ...newGroups];

    // 保持原有字符串格式，用于兼容性
    state.otherInfo.zdjb = state.groupList.map(group => group.name).join('、');

    idsForAddGroup.value = [];
    openMore.value = false;
};
// 新增用户取消
const cancel = () => {
    open.value = false;
    openMore.value = false;
    idsForAdd.value = [];
    idsForAddGroup.value = [];
};

// 删除单个群组
const handleRemoveGroup = (group: { id: number; name: string }) => {
    // 从群组列表中移除
    state.groupList = state.groupList.filter(item => item.id !== group.id);

    // 从groupIds中移除
    state.cnsInfo.groupIds = state.cnsInfo.groupIds?.filter(id => id !== group.id) || [];

    // 更新字符串显示
    state.otherInfo.zdjb = state.groupList.map(g => g.name).join('、');
};

// 清空所有群组
const clearAllGroups = () => {
    state.groupList = [];
    state.cnsInfo.groupIds = [];
    state.otherInfo.zdjb = '';
};
</script>

<template>
    <div class="consultation_order_container">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" class="demo-tabs" type="card">
                <el-tab-pane label="基础信息" name="first">
                    <el-form ref="formRef" :model="state.cnsInfo" :rules="rules" label-position="top">
                        <el-row :gutter="10" class="form-con" justify="space-between">
                            <el-col :span="24">
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="主题" prop="theme" style="width: 100%">
                                            <el-input
                                                v-model="state.cnsInfo.theme"
                                                clearable
                                                maxlength="255"
                                                placeholder="请输入主题"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="进行状态" prop="status">
                                            <el-select
                                                v-model="state.cnsInfo.status"
                                                class="m-2"
                                                disabled
                                                placeholder=""
                                                size="large"
                                                value-key="value"
                                            >
                                                <el-option
                                                    v-for="dict in cns_consultation_status_enum"
                                                    :key="dict.value"
                                                    :label="dict.label"
                                                    :value="parseInt(dict.value)"
                                                />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="发起人" prop="organizerId">
                                            <el-input
                                                v-model="state.otherInfo.fqr"
                                                placeholder="点击选择发起人"
                                                style="width: 240px"
                                                @focus="() => handleClick(DialogEnum.FQR)"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="预约时间" prop="startTime">
                                            <el-date-picker
                                                v-model="state.cnsInfo.startTime"
                                                placeholder="请选择时间"
                                                type="datetime"
                                                value-format="x"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="示教群组(多选)" prop="groupIds">
                                            <div style="position: relative; width: 240px">
                                                <div
                                                    class="group-tags-container"
                                                    style="
                                                        width: 100%;
                                                        min-height: 40px;
                                                        background: #f8f9fb;
                                                        border-radius: 4px;
                                                        border: 1px solid #dcdfe6;
                                                        padding: 4px 8px;
                                                        display: flex;
                                                        flex-wrap: wrap;
                                                        align-items: center;
                                                        cursor: pointer;
                                                    "
                                                    @click="() => handleClickGroup()"
                                                >
                                                    <template v-if="state.groupList.length">
                                                        <el-tag
                                                            v-for="group in state.groupList"
                                                            :key="group.id"
                                                            closable
                                                            style="
                                                                margin: 2px 4px;
                                                                max-width: 120px;
                                                                overflow: hidden;
                                                                text-overflow: ellipsis;
                                                                white-space: nowrap;
                                                            "
                                                            @close.stop="handleRemoveGroup(group)"
                                                        >
                                                            {{ group.name }}
                                                        </el-tag>
                                                    </template>
                                                    <span v-else style="color: #909399; font-size: 14px"
                                                    >点击选择示教群组</span
                                                    >
                                                </div>
                                                <el-button
                                                    v-if="state.groupList.length"
                                                    type="text"
                                                    style="
                                                        position: absolute;
                                                        right: 5px;
                                                        top: 5px;
                                                        z-index: 1;
                                                        margin-right: 5px;
                                                        padding: 4px 8px;
                                                    "
                                                    @click.stop="clearAllGroups"
                                                >清空
                                                </el-button
                                                >
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="导管室" prop="deviceId">
                                            <el-autocomplete
                                                v-model="state.cnsInfo.deviceId"
                                                :fetch-suggestions="querySearchDevice"
                                                clearable
                                                placeholder="请选择导管室"
                                                style="width: 240px"
                                                @select="handleSelectDevice"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="20">
                                        <el-form-item label="主诉" prop="description">
                                            <el-input
                                                v-model="state.cnsInfo.description"
                                                maxlength="500"
                                                placeholder="请输入主诉"
                                                style="width: 70%; height: 111px"
                                                type="textarea"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                                <el-row :gutter="30">-->
                                <!--                                    <el-col :span="10">-->
                                <!--                                        <el-form-item label="自选专家(单选)" prop="expertId">-->
                                <!--                                            <el-input-->
                                <!--                                                v-model="state.otherInfo.zxzj"-->
                                <!--                                                :readonly="true"-->
                                <!--                                                placeholder="点击选择自选专家"-->
                                <!--                                                style="width: 240px"-->
                                <!--                                                @focus="() => handleClick(DialogEnum.ZXZJ)"-->
                                <!--                                            />-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                </el-row>-->
                            </el-col>
                            <el-col :span="24" style="height: 100px; margin-top: auto">
                                <el-row justify="end">
                                    <el-button @click="handleBack">返回</el-button>
                                    <el-button type="primary" @click="save">保存</el-button>
                                    <el-button type="primary" @click="saveAndBack">保存并返回</el-button>
                                </el-row>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </el-card>
        <!--    选择用户弹框 单选 -->
        <el-dialog
            v-model="open"
            append-to-body
            draggable
            modal-class="consultation_select_group"
            width="1156px"
            @closed="closeDialog"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">选择发起人</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAdd.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="8">
                            <el-form-item label="ID" prop="id">
                                <el-input
                                    v-model="tableInfoAdd.from.id"
                                    clearable
                                    placeholder="请输入ID"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAdd.from.like_realname"
                                    clearable
                                    placeholder="请输入"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-button icon="Search" type="primary" @click="tableInfoAdd.search"
                            >搜索
                            </el-button>
                            <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                        </el-col>
                    </el-row>
                    <!--            <el-form-item label="描述" prop="description">-->
                    <!--                <el-input-->
                    <!--                    v-model="tableInfo.from.description"-->
                    <!--                    placeholder="请输入描述"-->
                    <!--                    clearable-->
                    <!--                    style="width: 240px"-->
                    <!--                    @keyup.enter="handleQuery"-->
                    <!--                />-->
                    <!--            </el-form-item>-->
                </el-form>
                <el-table
                    ref="multipleTableRef"
                    :data="tableInfoAdd.list.value"
                    height="350"
                    highlight-current-row
                    style="width: 100%; margin-top: 20px"
                    @current-change="handleCurrentChange"
                >
                    <el-table-column label="ID" prop="id" width="140"></el-table-column>
                    <el-table-column label="用户姓名" prop="realname" width="120"></el-table-column>
                    <el-table-column label="手机号" prop="mobile"></el-table-column>
                    <el-table-column label="医院" prop="company"></el-table-column>
                    <el-table-column label="科室" prop="department"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAdd.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAdd.total.value > 0"
                        v-model:limit="tableInfoAdd.pageParam.pageSize"
                        v-model:page="tableInfoAdd.pageParam.pageNum"
                        :total="tableInfoAdd.total.value"
                        @pagination="tableInfoAdd.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!--    选择用户弹框 多选 -->
        <el-dialog
            v-model="openMore"
            append-to-body
            draggable
            modal-class="consultation_select_group"
            width="1156px"
            @closed="closeDialog"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">选择群组</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAddGroup.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="8">
                            <el-form-item label="群组名称" prop="id">
                                <el-input
                                    v-model="tableInfoAddGroup.from.like_name"
                                    clearable
                                    placeholder="请输入群组名称"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAddGroup.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="成员姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAddGroup.from.like_Jmembers_Juser_realname"
                                    clearable
                                    placeholder="请输入成员姓名"
                                    style="width: 240px"
                                    @keyup.enter="tableInfoAddGroup.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                label="成员身份"
                                prop="watermark"
                                style="width: 100%; background-color: #fff"
                            >
                                <el-radio-group v-model="tableInfoAddGroup.from.q_eq_Jmembers_leader_Boolean">
                                    <!-- works when >=2.6.0, recommended ✔️ not work when <2.6.0 ❌ -->
                                    <el-radio :label="true">专家</el-radio>
                                    <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
                                    <el-radio :label="false">成员</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-button icon="Search" type="primary" @click="tableInfoAddGroup.search"
                            >搜索
                            </el-button>
                            <el-button icon="Refresh" @click="tableInfoAddGroup.reset">重置</el-button>
                        </el-col>
                    </el-row>
                    <!--            <el-form-item label="描述" prop="description">-->
                    <!--                <el-input-->
                    <!--                    v-model="tableInfo.from.description"-->
                    <!--                    placeholder="请输入描述"-->
                    <!--                    clearable-->
                    <!--                    style="width: 240px"-->
                    <!--                    @keyup.enter="handleQuery"-->
                    <!--                />-->
                    <!--            </el-form-item>-->
                </el-form>

                <el-table
                    :data="tableInfoAddGroup.list.value"
                    height="350"
                    style="width: 100%; margin-top: 20px"
                    @selection-change="handleSelectionChangeForAdd"
                >
                    <el-table-column :selectable="selectable" type="selection" width="55"></el-table-column>
                    <el-table-column label="群组ID" prop="id" width="140"></el-table-column>
                    <el-table-column label="群组名称" prop="name" width="120"></el-table-column>
                    <el-table-column label="群组所属专家" prop="leaders">
                        <template #default="scope">
                            <span>{{
                                scope.row.leaders
                                    ? scope.row.leaders.map((i: Leader) => i.realname).join('、')
                                    : '--'
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="群组人数" prop="memberCount"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAddGroup.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAddGroup.total.value > 0"
                        v-model:limit="tableInfoAddGroup.pageParam.pageSize"
                        v-model:page="tableInfoAddGroup.pageParam.pageNum"
                        :total="tableInfoAddGroup.total.value"
                        @pagination="tableInfoAddGroup.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFormAdd">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<style lang="scss">
.consultation_select_group {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
<style lang="scss" scoped>
.consultation_order_container {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;

    :deep(.el-card) {
        height: 100%;
    }

    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }

    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }

    :deep(.el-tab-pane) {
        height: 100% !important;
        overflow-y: scroll;
    }

    :deep(.el-form) {
        height: 100% !important;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0 0 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }

    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }

    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }

    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }

    :deep(.el-input__inner) {
        height: 40px !important;
    }

    .form-con {
        height: 100%;
    }

    :deep(.el-form-item__content) {
        width: 100% !important;
    }

    :deep(.el-textarea) {
        width: 100% !important;
    }

    :deep(.el-row) {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}
</style>
