# 阿里云OSS直接上传配置指南

本指南将帮助您配置阿里云OSS SDK，实现前端直接上传文件到阿里云对象存储。

## 1. 安装依赖

```bash
npm install ali-oss
```

## 2. 配置方式

### 方式一：STS临时凭证（推荐）

通过后台接口获取STS临时凭证，这是生产环境推荐的安全方式：

```env
# 基础配置（可选，用于回退）
VITE_OSS_REGION=oss-cn-shanghai
VITE_OSS_BUCKET=your_bucket_name
VITE_OSS_ENDPOINT=https://oss-cn-shanghai.aliyuncs.com
```

后台需要提供以下接口：
- `GET /api/oss/sts-token` - 获取STS临时凭证

接口返回格式：
```json
{
  "code": 200,
  "data": {
    "accessKeyId": "STS.xxx",
    "accessKeySecret": "xxx",
    "securityToken": "xxx",
    "bucketName": "your-bucket",
    "region": "oss-cn-shanghai",
    "endpoint": "https://oss-cn-shanghai.aliyuncs.com",
    "expiration": "2024-01-01T12:00:00Z"
  }
}
```

### 方式二：静态配置（仅开发环境）

在项目根目录创建 `.env` 文件，添加以下配置：

```env
# 阿里云OSS配置
VITE_OSS_REGION=oss-cn-shanghai
VITE_OSS_ACCESS_KEY_ID=your_access_key_id
VITE_OSS_ACCESS_KEY_SECRET=your_access_key_secret
VITE_OSS_BUCKET=your_bucket_name
VITE_OSS_ENDPOINT=https://oss-cn-shanghai.aliyuncs.com
```

⚠️ **注意**：静态配置仅适用于开发环境，生产环境请使用STS临时凭证。

## 3. 获取阿里云AccessKey

1. 登录阿里云控制台
2. 进入 **访问控制 RAM** 服务
3. 创建用户并授予OSS相关权限
4. 获取AccessKey ID和AccessKey Secret

### 推荐权限策略

为了安全起见，建议创建专门的RAM用户，只授予必要的OSS权限：

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "oss:PutObject",
        "oss:GetObject",
        "oss:DeleteObject",
        "oss:ListObjects"
      ],
      "Resource": [
        "acs:oss:*:*:your-bucket-name/*"
      ]
    }
  ]
}
```

## 4. 创建OSS存储桶

1. 登录阿里云OSS控制台
2. 创建存储桶（Bucket）
3. 配置访问权限（建议设置为私有或公共读）
4. 配置跨域规则（CORS）

### CORS配置示例

```xml
<CORSRule>
  <AllowedOrigin>*</AllowedOrigin>
  <AllowedMethod>GET</AllowedMethod>
  <AllowedMethod>POST</AllowedMethod>
  <AllowedMethod>PUT</AllowedMethod>
  <AllowedMethod>DELETE</AllowedMethod>
  <AllowedMethod>HEAD</AllowedMethod>
  <AllowedHeader>*</AllowedHeader>
  <ExposeHeader>ETag</ExposeHeader>
  <ExposeHeader>x-oss-request-id</ExposeHeader>
  <MaxAgeSeconds>3600</MaxAgeSeconds>
</CORSRule>
```

## 5. 使用方法

### 基础用法

```vue
<template>
  <OssDirectUpload
    v-model="fileList"
    :multiple="true"
    :max-files="5"
    :max-size="10 * 1024 * 1024"
    accept="image/*,video/*,application/pdf"
    @upload-success="handleSuccess"
    @upload-error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue'
import OssDirectUpload from '@/components/OssDirectUpload.vue'

const fileList = ref([])

const handleSuccess = (files) => {
  console.log('上传成功:', files)
}

const handleError = (error) => {
  console.error('上传失败:', error)
}
</script>
```

### 高级配置

```vue
<template>
  <OssDirectUpload
    v-model="fileList"
    :upload-options="{
      folder: 'documents',
      onProgress: handleProgress
    }"
    @upload-success="handleSuccess"
  />
</template>

<script setup>
const handleProgress = (progress) => {
  console.log('上传进度:', progress + '%')
}
</script>
```

### 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | FileItem[] | [] | v-model绑定的文件列表 |
| multiple | boolean | false | 是否支持多选 |
| accept | string | - | 接受的文件类型 |
| maxSize | number | 10MB | 最大文件大小（字节） |
| maxFiles | number | 10 | 最大文件数量 |
| disabled | boolean | false | 是否禁用 |
| dragText | string | - | 拖拽区域提示文字 |
| acceptText | string | - | 文件类型提示文字 |
| uploadOptions | UploadOptions | {} | OSS上传配置 |
| autoUpload | boolean | true | 是否自动上传 |

### 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| upload-success | files: FileItem[] | 上传成功时触发 |
| upload-error | error: string | 上传失败时触发 |
| file-remove | file: FileItem, index: number | 移除文件时触发 |

### 直接使用OSS工具函数

```javascript
import { uploadFileToOSS, initOSSClient } from '@/utils/oss'

// 初始化OSS客户端（可选，会自动初始化）
initOSSClient({
  region: 'oss-cn-hangzhou',
  accessKeyId: 'your-key',
  accessKeySecret: 'your-secret',
  bucket: 'your-bucket'
})

// 上传单个文件
const file = document.querySelector('input[type="file"]').files[0]
try {
  const result = await uploadFileToOSS(file, {
    folder: 'uploads',
    onProgress: (progress) => {
      console.log('上传进度:', progress + '%')
    }
  })
  console.log('上传成功:', result.url)
} catch (error) {
  console.error('上传失败:', error)
}
```

## 6. 安全注意事项

### 生产环境建议

1. **使用STS临时凭证**：避免在前端暴露永久AccessKey
2. **服务端签名**：通过后端生成上传签名
3. **文件类型验证**：在前端和后端都进行文件类型检查
4. **大小限制**：设置合理的文件大小限制
5. **访问控制**：配置适当的存储桶权限

### STS临时凭证示例

```javascript
// 从后端获取临时凭证
const getSTSToken = async () => {
  const response = await fetch('/api/oss/sts-token')
  return response.json()
}

// 使用临时凭证初始化OSS
const stsToken = await getSTSToken()
initOSSClient({
  region: 'oss-cn-hangzhou',
  accessKeyId: stsToken.AccessKeyId,
  accessKeySecret: stsToken.AccessKeySecret,
  stsToken: stsToken.SecurityToken,
  bucket: 'your-bucket'
})
```

## 7. 常见问题

### Q: 上传失败，提示跨域错误
A: 检查OSS存储桶的CORS配置，确保允许来源域名和请求方法。

### Q: 上传成功但无法访问文件
A: 检查存储桶的访问权限设置，确保文件可以被公开访问。

### Q: 如何实现断点续传？
A: 使用OSS的分片上传功能，可以通过 `multipartUpload` 方法实现。

### Q: 如何限制上传文件的类型？
A: 在组件的 `accept` 属性中指定允许的文件类型，同时在 `beforeUpload` 回调中进行验证。

## 8. 性能优化

1. **启用CDN加速**：配置阿里云CDN加速文件访问
2. **图片处理**：使用OSS的图片处理功能自动压缩和格式转换
3. **分片上传**：对于大文件使用分片上传提高成功率
4. **并发控制**：限制同时上传的文件数量

## 9. 监控和日志

1. **上传统计**：记录上传成功率和失败原因
2. **性能监控**：监控上传速度和响应时间
3. **错误日志**：记录详细的错误信息用于排查问题

通过以上配置，您就可以在项目中使用阿里云OSS进行文件的直接上传了。
