<script setup lang="ts">
import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useRoute } from 'vue-router';
import { DmsHospitalInfo } from '@/model/dms_hospital';
import { addDmsHospital, getDmsHospital, updateDmsHospital, listHospital } from '@/api/evms/dms_hospital';
import { cityList } from '@/assets/constant/city';
// 医院类型字典
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_dms_hospital_type } = proxy!.useDict('evms_dms_hospital_type');
// 表单校验规则
const rules = reactive({
    name: [{ required: true, message: '请输入医院名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择医院类型', trigger: 'change' }],
    city: [{ required: true, message: '请选择城市', trigger: 'change' }],
    area: [{ required: true, message: '请选择区域', trigger: 'change' }],
    province: [{ required: true, message: '请选择省份', trigger: 'change' }],
    description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    areaArray: [{ type: 'array', required: true, message: '请选择医院位置', trigger: 'blur' }]
});
const route = useRoute();
const hospitalId = parseInt(route.params.id as string);
// 记录新增的医院id
let newHospitalId = -1;
const formRef = ref<FormInstance>();
const activeName = ref('first');
let state = reactive({
    formData: {} as DmsHospitalInfo
});

// 根据id 获取医院详情
const getHospitalInfo = async (id: number) => {
    const res = await getDmsHospital(id);
    if (res.code === 200) {
        state.formData = res.data || ({} as DmsHospitalInfo);
        if (state.formData.area && state.formData.city && state.formData.province) {
            state.formData.areaArray = [state.formData.area, state.formData.province, state.formData.city];
        }
        console.log(state.formData);
    }
};
if (hospitalId) {
    // 获取医院详情
    getHospitalInfo(Number(hospitalId));
} else {
    // 新增医院
}
// 取消
const cancel = () => {
    state.formData = {} as DmsHospitalInfo;
    history.back();
};
// 保存
const save = async () => {
    console.log(formRef.value, '---3');
    if (!formRef.value) return;
    if (newHospitalId > -1) {
        // 新增已经保存过了
        proxy!.$modal.msgWarning('已经保存了');
        return;
    }
    await formRef.value.validate(async (valid: boolean) => {
        console.log(valid, '---4');
        if (!valid) {
            return false;
        } else {
            const data = { ...state.formData };
            delete data.areaArray;
            if (hospitalId) {
                // 编辑
                const res = await updateDmsHospital(data);
                if (res.code === 200) {
                    proxy!.$modal.msgSuccess('保存成功');
                }
            } else {
                // 新增
                const res = await addDmsHospital(data);
                if (res.code === 200) {
                    newHospitalId = (res.data as DmsHospitalInfo).id!;
                    proxy!.$modal.msgSuccess('保存成功');
                }
            }
        }
    });
};
// 保存并返回
const saveAndBack = async () => {
    if (!formRef.value) return;
    if (newHospitalId > -1) {
        // 新增已经保存过了
        history.back();
        return;
    }
    await formRef.value.validate(async (valid: boolean) => {
        if (!valid) {
            return false;
        } else {
            const data = { ...state.formData };
            delete data.areaArray;
            if (hospitalId) {
                // 编辑
                const res = await updateDmsHospital(data);
                if (res.code === 200) {
                    ElMessage.success('保存成功');
                    history.back();
                }
            } else {
                // 新增
                const res = await addDmsHospital(data);
                if (res.code === 200) {
                    ElMessage.success('保存成功');
                    history.back();
                }
            }
        }
    });
};

const handleChange = (value: any) => {
    console.log(value);
    state.formData.area = value[0];
    state.formData.city = value[2];
    state.formData.province = value[1];
    console.log(state.formData);
};
// 医院名称推荐选择
type HopitalItem = {
    value: string;
    id: number;
};
const handleSelect = (item: HopitalItem) => {
    console.log(item);
    state.formData.name = item.value;
};
// 获取医院名称搜索结果

const querySearch = async (queryString: string, cb: any) => {
    const results = await listHospital({ keywords: queryString });
    // call callback function to return suggestions
    if (results.code === 1) {
        console.log(results.list, '--1');
        cb(
            results?.list && results.list.length > 0
                ? results.list?.map((item: any) => {
                    return {
                        value: item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};
</script>

<template>
    <div class="dms_hospital_container">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" type="card" class="demo-tabs">
                <el-tab-pane label="基础信息" name="first">
                    <el-form ref="formRef" :model="state.formData" label-position="top" :rules="rules">
                        <el-row class="form-con" justify="space-between">
                            <el-col :span="24">
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="医院名称" prop="name">
                                            <el-autocomplete
                                                v-model="state.formData.name"
                                                :fetch-suggestions="querySearch"
                                                clearable
                                                placeholder="请输入医院名称"
                                                @select="handleSelect"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="医院英文名称" prop="enName">
                                            <el-input
                                                v-model="state.formData.enName"
                                                clearable
                                                placeholder="请输入医院英文名称"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="30">
                                    <el-col :span="10">
                                        <el-form-item label="医院类型" prop="type">
                                            <el-select
                                                v-model="state.formData.type"
                                                value-key="label"
                                                class="m-2"
                                                placeholder="请选择医院类型"
                                                size="large"
                                            >
                                                <el-option
                                                    v-for="dict in evms_dms_hospital_type"
                                                    :key="dict.value"
                                                    :label="dict.label"
                                                    :value="parseInt(dict.value)"
                                                />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item label="选择位置">
                                            <el-cascader
                                                v-model="state.formData.areaArray"
                                                :options="cityList"
                                                :props="{
                                                    value: 'name',
                                                    label: 'name',
                                                    children: 'children',
                                                }"
                                                @change="handleChange"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row style="width: 100%">
                                    <el-col :span="24">
                                        <el-form-item label="描述">
                                            <el-input
                                                v-model="state.formData.description"
                                                placeholder="请输入描述"
                                                type="textarea"
                                                style="width: 85%; height: 111px"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-col>
                            <el-col :span="24" style="height: 100px; margin-top: auto">
                                <el-row justify="end">
                                    <el-button @click="cancel">返回</el-button>
                                    <el-button type="primary" @click="save">保存</el-button>
                                    <el-button type="primary" @click="saveAndBack">保存并返回</el-button>
                                </el-row>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<style scoped lang="scss">
.dms_hospital_container {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;
    :deep(.el-card) {
        height: 100%;
    }
    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }
    :deep(.el-tabs) {
        height: 100%;
    }
    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }
    :deep(.el-tab-pane) {
        height: 100% !important;
        overflow-y: scroll;
    }

    :deep(.el-form) {
        height: 100% !important;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0 0 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }
    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }
    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }
    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }
    :deep(.el-input__inner) {
        height: 40px !important;
    }
    .form-con {
        height: 100%;
    }
    :deep(.el-autocomplete) {
        width: 100%;
    }
}
</style>
