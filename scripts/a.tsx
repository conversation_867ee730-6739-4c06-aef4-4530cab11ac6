import {
    ElButton,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { defineComponent, getCurrentInstance, reactive } from 'vue';
import RightToolar from '@/components/RightToolbar/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import { addOrdert, delOrdert, EvmsCousultOrderDetail, getOrdert, listOrdert, updateOrdert } from '@/api/evms/ordert';
import moment from 'moment';
import { queryFormat } from '@/utils/ruoyi';

interface QueryParams extends Partial<EvmsCousultOrderDetail> {
    pageNum: number;
    pageSize: number;
    name: any;
}

export default defineComponent({
    setup() {
        const { proxy } = getCurrentInstance()!;

        const state = reactive({
            ordertList: [] as EvmsCousultOrderDetail[],
            open: false,
            loading: true,
            showSearch: true,
            ids: [] as Array<string | number>,
            single: true,
            total: 0,
            title: '',
            multiple: false,
            daterange: [] as string[],
            form: {} as unknown as EvmsCousultOrderDetail,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: undefined,
                orderId: null as unknown as number,
                hospitalId: null as unknown as number,
                deviceId: null as unknown as number,
                expertId: null as unknown as number,
                description: '',
                meetingId: null as unknown as number,
                cousultType: null as unknown as number,
                surgicalTime: '',
                surgicalExpectEndTime: '',
                surgicalType: '',
                mainDescription: '',
                userId: null as unknown as number,
                patientId: null as unknown as number
            } as QueryParams,
            rules: {
                hospitalId: [{ required: true, message: '医院id不能为空', trigger: 'blur' }],
                deviceId: [{ required: true, message: '设备id不能为空', trigger: 'blur' }],
                cousultType: [
                    {
                        required: true,
                        message: 'TODO:会诊类型 1-EMER_TREAT-急诊 2-SELECT_TIME-择期不能为空',
                        trigger: 'change'
                    }
                ],
                surgicalTime: [{ required: true, message: '手术时间不能为空', trigger: 'blur' }],
                surgicalExpectEndTime: [
                    { required: true, message: '手术预期结束时间不能为空', trigger: 'blur' }
                ],
                createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }] as any[]
            }
        });

        function getList() {
            state.loading = true;
            listOrdert(queryFormat(state.queryParams)).then(response => {
                state.ordertList = response.data.rows;
                state.total = response.data.total;
                state.loading = false;
            });
        }

        function cancel() {
            state.open = false;
            reset();
        }

        // 重置区域
        function reset() {
            state.form = {} as unknown as EvmsCousultOrderDetail;
            proxy?.resetForm('ordertRef');
        }

        function handleQuery() {
            state.queryParams.pageNum = 1;
            getList();
        }

        function resetQuery() {
            proxy?.resetForm('queryRef');
            handleQuery();
        }

        function handleSelectionChange(selection: EvmsCousultOrderDetail[]) {
            state.ids = selection.map(item => item.id);
            state.single = selection.length !== 1;
            state.multiple = !selection.length;
        }

        function handleAdd() {
            reset();
            state.open = true;
            state.title = '添加会诊订单';
        }

        function handleUpdate(row?: EvmsCousultOrderDetail) {
            reset();
            const _id = row ? row.id : state.ids;
            getOrdert(_id).then(response => {
                state.form = response.data;

                state.open = true;
                state.title = '修改会诊订单';
            });
        }

        function submitForm() {
            (proxy?.$refs['ordertRef'] as any).validate((valid: boolean) => {
                if (valid) {
                    if (state.form.id != null) {
                        updateOrdert(state.form).then(() => {
                            proxy?.$modal.msgSuccess('修改成功');
                            state.open = false;
                            getList();
                        });
                    } else {
                        addOrdert(state.form).then(() => {
                            proxy?.$modal.msgSuccess('新增成功');
                            state.open = false;
                            getList();
                        });
                    }
                }
            });
        }

        function handleDelete(row?: EvmsCousultOrderDetail) {
            const _ids = row ? row.id : state.ids;
            proxy?.$modal
                .confirm('是否确认删除会诊订单编号为' + _ids + '的数据项？')
                .then(() => {
                    return delOrdert(_ids);
                })
                .then(() => {
                    getList();
                    proxy?.$modal.msgSuccess('删除成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        function handleExport() {
            proxy?.download(
                'evms/ordert/exportExcel',
                {
                    ...queryFormat(state.queryParams)
                },
                `ordert_${new Date().getTime()}}#.xlsx`
            );
        }

        function triggerQuery(evt: KeyboardEvent | Event) {
            if ((evt as KeyboardEvent).key === 'Enter') {
                handleQuery();
            }
        }

        const slots = {
            column: {
                surgicalTime({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <span>
                            {record.surgicalTime ? moment(record.surgicalTime).format('YYYY-MM-DD') : '--'}
                        </span>
                    );
                },
                surgicalExpectEndTime({ row: record }: { row: EvmsCousultOrderDetail }) {
                    return (
                        <span>
                            {record.surgicalExpectEndTime
                                ? moment(record.surgicalExpectEndTime).format('YYYY-MM-DD')
                                : '--'}
                        </span>
                    );
                }
            },
            actionSlots(record: EvmsCousultOrderDetail) {
                return (
                    <>
                        <ElButton
                            v-hasPermi={[['evms:ordert:edit']]}
                            link
                            type="primary"
                            icon="Edit"
                            onClick={() => handleUpdate(record)}
                        >
                            修改
                        </ElButton>
                        <ElButton
                            v-hasPermi={[['evms:ordert:remove']]}
                            link
                            type="primary"
                            icon="Delete"
                            onClick={() => handleDelete(record)}
                        >
                            删除
                        </ElButton>
                    </>
                );
            },
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={submitForm}>
                                确 定
                            </ElButton>
                            <ElButton onClick={cancel}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };
        getList();
        return () => (
            <div class="app-container">
                {/* 查询区域 */}
                <ElForm v-show={state.showSearch} ref="queryRef" model={state.queryParams} inline={true}>
                    <ElFormItem label="订单id" prop="orderId">
                        <ElInput
                            v-model={state.queryParams.orderId}
                            placeholder="请输入订单id"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="医院id" prop="hospitalId">
                        <ElInput
                            v-model={state.queryParams.hospitalId}
                            placeholder="请输入医院id"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="设备id" prop="deviceId">
                        <ElInput
                            v-model={state.queryParams.deviceId}
                            placeholder="请输入设备id"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="专家id" prop="expertId">
                        <ElInput
                            v-model={state.queryParams.expertId}
                            placeholder="请输入专家id"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="备注" prop="description">
                        <ElInput
                            v-model={state.queryParams.description}
                            placeholder="请输入备注"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="会议" prop="meetingId">
                        <ElInput
                            v-model={state.queryParams.meetingId}
                            placeholder="请输入会议"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="TODO:会诊类型 1-EMER_TREAT-急诊 2-SELECT_TIME-择期" prop="cousultType">
                        <ElSelect
                            v-model={state.queryParams.cousultType}
                            placeHolder="请选择TODO:会诊类型 1-EMER_TREAT-急诊 2-SELECT_TIME-择期"
                            clearable
                        >
                            <ElOption label="请选择字典生成" value=""></ElOption>
                        </ElSelect>
                    </ElFormItem>
                    <ElFormItem label="手术时间" prop="surgicalTime">
                        <ElDatePicker
                            clearable
                            v-model={state.queryParams.surgicalTime}
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="请选择手术时间"
                        />
                    </ElFormItem>
                    <ElFormItem label="手术预期结束时间" prop="surgicalExpectEndTime">
                        <ElDatePicker
                            clearable
                            v-model={state.queryParams.surgicalExpectEndTime}
                            type="date"
                            value-format="YYYY-MM-DD"
                            placeholder="请选择手术预期结束时间"
                        />
                    </ElFormItem>
                    <ElFormItem label="手术类型" prop="surgicalType">
                        <ElSelect
                            v-model={state.queryParams.surgicalType}
                            placeHolder="请选择手术类型"
                            clearable
                        >
                            <ElOption label="请选择字典生成" value=""></ElOption>
                        </ElSelect>
                    </ElFormItem>
                    <ElFormItem label="用户id" prop="userId">
                        <ElInput
                            v-model={state.queryParams.userId}
                            placeholder="请输入用户id"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem label="患者id" prop="patientId">
                        <ElInput
                            v-model={state.queryParams.patientId}
                            placeholder="请输入患者id"
                            clearable
                            style="width: 240px"
                            onKeydown={triggerQuery}
                        />
                    </ElFormItem>
                    <ElFormItem>
                        <ElButton type="primary" icon="Search" onClick={handleQuery}>
                            搜索
                        </ElButton>
                        <ElButton icon="Refresh" onClick={resetQuery}>
                            重置
                        </ElButton>
                    </ElFormItem>
                </ElForm>
                {/* 编辑区域 */}

                <ElRow gutter={10} class="mb8">
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['evms:ordert:add']]}
                            type="primary"
                            plain
                            icon="Plus"
                            onClick={handleAdd}
                        >
                            新增
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['evms:ordert:edit']]}
                            type="success"
                            plain
                            icon="Edit"
                            disabled={state.single}
                            onClick={() => handleUpdate()}
                        >
                            修改
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['evms:ordert:remove']]}
                            type="danger"
                            plain
                            icon="Delete"
                            disabled={state.multiple}
                            onClick={() => handleDelete()}
                        >
                            删除
                        </ElButton>
                    </ElCol>
                    <ElCol span={1.5}>
                        <ElButton
                            v-hasPermi={[['evms:ordert:export']]}
                            type="warning"
                            plain
                            icon="Download"
                            onClick={handleExport}
                        >
                            导出
                        </ElButton>
                    </ElCol>
                    <RightToolar v-model:showSearch={state.showSearch} onQueryTable={getList}></RightToolar>
                </ElRow>
                {/* 表格区域 */}
                <ElTable
                    v-loading={state.loading}
                    data={state.ordertList}
                    onSelection-change={handleSelectionChange}
                >
                    <ElTableColumn type="selection" width="55" align="center" />
                    <ElTableColumn label="订单id" align="center" prop="orderId" />
                    <ElTableColumn label="医院id" align="center" prop="hospitalId" />
                    <ElTableColumn label="设备id" align="center" prop="deviceId" />
                    <ElTableColumn label="专家id" align="center" prop="expertId" />
                    <ElTableColumn label="备注" align="center" prop="description" />
                    <ElTableColumn label="会议" align="center" prop="meetingId" />
                    <ElTableColumn
                        label="TODO:会诊类型 1-EMER_TREAT-急诊 2-SELECT_TIME-择期"
                        align="center"
                        prop="cousultType"
                    />
                    <ElTableColumn
                        label="手术时间"
                        align="center"
                        prop="surgicalTime"
                        width="180"
                        v-slots={slots.column.surgicalTime}
                    />
                    <ElTableColumn
                        label="手术预期结束时间"
                        align="center"
                        prop="surgicalExpectEndTime"
                        width="180"
                        v-slots={slots.column.surgicalExpectEndTime}
                    />
                    <ElTableColumn label="手术类型" align="center" prop="surgicalType" />
                    <ElTableColumn label="主述" align="center" prop="mainDescription" />
                    <ElTableColumn label="用户id" align="center" prop="userId" />
                    <ElTableColumn label="患者id" align="center" prop="patientId" />
                    <ElTableColumn
                        label="操作"
                        align="center"
                        class-name="small-padding fixed-width"
                        v-slots={slots.actionSlots}
                    ></ElTableColumn>
                </ElTable>
                {/* 页脚区域 */}
                <Pagination
                    v-show={state.total > 0}
                    v-model:page={state.queryParams.pageNum}
                    v-model:limit={state.queryParams.pageSize}
                    total={state.total}
                    onPagination={getList}
                />
                {/* 添加model 区域 */}
                <ElDialog
                    v-model={state.open}
                    title={state.title}
                    width="1000px"
                    appendToBody
                    v-slots={slots.modalFooter}
                >
                    <ElForm ref="ordertRef" model={state.form} rules={state.rules} labelWidth="80px">
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="订单id" prop="orderId">
                                    <ElInput v-model={state.form.orderId} placeholder="请输入订单id" />
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="医院id" prop="hospitalId">
                                    <ElInput v-model={state.form.hospitalId} placeholder="请输入医院id" />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="设备id" prop="deviceId">
                                    <ElInput v-model={state.form.deviceId} placeholder="请输入设备id" />
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="专家id" prop="expertId">
                                    <ElInput v-model={state.form.expertId} placeholder="请输入专家id" />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="备注" prop="description">
                                    <ElInput v-model={state.form.description} placeholder="请输入备注" />
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="会议" prop="meetingId">
                                    <ElInput v-model={state.form.meetingId} placeholder="请输入会议" />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem
                                    labelWidth="150px"
                                    label="TODO:会诊类型 1-EMER_TREAT-急诊 2-SELECT_TIME-择期"
                                    prop="cousultType"
                                >
                                    <ElSelect
                                        v-model={state.form.cousultType}
                                        placeHolder="请选择TODO:会诊类型 1-EMER_TREAT-急诊 2-SELECT_TIME-择期"
                                    >
                                        <ElOption label="请选择字典生成" value=""></ElOption>
                                    </ElSelect>
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="手术时间" prop="surgicalTime">
                                    <ElDatePicker
                                        clearable
                                        v-model={state.form.surgicalTime}
                                        type="date"
                                        value-format="YYYY-MM-DD"
                                        placeholder="请选择手术时间"
                                    />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem
                                    labelWidth="150px"
                                    label="手术预期结束时间"
                                    prop="surgicalExpectEndTime"
                                >
                                    <ElDatePicker
                                        clearable
                                        v-model={state.form.surgicalExpectEndTime}
                                        type="date"
                                        value-format="YYYY-MM-DD"
                                        placeholder="请选择手术预期结束时间"
                                    />
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="手术类型" prop="surgicalType">
                                    <ElSelect v-model={state.form.surgicalType} placeHolder="请选择手术类型">
                                        <ElOption label="请选择字典生成" value=""></ElOption>
                                    </ElSelect>
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="主述" prop="mainDescription">
                                    <ElInput
                                        v-model={state.form.mainDescription}
                                        placeholder="请输入主述"
                                        type="textarea"
                                    />
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="用户id" prop="userId">
                                    <ElInput v-model={state.form.userId} placeholder="请输入用户id" />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                        <ElRow>
                            <ElCol span={12}>
                                <ElFormItem labelWidth="150px" label="患者id" prop="patientId">
                                    <ElInput v-model={state.form.patientId} placeholder="请输入患者id" />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                    </ElForm>
                </ElDialog>
            </div>
        );
    }
});
