import OSS from 'ali-oss';
import { getOSSSTSToken } from '@/api/oss/sts';

// 阿里云OSS配置接口
export interface OSSConfig {
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
    bucket: string;
    endpoint?: string;
    secure?: boolean;
    timeout?: number;
    stsToken?: string; // STS临时凭证
}

// STS临时凭证响应接口（与您的后台接口保持一致）
export interface STSCredentials {
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    bucket: string; // 注意：您的接口返回的是 bucket
    endpoint: string;
    expiration: string; // 过期时间格式：2025-06-26 03:33:55
    requestId?: string;
}

// 默认OSS配置（从环境变量获取基础配置，仅用于回退）
const defaultConfig = {
    region: 'oss-cn-shenzhen',
    accessKeyId: '',
    accessKeySecret: '',
    bucket: 'evms-resource',
    endpoint: 'oss-cn-shenzhen.aliyuncs.com',
    secure: true,
    timeout: 60000 // 60秒超时
};

// OSS客户端实例
let ossClient: OSS | null = null;
let stsCredentials: STSCredentials | null = null;
let credentialsExpireTime: number = 0;

/**
 * 从后台获取STS临时凭证
 */
export async function getSTSCredentials(): Promise<STSCredentials> {
    try {
        const response = await getOSSSTSToken();

        // 适配您的接口返回格式：code: 1 表示成功
        if (response.code === 1 && response.data) {
            const credentials: STSCredentials = {
                accessKeyId: response.data.accessKeyId,
                accessKeySecret: response.data.accessKeySecret,
                securityToken: response.data.securityToken,
                bucket: response.data.bucket, // 注意：您的接口返回的是 bucket
                endpoint: response.data.endpoint,
                expiration: response.data.expiration,
                requestId: response.data.requestId
            };

            // 缓存凭证和过期时间
            stsCredentials = credentials;
            if (credentials.expiration) {
                // 您的时间格式：2025-06-26 03:33:55，需要转换为时间戳
                credentialsExpireTime = new Date(credentials.expiration).getTime();
            } else {
                // 如果没有过期时间，默认1小时后过期
                credentialsExpireTime = Date.now() + 60 * 60 * 1000;
            }

            return credentials;
        } else {
            throw new Error(response.message || '获取STS凭证失败');
        }
    } catch (error) {
        console.error('获取STS凭证失败:', error);
        throw new Error(`获取STS凭证失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 检查STS凭证是否过期
 */
function isCredentialsExpired(): boolean {
    if (!stsCredentials || !credentialsExpireTime) {
        return true;
    }
    // 提前5分钟刷新凭证
    return Date.now() > (credentialsExpireTime - 5 * 60 * 1000);
}

/**
 * 获取有效的STS凭证（自动刷新）
 */
async function getValidCredentials(): Promise<STSCredentials> {
    if (isCredentialsExpired()) {
        console.log('STS凭证已过期或不存在，正在获取新凭证...');
        await getSTSCredentials();
    }
    return stsCredentials!;
}

/**
 * 测试STS凭证和OSS连接
 */
export async function testOSSConnection(): Promise<boolean> {
    try {
        const client = await getOSSClient();

        // 尝试列出存储桶中的对象（只获取1个，用于测试连接）
        const result = await client.list({
            'max-keys': 1
        });

        console.log('OSS连接测试成功:', result);
        return true;
    } catch (error) {
        console.error('OSS连接测试失败:', error);
        return false;
    }
}

/**
 * 使用STS凭证初始化OSS客户端
 * @param credentials STS凭证
 */
export function initOSSClientWithSTS(credentials: STSCredentials): OSS {
    // 从endpoint提取region，格式：oss-cn-shenzhen.aliyuncs.com
    const region = credentials.endpoint.includes('aliyuncs.com')
        ? credentials.endpoint.split('.')[0]
        : 'oss-cn-shenzhen';

    const config: OSSConfig = {
        region: region,
        accessKeyId: credentials.accessKeyId,
        accessKeySecret: credentials.accessKeySecret,
        stsToken: credentials.securityToken,
        bucket: credentials.bucket,
        // 注意：不要添加 https:// 前缀，OSS SDK会自动处理
        endpoint: credentials.endpoint,
        secure: true,
        timeout: 60000
    };

    console.log('初始化OSS客户端配置:', {
        region: config.region,
        bucket: config.bucket,
        endpoint: config.endpoint,
        hasToken: !!config.stsToken
    });

    ossClient = new OSS(config);
    return ossClient;
}

/**
 * 初始化OSS客户端（兼容旧版本，优先使用STS凭证）
 * @param config OSS配置
 */
export async function initOSSClient(config?: Partial<OSSConfig>): Promise<OSS> {
    try {
        // 优先尝试使用STS凭证
        const credentials = await getValidCredentials();
        return initOSSClientWithSTS(credentials);
    } catch (error) {
        console.warn('无法获取STS凭证，尝试使用静态配置:', error);

        // 如果STS凭证获取失败，回退到静态配置
        const finalConfig = { ...defaultConfig, ...config };

        if (!finalConfig.accessKeyId || !finalConfig.accessKeySecret || !finalConfig.bucket) {
            throw new Error('OSS配置不完整，请检查accessKeyId、accessKeySecret和bucket配置，或确保STS服务可用');
        }

        ossClient = new OSS(finalConfig);
        return ossClient;
    }
}

/**
 * 获取OSS客户端实例（自动处理凭证刷新）
 */
export async function getOSSClient(): Promise<OSS> {
    // 检查客户端是否存在且凭证是否有效
    if (!ossClient || isCredentialsExpired()) {
        console.log('OSS客户端不存在或凭证已过期，正在重新初始化...');
        ossClient = await initOSSClient();
    }
    return ossClient;
}

/**
 * 生成唯一的文件名
 * @param originalName 原始文件名
 * @param folder 文件夹路径
 */
export function generateFileName(originalName: string, folder = 'uploads'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split('.').pop();
    const nameWithoutExt = originalName.split('.').slice(0, -1).join('.');

    // 清理文件名，移除特殊字符
    const cleanName = nameWithoutExt.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_');

    return `${folder}/${timestamp}_${random}_${cleanName}.${extension}`;
}

/**
 * 上传文件到OSS
 * @param file 文件对象
 * @param options 上传选项
 */
export interface UploadOptions {
    folder?: string; // 上传文件夹
    fileName?: string; // 自定义文件名
    onProgress?: (progress: number) => void; // 上传进度回调
    headers?: Record<string, string>; // 自定义请求头
}

export async function uploadFileToOSS(
    file: File,
    options: UploadOptions = {}
): Promise<{ url: string; fileName: string; size: number }> {
    try {
        const client = await getOSSClient();
        const { folder = 'uploads', fileName, onProgress, headers } = options;

        // 生成文件名
        const ossFileName = fileName || generateFileName(file.name, folder);

        console.log('开始上传文件:', {
            fileName: ossFileName,
            fileSize: file.size,
            fileType: file.type,
            folder: folder
        });

        // 上传文件
        const result = await client.put(ossFileName, file, {
            headers: {
                'Content-Type': file.type,
                ...headers
            },
            progress: onProgress ? (p: number) => {
                onProgress(Math.round(p * 100));
            } : undefined
        });

        console.log('文件上传成功:111', {
            url: result.url,
            fileName: ossFileName
        });

        return {
            url: result.url,
            fileName: ossFileName,
            size: file.size
        };
    } catch (error) {
        console.error('OSS上传失败:', error);

        // 检查是否是CORS错误
        if (error instanceof Error && error.message.includes('CORS')) {
            throw new Error('CORS跨域错误：请检查阿里云OSS的CORS配置');
        }

        // 检查是否是网络错误
        if (error instanceof Error && (error.message.includes('XHR error') || error.message.includes('net::ERR_FAILED'))) {
            throw new Error('网络连接错误：请检查网络连接和OSS配置');
        }

        throw new Error(`文件上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 批量上传文件到OSS
 * @param files 文件数组
 * @param options 上传选项
 */
export async function batchUploadToOSS(
    files: File[],
    options: UploadOptions = {}
): Promise<{ url: string; fileName: string; size: number }[]> {
    const results = [];

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileOptions = {
            ...options,
            onProgress: options.onProgress ? (progress: number) => {
                // 计算总体进度
                const totalProgress = ((i * 100) + progress) / files.length;
                options.onProgress!(Math.round(totalProgress));
            } : undefined
        };

        try {
            const result = await uploadFileToOSS(file, fileOptions);
            results.push(result);
        } catch (error) {
            console.error(`文件 ${file.name} 上传失败:`, error);
            throw error;
        }
    }

    return results;
}

/**
 * 删除OSS文件
 * @param fileName 文件名或文件URL
 */
export async function deleteFileFromOSS(fileName: string): Promise<void> {
    const client = await getOSSClient();

    try {
        // 如果是完整URL，提取文件名
        let ossFileName = fileName;
        if (fileName.startsWith('http')) {
            const url = new URL(fileName);
            ossFileName = url.pathname.substring(1); // 移除开头的 /
        }

        await client.delete(ossFileName);
    } catch (error) {
        console.error('OSS删除失败:', error);
        throw new Error(`文件删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 批量删除OSS文件
 * @param fileNames 文件名或文件URL数组
 */
export async function batchDeleteFromOSS(fileNames: string[]): Promise<void> {
    const client = await getOSSClient();

    try {
        // 处理文件名
        const ossFileNames = fileNames.map(fileName => {
            if (fileName.startsWith('http')) {
                const url = new URL(fileName);
                return url.pathname.substring(1);
            }
            return fileName;
        });

        await client.deleteMulti(ossFileNames);
    } catch (error) {
        console.error('OSS批量删除失败:', error);
        throw new Error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 获取文件签名URL（用于私有bucket）
 * @param fileName 文件名
 * @param expires 过期时间（秒）
 */
export async function getSignedUrl(fileName: string, expires = 3600): Promise<string> {
    const client = await getOSSClient();

    try {
        return client.signatureUrl(fileName, { expires });
    } catch (error) {
        console.error('获取签名URL失败:', error);
        throw new Error(`获取签名URL失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 检查文件是否存在
 * @param fileName 文件名
 */
export async function checkFileExists(fileName: string): Promise<boolean> {
    const client = await getOSSClient();

    try {
        await client.head(fileName);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 获取文件信息
 * @param fileName 文件名
 */
export async function getFileInfo(fileName: string): Promise<{
    size: number
    lastModified: Date
    contentType: string
}> {
    const client = await getOSSClient();

    try {
        const result = await client.head(fileName);
        return {
            size: parseInt(result.res.headers['content-length'] || '0'),
            lastModified: new Date(result.res.headers['last-modified'] || ''),
            contentType: result.res.headers['content-type'] || ''
        };
    } catch (error) {
        console.error('获取文件信息失败:', error);
        throw new Error(`获取文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}
