import request from '@/utils/request';

// 登录方法
export function login(username: string, password: string, code: string, uuid: string) {
    const data = {
        username,
        password,
        code,
        uuid,
    };
    return request({
        url: 'sys/login',
        headers: {
            isToken: false,
        },
        method: 'post',
        data: data,
    });
}

// 注册方法
export function register(data: any) {
    return request({
        url: '/register',
        headers: {
            isToken: false,
        },
        method: 'post',
        data: data,
    });
}

// 获取用户详细信息
export function getInfo() {
    return request({
        url: 'sys/user/info',
        method: 'get',
    });
}

// 退出方法
export function logout() {
    return request({
        url: 'sys/logout',
        method: 'post',
    });
}

// 获取验证码
export function getCodeImg() {
    return request({
        url: 'sys/captchaImage',
        method: 'get',
        timeout: 20000,
    });
}
