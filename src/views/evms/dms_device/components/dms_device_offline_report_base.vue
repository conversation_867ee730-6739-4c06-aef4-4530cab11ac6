<template>
    <el-form ref="elabofflinereportRef" :model="form" :rules="rules" label-width="100px">
        <el-row class="form-con" justify="space-between">
            <el-col :span="24">
                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="填写人" prop="writer">
                            <el-input v-model="form.writer" placeholder="请输入填写人" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="办事处" prop="office">
                            <el-input v-model="form.office" placeholder="请输入办事处" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="巡检结果" prop="result">
                            <el-input v-model="form.result" placeholder="请输入巡检结果" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="9">
                        <el-form-item label="巡检日期" prop="inspectTime">
                            <el-date-picker
                                clearable
                                v-model="form.inspectTime"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="请选择巡检日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="9">
                        <el-form-item label="网络测试情况" prop="network">
                            <el-input v-model="form.network" placeholder="请输入网络测试情况" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="巡检工作记录" prop="record">
                            <el-input
                                type="textarea"
                                :rows="3"
                                v-model="form.record"
                                placeholder="请输入巡检工作记录"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="E-lab画面" prop="elabImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.elabImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="E-bs画面" prop="ebsImage">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.ebsImage"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="30">
                    <el-col :span="18">
                        <el-form-item label="脑医汇Logo" prop="logo">
                            <ImageUpload
                                ref="imageUpload"
                                v-model:modelValue="form.logo"
                                list-type="picture-card"
                                :show-file-list="true"
                            ></ImageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>

            <el-col :span="24" style="height: 100px">
                <el-row justify="end">
                    <el-button @click="cancel">返回</el-button>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                </el-row>
            </el-col>
        </el-row>
    </el-form>
</template>

<script setup name="Elabofflinereport" lang="ts">
import { getElabpatrolreport, addElabpatrolreport, updateElabpatrolreport } from '@/api/cl/elabpatrolreport';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ImageUpload from '@/components/ImageUpload/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const elabofflinereportList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const route = useRoute();
const router = useRouter();

const data = reactive<{
    form: any;
    rules: any;
}>({
    form: {},
    rules: {
        result: [{ required: true, message: '巡检结果不能为空', trigger: 'blur' }],
        writer: [{ required: true, message: '填写人不能为空', trigger: 'blur' }]
    }
});

const { form, rules } = toRefs(data);

/** 取消按钮 */
function cancel() {
    console.log('返回');
    router.back();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        cathId: undefined,
        writer: undefined,
        writeTime: undefined,
        office: undefined,
        result: undefined,
        inspectTime: undefined,
        record: undefined,
        elabImage: undefined,
        ebsImage: undefined,
        network: undefined,
        logo: undefined,
        type: 'D',
        status: undefined,
        fault: undefined,
        reason: undefined,
        solveTime: undefined,
        hospital: undefined,
        courtyard: undefined,
        provinceCity: undefined,
        address: undefined,
        area: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
    };
    proxy?.resetForm('elabofflinereportRef');
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['elabofflinereportRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateElabpatrolreport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                });
            } else {
                addElabpatrolreport(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                });
            }
        }
    });
}

function getCLElabOfflineReports() {
    const reportId = route.params.id;
    const deviceId = route.params.deviceId;
    if (reportId && reportId != 0) {
        getElabpatrolreport(reportId).then((response: any) => {
            form.value = response.data;
        });
    }
    form.value.deviceId = deviceId;
    form.value.type = 'D';
}

getCLElabOfflineReports();
</script>
