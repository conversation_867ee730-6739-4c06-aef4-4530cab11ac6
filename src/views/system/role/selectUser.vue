<template>
    <!-- 授权用户 -->
    <el-dialog v-model="visible" title="选择用户" width="800px" top="5vh" append-to-body>
        <el-form ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="用户名称" prop="username">
                <el-input
                    v-model="queryParams.username"
                    placeholder="请输入用户名称"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="手机号码" prop="phone">
                <el-input
                    v-model="queryParams.phone"
                    placeholder="请输入手机号码"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row>
            <el-table
                ref="refTable"
                :data="userList"
                height="260px"
                @rowClick="clickRow"
                @selectionChange="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column label="用户名称" prop="username" :show-overflow-tooltip="true" />
                <el-table-column label="用户昵称" prop="nickname" :show-overflow-tooltip="true" />
                <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
                <el-table-column label="手机" prop="phone" :show-overflow-tooltip="true" />
                <el-table-column label="状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime) }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                :total="total"
                @pagination="getList"
            />
        </el-row>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleSelectUser">确 定</el-button>
                <el-button @click="visible = false">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="SelectUser" lang="ts">
/* eslint-disable camelcase */
import { roleNotUserList, grantUser } from '@/api/system/role';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive } from 'vue';

const props = defineProps({
    roleId: {
        type: [Number, String],
    },
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_normal_disable } = proxy!.useDict('sys_normal_disable');

const userList = ref<any[]>([]);
const visible = ref(false);
const total = ref(0);
const userIds = ref<any[]>([]);
const roleId = ref<any>();

const queryParams = reactive<{
    pageNum: number;
    pageSize: number;
    username: any;
    phone: any;
}>({
    pageNum: 1,
    pageSize: 10,
    username: undefined,
    phone: undefined,
});

// 显示弹框
function show() {
    roleId.value = props.roleId;
    getList();
    visible.value = true;
}
/**选择行 */
function clickRow(row: any) {
    (proxy?.$refs['refTable'] as any).toggleRowSelection(row);
}
// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    userIds.value = selection.map(item => item.id);
}
// 查询表数据
function getList() {
    roleNotUserList(roleId.value, queryFormat(queryParams)).then((res: any) => {
        userList.value = res.data.rows;
        total.value = res.data.total;
    });
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}
const emit = defineEmits(['ok']);
/** 选择授权用户操作 */
function handleSelectUser() {
    const uIds = userIds.value.join(',');
    if (uIds === '') {
        proxy!.$modal.msgError('请选择要分配的用户');
        return;
    }
    grantUser({ roleId: roleId.value, userIds: uIds }).then((res: any) => {
        proxy!.$modal.msgSuccess(res.msg);
        if (res.code === 200) {
            visible.value = false;
            emit('ok');
        }
    });
}

defineExpose({
    show,
});
</script>
