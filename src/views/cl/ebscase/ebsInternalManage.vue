<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button disabled>{{ elabCaseTitle }}--EBS病例内部管理</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsinternalmanage:add']"
                    type="primary"
                    plain
                    icon="Plus"
                    :disabled="exists"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsinternalmanage:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:ebsinternalmanage:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="ebsinternalmanageList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" prop="id" />

            <el-table-column label="应用活动" align="center" prop="activity" />

            <el-table-column label="发布日期" align="center" prop="publishDate" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.publishDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="上架日期" align="center" prop="onlineDate" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.onlineDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>

            <el-table-column label="下载来源" align="center" prop="download" />

            <el-table-column label="需求来源" align="center" prop="demand" />

            <el-table-column label="商务" align="center" prop="business" />

            <el-table-column label="办事处" align="center" prop="office" />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:ebsinternalmanage:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:ebsinternalmanage:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改EBS病例内部管理表对话框 -->
        <el-dialog v-model="open" :title="title" width="800px" append-to-body>
            <el-form ref="ebsinternalmanageRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="应用活动" prop="activity">
                            <el-input v-model="form.activity" placeholder="请输入应用活动" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="发布日期" prop="publishDate">
                            <el-date-picker
                                clearable
                                v-model="form.publishDate"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择公众号发布日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="下载来源" prop="download">
                            <el-input v-model="form.download" placeholder="请输入下载来源" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="App上架日期" prop="onlineDate">
                            <el-date-picker
                                clearable
                                v-model="form.onlineDate"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择App上架日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="需求来源" prop="demand">
                            <el-input v-model="form.demand" placeholder="请输入需求来源" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="商务" prop="business">
                            <el-input v-model="form.business" placeholder="请输入商务" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="办事处" prop="office">
                            <el-input v-model="form.office" placeholder="请输入办事处" />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="工作内容" prop="works">
                            <el-input
                                type="textarea"
                                :min="3"
                                v-model="form.works"
                                placeholder="请输入工作内容"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="Ebsinternalmanage" lang="ts">
import {
    listEbsinternalmanage,
    getEbsinternalmanage,
    delEbsinternalmanage,
    addEbsinternalmanage,
    updateEbsinternalmanage
} from '@/api/cl/ebsinternalmanage';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { getEbscase, getElabCaseById } from '@/api/cl/ebscase';
import { allEbsreleaseplan } from '@/api/cl/ebsreleaseplan';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const ebsinternalmanageList = ref<any[]>([]);
const planList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const exists = ref(false);
const route = useRoute();
const load = ref(true);
const elabCaseTitle = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        ebsCase_id: undefined,
        activity: undefined,
        download: undefined,
        demand: undefined,
        business: undefined,
        office: undefined,
        works: undefined,
    },
    rules: {
        activity: [{ required: true, message: '应用活动不能为空', trigger: 'blur' }]
    },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询EBS病例内部管理表列表 */
function getList() {
    loading.value = true;
    listEbsinternalmanage(queryFormat(queryParams.value)).then((response: any) => {
        ebsinternalmanageList.value = response.data.rows;
        total.value = response.data.total;
        exists.value = total.value > 0;
        loading.value = false;
    });
}

/**
 * 获取所有EBS公众号发布计划
 */
function searchEbsReleasePlanList(title?: String) {
    load.value = true;
    allEbsreleaseplan(queryFormat({ title: title, sort: 'views asc' })).then(response => {
        planList.value = response.data;
    });
    load.value = false;
}

function getEbsCaseInfo(ebsCaseId: any) {
    getEbscase(ebsCaseId).then((response: any) => {
        if (response.data.elabId) {
            getElabCaseById(response.data.elabId).then(response => {
                elabCaseTitle.value = response.data[0].caseName;
            });
        }
    });
}

/** 关闭按钮 */
function close() {
    const obj = { path: '/cl/ebsCase' };
    proxy!.$tab.closeOpenPage(obj);
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        caseId: undefined,
        activity: undefined,
        publishDate: undefined,
        onlineDate: undefined,
        download: undefined,
        demand: undefined,
        business: undefined,
        office: undefined,
        works: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('ebsinternalmanageRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    form.value.caseId = queryParams.value.ebsCase_id;
    title.value = '添加EBS病例内部管理表';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getEbsinternalmanage(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改EBS病例内部管理表';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['ebsinternalmanageRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateEbsinternalmanage(form.value).then(() => {
                    proxy?.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addEbsinternalmanage(form.value).then(() => {
                    proxy?.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除EBS病例内部管理表编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delEbsinternalmanage(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy?.download(
        'cl/ebsinternalmanage/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `ebsinternalmanage_${new Date().getTime()}#.xlsx`
    );
}

function getEbsInternalManage(ebsCaseId: any) {
    if (ebsCaseId) {
        queryParams.value.ebsCase_id = ebsCaseId;
        getEbsCaseInfo(ebsCaseId);
    }
    getList();
}

getEbsInternalManage(route.params && route.params.ebsCaseId);
</script>
