{"contentStat.article": "文章数量", "contentStat.channel": "栏目数量", "contentStat.user": "用户数量", "contentStat.attachment": "附件数量", "contentStat.last7day": "最近7天", "systemInfo.version": "版本号", "systemInfo.os": "操作系统", "systemInfo.osName": "系统名称", "systemInfo.osArch": "系统架构", "systemInfo.osVersion": "系统版本", "systemInfo.java": "Java Runtime", "systemInfo.javaRuntimeName": "Java Runtime名称", "systemInfo.javaRuntimeVersion": "Java Runtime版本", "systemInfo.javaVersion": "Java版本", "systemInfo.javaVendor": "Java提供商", "systemInfo.javaVm": "Java虚拟机", "systemInfo.javaVmName": "虚拟机名称", "systemInfo.javaVmVersion": "虚拟机版本", "systemInfo.javaVmVendor": "虚拟机供应商", "systemInfo.userName": "系统用户", "systemInfo.userDir": "用户主目录", "systemInfo.javaIoTmpdir": "用户临时目录", "systemInfo.memory": "内存使用率", "systemInfo.maxMemory": "最大内存", "systemInfo.maxMemory.tooltip": "JVM可使用的最大内存", "systemInfo.totalMemory": "总内存", "systemInfo.totalMemory.tooltip": "JVM已申请的内存。总内存小于等于最大内存，JVM通常不会一次性把所有内存都申请下来", "systemInfo.usedMemory": "已用内存", "systemInfo.freeMemory": "空闲内存", "systemInfo.freeMemory.tooltip": "已申请但未使用的内存", "systemInfo.remainingMemory": "剩余内存", "systemInfo.remainingMemory.tooltip": "JVM未申请内存", "systemInfo.availableMemory": "可用内存", "systemInfo.availableMemory.tooltip": "未申请的内存加上空闲内存的总和", "systemInfo.upDays": "运行天数", "systemInfo.upDays.unit": "天", "systemInfo.upDays.tooltip": "JVM运行天数。即程序运行天数，非服务器运行天数", "systemMonitor.osUpDays": "运行天数", "systemMonitor.osName": "操作系统", "systemMonitor.cpuName": "CPU名称", "systemMonitor.cpuVendorFreq": "主频", "systemMonitor.cpuLoad": "CPU利用率", "systemMonitor.cpuCores": "物理核心", "systemMonitor.cpuLogicalCores": "逻辑核心", "systemMonitor.memory": "服务器内存", "systemMonitor.memoryTotal": "总内存", "systemMonitor.memoryUsed": "已用内存", "systemMonitor.memoryAvailable": "可用内存", "systemMonitor.fileStores": "文件系统", "systemMonitor.fileStore.mount": "挂载点", "systemMonitor.fileStore.type": "类型", "systemMonitor.fileStore.space": "空间", "systemMonitor.fileStore.total": "总空间", "systemMonitor.fileStore.used": "已用空间", "systemMonitor.fileStore.usable": "可用空间", "": ""}