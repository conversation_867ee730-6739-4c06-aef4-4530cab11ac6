# 后台STS接口实现指南

本文档说明如何在后台实现STS临时凭证接口，为前端提供安全的OSS上传凭证。

## 接口规范

### 获取STS临时凭证

**接口地址：** `GET /api/oss/sts-token`

**请求参数：**
```json
{
  "folder": "uploads",     // 可选，上传文件夹
  "duration": 3600        // 可选，凭证有效期（秒）
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "accessKeyId": "STS.NUgYrLnoS37r9**********",
    "accessKeySecret": "BcR2O2BYlBm2VnKMZgEcKPqKvh**********",
    "securityToken": "CAIS8AF1q6Ft5B2yfSjIr5bAJcvfrpx50oWvVTHOgkUNTdxOjrjNmTz2IHtIf3NpAusdsP41nGtY6v8dlqFrR4QAXlDfNRvQp03rFqFHPWZHInuDox55m4cTXNAr+Ihknz6OSXGukDc7kZbaAKlfQFHyzimZYrWmdat6yJwtKBJ7HtdwfUeQXoT4B3NdXLwM7VrTCoFdZHJaPOmvDJX1bf5hdMpOZjuMpHrRdssUXlDDLotjBA==",
    "bucketName": "your-bucket-name",
    "region": "oss-cn-shanghai",
    "endpoint": "https://oss-cn-shanghai.aliyuncs.com",
    "expiration": "2024-01-01T12:00:00Z"
  }
}
```

## Java实现示例

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>aliyun-java-sdk-core</artifactId>
    <version>4.6.3</version>
</dependency>
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>aliyun-java-sdk-sts</artifactId>
    <version>3.1.0</version>
</dependency>
```

### 2. 配置类

```java
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
@Data
public class OSSConfig {
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String region = "oss-cn-shanghai";
    private String endpoint;
    private String roleArn; // RAM角色ARN
    private String roleSessionName = "oss-upload-session";
    private Long durationSeconds = 3600L; // 默认1小时
}
```

### 3. STS服务类

```java
@Service
public class STSService {

    @Autowired
    private OSSConfig ossConfig;

    public STSTokenResponse getSTSToken(String folder, Long duration) {
        try {
            // 创建STS客户端
            DefaultProfile profile = DefaultProfile.getProfile(
                ossConfig.getRegion(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
            );
            IAcsClient client = new DefaultAcsClient(profile);

            // 创建AssumeRole请求
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleArn(ossConfig.getRoleArn());
            request.setRoleSessionName(ossConfig.getRoleSessionName());
            request.setDurationSeconds(duration != null ? duration : ossConfig.getDurationSeconds());

            // 设置权限策略（可选）
            if (folder != null) {
                String policy = buildPolicy(folder);
                request.setPolicy(policy);
            }

            // 获取临时凭证
            AssumeRoleResponse response = client.getAcsResponse(request);
            AssumeRoleResponse.Credentials credentials = response.getCredentials();

            // 构建返回结果
            return STSTokenResponse.builder()
                .accessKeyId(credentials.getAccessKeyId())
                .accessKeySecret(credentials.getAccessKeySecret())
                .securityToken(credentials.getSecurityToken())
                .bucketName(ossConfig.getBucketName())
                .region(ossConfig.getRegion())
                .endpoint(ossConfig.getEndpoint())
                .expiration(credentials.getExpiration())
                .build();

        } catch (Exception e) {
            throw new RuntimeException("获取STS凭证失败", e);
        }
    }

    private String buildPolicy(String folder) {
        // 构建权限策略，限制只能上传到指定文件夹
        return "{\n" +
               "  \"Version\": \"1\",\n" +
               "  \"Statement\": [\n" +
               "    {\n" +
               "      \"Effect\": \"Allow\",\n" +
               "      \"Action\": [\n" +
               "        \"oss:PutObject\",\n" +
               "        \"oss:GetObject\"\n" +
               "      ],\n" +
               "      \"Resource\": [\n" +
               "        \"acs:oss:*:*:" + ossConfig.getBucketName() + "/" + folder + "/*\"\n" +
               "      ]\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }
}
```

### 4. 控制器

```java
@RestController
@RequestMapping("/api/oss")
public class OSSController {

    @Autowired
    private STSService stsService;

    @GetMapping("/sts-token")
    public Result<STSTokenResponse> getSTSToken(
        @RequestParam(required = false) String folder,
        @RequestParam(required = false) Long duration
    ) {
        try {
            STSTokenResponse token = stsService.getSTSToken(folder, duration);
            return Result.success(token);
        } catch (Exception e) {
            return Result.error("获取STS凭证失败: " + e.getMessage());
        }
    }
}
```

### 5. 响应实体

```java
@Data
@Builder
public class STSTokenResponse {
    private String accessKeyId;
    private String accessKeySecret;
    private String securityToken;
    private String bucketName;
    private String region;
    private String endpoint;
    private String expiration;
}
```

## Node.js实现示例

### 1. 安装依赖

```bash
npm install @alicloud/pop-core
```

### 2. 实现代码

```javascript
const Core = require('@alicloud/pop-core');

class STSService {
  constructor(config) {
    this.client = new Core({
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
      endpoint: 'https://sts.cn-shanghai.aliyuncs.com',
      apiVersion: '2015-04-01'
    });
    this.config = config;
  }

  async getSTSToken(folder, duration = 3600) {
    const params = {
      'RegionId': this.config.region,
      'RoleArn': this.config.roleArn,
      'RoleSessionName': 'oss-upload-session',
      'DurationSeconds': duration
    };

    if (folder) {
      params.Policy = this.buildPolicy(folder);
    }

    try {
      const result = await this.client.request('AssumeRole', params, {
        method: 'POST'
      });

      return {
        accessKeyId: result.Credentials.AccessKeyId,
        accessKeySecret: result.Credentials.AccessKeySecret,
        securityToken: result.Credentials.SecurityToken,
        bucketName: this.config.bucketName,
        region: this.config.region,
        endpoint: this.config.endpoint,
        expiration: result.Credentials.Expiration
      };
    } catch (error) {
      throw new Error(`获取STS凭证失败: ${error.message}`);
    }
  }

  buildPolicy(folder) {
    return JSON.stringify({
      "Version": "1",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "oss:PutObject",
            "oss:GetObject"
          ],
          "Resource": [
            `acs:oss:*:*:${this.config.bucketName}/${folder}/*`
          ]
        }
      ]
    });
  }
}

module.exports = STSService;
```

## 安全注意事项

1. **RAM角色配置**：创建专门的RAM角色，只授予必要的OSS权限
2. **权限策略**：通过Policy限制上传路径和操作权限
3. **凭证有效期**：设置合理的凭证有效期，建议不超过1小时
4. **接口鉴权**：确保STS接口有适当的用户鉴权
5. **日志记录**：记录STS凭证的申请和使用情况

## RAM角色配置

1. 登录阿里云控制台，进入RAM管理
2. 创建RAM角色，选择"阿里云服务"，受信服务选择"云服务器ECS"
3. 为角色添加权限策略：

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "oss:PutObject",
        "oss:GetObject",
        "oss:DeleteObject"
      ],
      "Resource": [
        "acs:oss:*:*:your-bucket-name/*"
      ]
    }
  ]
}
```

4. 记录角色的ARN，配置到应用中

通过以上配置，您的后台就可以为前端提供安全的STS临时凭证了。