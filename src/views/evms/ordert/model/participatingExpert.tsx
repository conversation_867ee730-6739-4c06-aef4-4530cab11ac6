import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCol,
    ElDialog,
    ElRow,
    ElEmpty,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElSelect
} from 'element-plus';
import { RefsKes } from '@/type/common';
import moment from 'moment';
import {
    EvmsVideoListDetail,
    EvmsCousultOrderDetail,
    Experts,
    inviteAgree,
    getOrdert
} from '@/api/evms/ordert';

export default defineComponent({
    emits: ['ok'],
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        let instance: RefsKes;

        const state = reactive({
            visible: false,
            showSearch: true,
            loading: false,
            record: {} as EvmsCousultOrderDetail,
            title: '',
            agreeStatus: true,
            statusVisible: false,
            agreeObj: {
                0: '待确认', // 待确认 无
                1: '同意',
                2: '拒绝',
                3: '过期',
                4: '已被其他用户接取',
                5: '已取消'
            } as any,
            orderNumber: '' as string
        });
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });

        function handleOk() {
            close();
        }

        function close() {
            state.visible = false;
        }

        function show(record: EvmsCousultOrderDetail) {
            state.visible = true;
            state.agreeStatus = true;
            state.orderNumber = record.orderNumber;
            // getData()
            state.record = record;
            state.record.experts.map(item => {
                item.agreeText = state.agreeObj[item.agree];
                if (item.agree === 1) {
                    state.agreeStatus = false;
                }
            });
            state.title = `当前邀约专家${state.record.experts.length}人:`;
        }

        function getData() {
            getOrdert({ orderNumber: state.orderNumber }).then(res => {
                state.record = res.data;
                state.record.experts.map(item => {
                    item.agreeText = state.agreeObj[item.agree];
                    if (item.agree === 1) {
                        state.agreeStatus = false;
                    }
                });
                state.title = `当前邀约专家${state.record.experts.length}人:`;
            });
        }

        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                }
            }
        };

        const textAlign = 'text-align:center;';
        expose({
            show
        });

        function inviteAgreeFn(item: Experts) {
            inviteAgree({
                agree: true,
                doctorId: item.doctorId,
                orderNumber: state.record.orderNumber
            });
        }

        function takeHisOrder(item: Experts) {
            proxy?.$modal
                .confirm('是否确认帮他接单？')
                .then(() => {
                    return inviteAgreeFn(item);
                })
                .then(() => {
                    close();
                    emit('ok');
                    proxy?.$modal.msgSuccess('操作成功');
                })
                .catch(e => {
                    console.log(e);
                });
        }

        return () => (
            <ElDialog
                title={state.title}
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
            >
                {state.record.experts && state.record.experts.length > 0 ? (
                    <ElForm v-show={state.showSearch} ref="queryRef" inline={true}>
                        <ElRow gutter={24} class="mb8">
                            <ElCol style={textAlign} span={4}>
                                专家ID
                            </ElCol>
                            <ElCol style={textAlign} span={4}>
                                专家名
                            </ElCol>
                            <ElCol style={textAlign} span={4}>
                                专家手机号
                            </ElCol>
                            <ElCol style={textAlign} span={4}>
                                接受状态
                            </ElCol>
                            <ElCol style={textAlign} span={4}>
                                操作时间
                            </ElCol>
                            <ElCol style={textAlign} span={4}>
                                操作
                            </ElCol>
                        </ElRow>

                        {state.record.experts.map(item => {
                            return (
                                <ElRow gutter={24} class="mb8">
                                    <ElCol style={textAlign} span={4}>
                                        {item.expertId}
                                    </ElCol>
                                    <ElCol style={textAlign} span={4}>
                                        {item.expertName}
                                    </ElCol>
                                    <ElCol style={textAlign} span={4}>
                                        {item.mobile}
                                    </ElCol>
                                    <ElCol style={textAlign} span={4}>
                                        {item.agreeText}
                                    </ElCol>
                                    <ElCol style={textAlign} span={4}>
                                        {moment(item.operationTimeStamp).format('YYYY-MM-DD HH:mm')}
                                    </ElCol>
                                    <ElCol
                                        v-show={state.agreeStatus && item.agree === 0}
                                        style={textAlign}
                                        span={4}
                                    >
                                        <ElButton
                                            link
                                            type="primary"
                                            onClick={() => takeHisOrder(item)}
                                            v-hasPermi={[['evms:ordert:edit']]}
                                        >
                                            帮他接单
                                        </ElButton>
                                    </ElCol>
                                </ElRow>
                            );
                        })}
                    </ElForm>
                ) : (
                    <ElEmpty description="暂无邀约专家"></ElEmpty>
                )}
            </ElDialog>
        );
    }
});
