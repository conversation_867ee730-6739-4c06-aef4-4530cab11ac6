<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Dms_permission_info_device from '@/views/evms/dms_permission/components/dms_permission_info_device.vue';
import Dms_permission_info_current from '@/views/evms/dms_permission/components/dms_permission_info_current.vue';

const activeName = ref('base');
const route = useRoute();

const userId = ref(route.params.id);
</script>

<template>
    <div class="dms_permission_container">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" type="card" class="demo-tabs">
                <el-tab-pane label="通用权限" name="base">
                    <dms_permission_info_current :userId="userId" />
                </el-tab-pane>
                <el-tab-pane label="设备权限" name="video" :disabled="!userId" :lazy="true">
                    <dms_permission_info_device v-if="!!userId" :userId="userId" />
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<style scoped lang="scss">
.dms_permission_container {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;
    :deep(.el-card) {
        height: 100%;
    }
    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }
    :deep(.el-tabs) {
        height: 100%;
    }
    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }
    :deep(.el-tab-pane) {
        height: 100% !important;
        overflow-y: scroll;
    }

    :deep(.el-form) {
        height: 100% !important;
        overflow-y: scroll;
        &::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
        margin-right: 1px;
        border: none;
        border-radius: 0px 0px 6px 6px;
        background: #ecf7ff;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        color: #009dff;
        font-family: Microsoft YaHei;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
        border: none;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
        border-radius: 0px 0px 6px 6px;
        background: #009dff;
        color: #fff;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;
    }
    :deep(.el-textarea__inner) {
        height: 111px !important;
        background: #f8f9fb !important;
    }
    :deep(.el-input__wrapper) {
        background: #f8f9fb !important;
    }
    :deep(.el-select) {
        width: 100% !important;
        margin: 0 !important;
    }
    :deep(.el-input__inner) {
        height: 40px !important;
    }
    .form-con {
        height: 100%;
    }
}
</style>
<style lang="scss">
.dms_device_info_user_dialog {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }
    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0px 0px 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }
    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
