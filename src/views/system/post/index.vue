<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
            <el-form-item label="岗位编码" prop="code">
                <el-input
                    v-model="queryParams.code"
                    clearable
                    placeholder="请输入岗位编码"
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="岗位名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    clearable
                    placeholder="请输入岗位名称"
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" clearable placeholder="岗位状态" style="width: 200px">
                    <el-option
                        v-for="dict in sys_normal_disable"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button v-hasPermi="['sys:post:add']" icon="Plus" plain type="primary" @click="handleAdd"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:post:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:post:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['sys:post:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="postList" @selectionChange="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column align="center" label="岗位编号" prop="id" />
            <el-table-column align="center" label="岗位编码" prop="code" />
            <el-table-column align="center" label="岗位名称" prop="name" />
            <el-table-column align="center" label="岗位排序" prop="sort" />
            <el-table-column align="center" label="状态" prop="status">
                <template #default="scope">
                    <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column align="center" label="创建时间" prop="createTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['sys:post:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button
                    >
                    <el-button
                        v-hasPermi="['sys:post:remove']"
                        icon="Delete"
                        link
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改岗位对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="postRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="岗位名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入岗位名称" />
                </el-form-item>
                <el-form-item label="岗位编码" prop="code">
                    <el-input v-model="form.code" placeholder="请输入编码名称" />
                </el-form-item>
                <el-form-item label="岗位顺序" prop="sort">
                    <el-input-number v-model="form.sort" :min="0" controls-position="right" />
                </el-form-item>
                <el-form-item label="岗位状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
                            dict.label
                            }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="Post" setup>
/* eslint-disable camelcase */
import { listPost, addPost, delPost, getPost, updatePost } from '@/api/system/post';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_normal_disable } = proxy!.useDict('sys_normal_disable');

const postList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: undefined,
        name: undefined,
        status: undefined
    },
    rules: {
        name: [{ required: true, message: '岗位名称不能为空', trigger: 'blur' }],
        code: [{ required: true, message: '岗位编码不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '岗位顺序不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询岗位列表 */
function getList() {
    loading.value = true;
    listPost(queryFormat(queryParams.value)).then((response: any) => {
        postList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}

/** 表单重置 */
function reset() {
    form.value = {
        id: undefined,
        code: undefined,
        name: undefined,
        sort: 0,
        status: '0',
        remark: undefined
    };
    proxy!.resetForm('postRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加岗位';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const id = row.id || ids.value;
    getPost(id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = '修改岗位';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['postRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id !== undefined) {
                updatePost(form.value).then(response => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addPost(form.value).then(response => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const postIds = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function() {
            return delPost(postIds);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch((e: any) => {
            console.log(e);
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'system/post/export',
        {
            ...queryParams.value
        },
        `post_${new Date().getTime()}.xlsx`
    );
}

getList();
</script>
