<template>
    <div class="app-container">
        <el-card shadow="never" style="padding-right: 50px; border-radius: 12px">
            <el-form :inline="true" :model="tableInfo.from" label-width="100" size="default">
                <el-form-item label="ID" prop="id">
                    <el-input
                        v-model="tableInfo.from.id"
                        clearable
                        placeholder="请输入"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <!--                <el-form-item label="进行状态" prop="name">-->
                <!--                    <el-select filterable v-model="tableInfo.from.status" placeholder="请选择状态" clearable style="width: 240px">-->
                <!--                        <el-option-->
                <!--                            v-for="dict in cns_consultation_status_enum"-->
                <!--                            :key="dict.value"-->
                <!--                            :label="dict.label"-->
                <!--                            :value="dict.value"-->
                <!--                        />-->
                <!--                    </el-select>-->
                <!--                </el-form-item>-->
                <el-form-item label="主题" prop="type">
                    <el-input
                        v-model="tableInfo.from.like_Jmeeting_theme"
                        clearable
                        placeholder="请输入主题"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="发起人" prop="type">
                    <el-input
                        v-model="tableInfo.from.like_Jorganizer_realname"
                        clearable
                        placeholder="请输入发起人姓名"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <!-- 可折叠的高级搜索项 -->
                <template v-if="isExpand">
                    <el-form-item label="所属医院" prop="type">
                        <el-autocomplete
                            v-model="state.hospitalName"
                            :fetch-suggestions="querySearchHospital"
                            clearable
                            placeholder="请选择医院"
                            style="width: 240px"
                            @clear="() => (tableInfo.from.like_Jdevice_hospital_name = '')"
                            @select="handleSelectHospital"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="手术室" prop="type">
                        <el-autocomplete
                            v-model="state.devicename"
                            :fetch-suggestions="querySearchDevice"
                            clearable
                            placeholder="请选择手术室"
                            style="width: 240px"
                            @clear="() => (tableInfo.from.like_Jdevice_id = '')"
                            @select="handleSelectDevice"
                            @keyup.enter="tableInfo.search"
                        />
                    </el-form-item>
                    <el-form-item label="指导类型" prop="address">
                        <el-select
                            v-model="tableInfo.from.guideType"
                            clearable
                            filterable
                            placeholder="请选择类型"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in cns_consultation_guide_type_enum"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="专家类型" prop="address">
                        <el-select
                            v-model="tableInfo.from.type"
                            clearable
                            filterable
                            placeholder="请选择类型"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="dict in cns_consultation_type_enum"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="预约时间" prop="dateStr">
                        <el-date-picker
                            v-model="tableInfo.from.meeting_startTimeUtc"
                            format="YYYY-MM-DD"
                            placeholder="请选择时间"
                            style="width: 240px"
                            type="daterange"
                            value-format="YYYY-MM-DD"
                        />
                    </el-form-item>
                </template>

                <!-- 搜索按钮区域 -->
                <el-row :gutter="20" justify="start" style="width: 100%; margin-top: 10px">
                    <el-col :span="12" style="padding-left: 50px">
                        <el-button v-hasPermi="['cns:cns_consultation:query']" icon="Search" type="primary"
                                   @click="tableInfo.search">搜索
                        </el-button>
                        <el-button icon="Refresh" @click="resetHandler">重置</el-button>
                        <el-button type="text" @click="isExpand = !isExpand">
                            {{ isExpand ? '收起' : '展开' }}
                            <el-icon class="el-icon--right">
                                <arrow-up v-if="isExpand" />
                                <arrow-down v-else />
                            </el-icon>
                        </el-button>
                    </el-col>
                </el-row>

                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:cns_consultation:add']"
                        color="#009dff"
                        icon="Plus"
                        plain
                        @click="handleAdd"
                    >新增
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:cns_consultation:edit']"
                        :disabled="single"
                        color="#01c064"
                        icon="Edit"
                        plain
                        @click="() => handleUpdate()"
                    >修改
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:cns_consultation:remove']"
                        :disabled="multiple"
                        color="#ff5c00"
                        icon="Delete"
                        plain
                        @click="() => handleDelete()"
                    >删除
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:cns_consultation:cancel']"
                        :disabled="multiple"
                        color="#ff9900"
                        icon="Close"
                        plain
                        @click="() => handleCancel()"
                    >取消会诊
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['cns:cns_consultation:close']"
                        :disabled="multiple"
                        color="#409eff"
                        icon="Check"
                        plain
                        @click="() => handleClose()"
                    >结束会诊
                    </el-button>
                </el-col>
                <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
            </el-row>

            <el-table
                :data="tableInfo.list.value"
                height="450"
                row-key="id"
                style="margin-top: 20px"
                @sort-change="tableInfo.sort"
                @selection-change="handleSelectionChange"
            >
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="ID" prop="id" />
                <el-table-column align="center" label="进行状态" prop="status">
                    <template #default="scope">
                        <dict-tag :options="cns_consultation_status_enum" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="指导主题" prop="meeting.theme" />

                <el-table-column align="center" label="发起人" prop="organizer.realname" />
                <el-table-column align="center" label="手术室" prop="device.name" />
                <el-table-column align="center" label="医院" prop="device.hospitalVo.name" />
                <el-table-column align="center" label="指导类型" prop="type">
                    <template #default="scope">
                        <dict-tag :options="cns_consultation_guide_type_enum" :value="scope.row.guideType" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="专家类型" prop="type">
                    <template #default="scope">
                        <dict-tag :options="cns_consultation_type_enum" :value="scope.row.type" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="指导专家" prop="expert">
                    <template #default="scope">
                        <el-popover
                        class="box-item"
                        content="Top Center prompts info"
                        placement="top"
                    >
                    <template #default>
                        <span >{{
                             scope.row.type === 'P' ?
                                (scope.row.expert && scope.row.expert.realname
                                    ? scope.row.expert.realname
                                    : '待分配')
                                    : (scope.row.members && scope.row.members.length > 0
                                    ? scope.row.members.map(member => member.user.realname).join(', ')
                                    : '无专家')
                            }}</span>
                    </template>
                        <template #reference>
                         <span >{{
                             (scope.row.type === 'P' ?
                                (scope.row.expert && scope.row.expert.realname
                                    ? scope.row.expert.realname
                                    : '待分配')
                                    : (scope.row.members && scope.row.members.length > 0
                                    ? scope.row.members.map(member => member.user.realname).join(', ')
                                    : '无专家')).toString().slice(0, 10) + '...'
                            }}</span>
                        </template>
                    </el-popover>
                       
                    </template>
                </el-table-column>
                <el-table-column align="center" label="预约时间" prop="dateStr" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.dateStr }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                    <template #default="scope">
                        <el-button
                            v-hasPermi="['cns:cns_consultation:edit']"
                            icon="Edit"
                            link
                            type="primary"
                            @click="handleUpdate(scope.row)"
                        >修改
                        </el-button>
                        <el-button
                            v-hasPermi="['cns:cns_consultation:remove']"
                            icon="Delete"
                            link
                            type="primary"
                            @click="handleDelete(scope.row)"
                        >删除
                        </el-button>
                        <el-button
                            v-if="scope.row.type === 'P'"
                            v-hasPermi="['consultation:order:share']"
                            icon="Share"
                            link
                            type="warning"
                            @click="handleShare(scope.row)"
                        >邀请链接
                        </el-button>
                        <el-button
                            v-hasPermi="['cns:cns_consultation:memebr_list']"
                            icon="User"
                            link
                            type="warning"
                            @click="handleLook(scope.row)"
                        >查看会议详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    v-model:page="tableInfo.pageParam.pageNum"
                    :total="tableInfo.total.value"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
        <!--    查看邀请链接 -->
        <el-dialog
            v-model="lookLink"
            append-to-body
            draggable
            modal-class="consultation_order_look"
            style="background-color: rgba(248, 249, 251, 1)"
            width="350px"
            @closed="lookLinkClose"
        >
            <template #header>
                <div class="my-header" style="background-color: #f8f8f8">
                    <div class="dialog_edit_header">邀请链接</div>
                </div>
            </template>
            <div style="padding: 20px 30px 50px">
                <div>{{ lookLinkData?.organizer.realname }}邀请你参加术中指导</div>
                <a :href="lookLinkData ? lookLinkData.deviceLinkUrl.brainmed : ''" style="color: #0581ce">{{
                        lookLinkData?.deviceLinkUrl.brainmed
                    }}</a>
                <div><span class="title">主题：</span>{{ lookLinkData?.meeting.theme }}</div>
                <div><span class="title">手术时间：</span>{{ lookLinkData?.dateStr }}</div>
                <div><span class="title">手术室：</span>{{ lookLinkData?.device.fullName }}</div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="handleCopy">复制</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
export default {
    name: 'ConsultationOrder'
};
</script>
<script lang="ts" setup>
import { getTableInfo } from '@/minix/tables';
import type { DmsDeviceInfo } from '@/model/dms_device';
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import type { OcConsultationOrder } from '@/model/oc_consultation';
import { delDeviceUser, getHospitalListByKeywords, listDmsDevice } from '@/api/evms/dms_device';
import { oc_delCns, oc_cancelConsultation, oc_closeConsultation } from '@/api/evms/oc_consultation';
import router from '@/router';
import type { AxiosResponse } from 'axios';

import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import { RtcMeetingDetail } from '@/model/rtc_meeting';
import { timestampToTime } from '@/utils/ruoyi';
import { DmsHospitalInfo } from '@/model/dms_hospital';

// 添加展开折叠控制
const isExpand = ref(false);

interface DeviceOption {
    value: string;
    id: number;
}

interface BaseResponse {
    code: number;
    message?: string;
    msg?: string;
}

interface DeviceListResponse extends BaseResponse {
    data: {
        total: number;
        rows: DmsDeviceInfo[];
    };
}

interface ConsultationResponse extends BaseResponse {
    data: any;
}

interface Device {
    id: string;
    name: string;
}

interface Organizer {
    userid: number;
    realname: string;
    avatar: string;
    title: string;
    company: string;
}

interface ExtendedConsultationOrder extends Omit<OcConsultationOrder, 'organizer' | 'device'> {
    device: Device;
    organizer: Organizer;
}

// 组件实例
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cns_consultation_status_enum } = proxy!.useDict('cns_consultation_status_enum');
const { cns_consultation_type_enum } = proxy!.useDict('cns_consultation_type_enum');
const { cns_consultation_guide_type_enum } = proxy!.useDict('cns_consultation_guide_type_enum');

// 表格数据
const tableInfo = getTableInfo<OcConsultationOrder>('/oc/consultation/list', {
    sort: 'id desc'
});

// 设备选择状态
const state = ref({
    devicename: '',
    hospitalName: ''
});
const resetHandler = () => {
    state.value.devicename = '';
    state.value.hospitalName = '';
    tableInfo.reset();
};
// 查询设备列表
const querySearchDevice = async (queryString: string, cb: (suggestions: DeviceOption[]) => void) => {
    try {
        const response = await listDmsDevice({ q_like_name: queryString });
        const results = response as unknown as DeviceListResponse;

        if (results.code === 200 && results.data?.rows?.length > 0) {
            const suggestions = results.data.rows.map((item: DmsDeviceInfo) => ({
                value: `${item.name}`,
                id: item.id
            }));
            cb(suggestions);
        } else {
            cb([]);
        }
    } catch (error) {
        console.error('查询设备列表失败:', error);
        cb([]);
    }
};

// 选择设备
const handleSelectDevice = (item: DeviceOption) => {
    if (tableInfo.from) {
        tableInfo.from.like_Jdevice_id = item.id.toString();
        state.value.devicename = item.value;
    }
};
const querySearchHospital = async (queryString: string, cb: any) => {
    const results = await getHospitalListByKeywords({ keywords: queryString });
    // call callback function to return suggestions
    if (results.code === 200) {
        console.log(results.data, '--1');
        // 判断results.data是一个数组并且长度大于0 使用type

        cb(
            results?.data && (results.data as DmsHospitalInfo[])?.length > 0
                ? (results.data as DmsHospitalInfo[]).map((item: any) => {
                    return {
                        value: item.name,
                        id: item.id
                    };
                })
                : []
        );
    }
};
const handleSelectHospital = (item: HopitalItem) => {
    console.log(item);
    tableInfo.from.like_Jdevice_hospital_name = item.value;
    state.value.hospitalName = item.value;
};

// 新增
const handleAdd = () => {
    router.push({ path: '/oc_consultation/intraoperativeGuidance_info' });
};

// 选择相关
const ids = ref<(string | number)[]>([]);
const single = ref(true);
const multiple = ref(true);

// 修改
const handleUpdate = (row?: OcConsultationOrder) => {
    const _id = row?.id || ids.value[0];
    if (_id) {
        router.push({ path: `/oc_consultation/intraoperativeGuidance_info/${_id}` });
    }
};

// 多选框选中数据
function handleSelectionChange(selection: OcConsultationOrder[]) {
    ids.value = selection.map(item => item.id || '');
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

// 查看邀请链接
const lookLink = ref(false);
const lookLinkData = ref<ExtendedConsultationOrder>();

const handleShare = (row: OcConsultationOrder) => {
    lookLink.value = true;
    lookLinkData.value = row as ExtendedConsultationOrder;
};

const lookLinkClose = () => {
    lookLinkData.value = undefined;
};

const handleLook = (row: OcConsultationOrder) => {
    router.push({ path: `/oc_consultation/look_meeting/${row.meeting.id}` });
};


// 按钮状态
const deleteBtnLoading = ref(false);
const cancelBtnLoading = ref(false);
const closeBtnLoading = ref(false);

// 删除操作
const handleDelete = async (row?: OcConsultationOrder) => {
    const id = row?.id || ids.value.join(',');
    if (!id) {
        proxy!.$modal.msgError('请选择指导');
        return;
    }

    try {
        deleteBtnLoading.value = true;
        await proxy!.$modal.confirm(`是否确认删除指导ID为"${id}"的数据项？`);
        const response = await oc_delCns(id);
        const res = response as unknown as ConsultationResponse;

        if (res.code === 200) {
            proxy!.$modal.msgSuccess('删除成功');
            state.value.devicename = '';
            tableInfo.reset();
        }
    } catch (error) {
        console.error('删除失败:', error);
    } finally {
        deleteBtnLoading.value = false;
    }
};

// 取消会诊操作
const handleCancel = async () => {
    const id = ids.value.join(',');
    if (!id) {
        proxy!.$modal.msgError('请选择要取消的会诊');
        return;
    }

    try {
        cancelBtnLoading.value = true;
        await proxy!.$modal.confirm(`是否确认取消选中的会诊？`);

        const response = await oc_cancelConsultation(id);
        const res = response as unknown as ConsultationResponse;

        if (res.code === 1) {
            proxy!.$modal.msgSuccess('取消会诊成功');
            state.value.devicename = '';
            tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '取消会诊失败');
        }
    } catch (error) {
        console.error('取消会诊失败:', error);
    } finally {
        cancelBtnLoading.value = false;
    }
};

// 结束会诊操作
const handleClose = async () => {
    const id = ids.value.join(',');
    if (!id) {
        proxy!.$modal.msgError('请选择要结束的会诊');
        return;
    }

    try {
        closeBtnLoading.value = true;
        await proxy!.$modal.confirm(`是否确认结束选中的会诊？`);

        const response = await oc_closeConsultation(id);
        const res = response as unknown as ConsultationResponse;

        if (res.code === 1) {
            proxy!.$modal.msgSuccess('结束会诊成功');
            state.value.devicename = '';
            tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '结束会诊失败');
        }
    } catch (error) {
        console.error('结束会诊失败:', error);
    } finally {
        closeBtnLoading.value = false;
    }
};

// 初始化加载
tableInfo.load();

const handleCopy = () => {
    console.log('分享', lookLinkData);
    const str = `
    ${lookLinkData.value?.organizer?.realname}邀请你参加术中指导
    ${lookLinkData.value?.deviceLinkUrl.brainmed}
    主题：${lookLinkData.value?.meeting.theme}
    手术时间：${lookLinkData.value?.dateStr}
    手术室：${lookLinkData.value?.device?.fullName}
    `;
    // 复制到剪切板
    navigator.clipboard.writeText(str).then(
        function() {
            proxy!.$modal.msgSuccess('复制成功');
        },
        function(err) {
            proxy!.$modal.msgError('复制失败');
        }
    );
};
const formatDuration = (totalDuration: number): string => {
    const hours = Math.floor(totalDuration / 3600);
    const minutes = Math.floor((totalDuration % 3600) / 60);
    const seconds = totalDuration % 60;

    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    if (hours > 0) {
        const hoursStr = hours.toString().padStart(2, '0');
        return `${hoursStr}:${minutesStr}:${secondsStr}`;
    } else {
        return `${minutesStr}:${secondsStr}`;
    }
};

const getRole = (role: string) => {
    if (role === 'E') {
        return '指导专家';
    } else if (role === 'N') {
        return '指导嘉宾';
    } else if (role === 'I') {
        return '发起人';
    }
    return '';
};
</script>

<style lang="scss">
.consultation_order_experts {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}

.consultation_order_look {
    .el-dialog {
        border-radius: 20px;
        border: 1px solid rgba(204, 204, 204, 0.8);
        background: #fff !important;
        box-shadow: 10px 10px 20px 0 rgba(0, 0, 0, 0.15);
        overflow: clip;
    }

    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_edit_header {
        padding: 6.5px 0;
        display: flex;
        justify-content: center;
        color: #333;
        text-align: center;
        font-size: 14px;
    }

    .el-dialog__body {
        padding: 0;

        div {
            color: #333;
            font-size: 14px;
            line-height: 150%;

            .title {
                color: #666;
            }
        }
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
<style lang="scss" scoped>
// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
