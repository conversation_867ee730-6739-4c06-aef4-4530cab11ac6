<template>
    <div class="oss-file-upload">
        <!-- 拖拽上传区域 -->
        <div
            :class="{
        'drag-over': isDragOver,
        'uploading': isUploading,
        'disabled': disabled
      }"
            class="upload-area"
            @click="triggerFileInput"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @dragover="handleDragOver"
            @drop="handleDrop"
        >
            <input
                ref="fileInput"
                :accept="accept"
                :disabled="disabled"
                :multiple="multiple"
                style="display: none"
                type="file"
                @change="handleFileSelect"
            />

            <div class="upload-content">
                <div v-if="!isUploading" class="upload-icon">
                    <el-icon size="48">
                        <UploadFilled />
                    </el-icon>
                </div>

                <div v-if="isUploading" class="upload-progress">
                    <el-progress
                        :percentage="progress"
                        :stroke-width="6"
                        :width="60"
                        type="circle"
                    />
                </div>

                <div class="upload-text">
                    <p v-if="!isUploading" class="main-text">
                        {{ dragText || '点击或拖拽文件到此处上传' }}
                    </p>
                    <p v-else class="main-text">
                        正在上传文件...
                    </p>
                    <p class="sub-text">
                        {{ acceptText || `支持 ${accept || '所有'} 格式文件` }}
                        <span v-if="maxSize">，单个文件不超过 {{ formatFileSize(maxSize) }}</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- 照片墙预览 -->
        <div v-if="fileList.length > 0" class="media-gallery">
            <div
                v-for="(file, index) in fileList"
                :key="index"
                class="media-item"
                :class="{ 'upload-error': file.error, 'uploading': file.uploading }"
            >
                <!-- 图片预览 -->
                <div v-if="isImage(file)" class="media-preview image-preview">
                    <img
                        v-if="file.url"
                        :src="file.url"
                        :alt="file.name"
                        @click="previewFile(file)"
                    />
                    <div v-else class="placeholder">
                        <el-icon size="24"><Picture /></el-icon>
                    </div>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="isVideo(file)" class="media-preview video-preview">
                    <video
                        v-if="file.url"
                        :src="file.url"
                        @click="previewFile(file)"
                        preload="metadata"
                    >
                        您的浏览器不支持视频播放
                    </video>
                    <div v-else class="placeholder">
                        <el-icon size="24"><VideoPlay /></el-icon>
                    </div>
                    <div class="video-overlay">
                        <el-icon size="20"><VideoPlay /></el-icon>
                    </div>
                </div>

                <!-- 上传进度遮罩 -->
                <div v-if="file.uploading" class="upload-overlay">
                    <el-progress
                        type="circle"
                        :percentage="progress"
                        :width="40"
                        :stroke-width="4"
                    />
                </div>

                <!-- 错误状态遮罩 -->
                <div v-if="file.error" class="error-overlay">
                    <el-icon size="24" color="#f56c6c"><CircleClose /></el-icon>
                    <div class="error-text">{{ file.error }}</div>
                </div>

                <!-- 操作按钮 -->
                <div class="media-actions" v-if="!file.uploading">
                    <el-button
                        v-if="file.url && !disabled"
                        @click="previewFile(file)"
                        type="primary"
                        circle
                        size="small"
                    >
                        <el-icon><View /></el-icon>
                    </el-button>

                    <el-button
                        v-if="!disabled"
                        @click="removeFile(index)"
                        type="danger"
                        circle
                        size="small"
                    >
                        <el-icon><Delete /></el-icon>
                    </el-button>
                </div>

                <!-- 文件名 -->
                <div class="media-name">{{ file.name }}</div>
            </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="errorMessage" class="error-message">
            <el-alert
                :closable="false"
                :title="errorMessage"
                show-icon
                type="error"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { UploadFilled, Picture, VideoPlay, CircleClose, View, Delete } from '@element-plus/icons-vue';
import { uploadFileToOSS, type UploadOptions } from '@/utils/oss';

interface FileItem {
    name: string;
    size: number;
    type: string;
    file?: File;
    url?: string;
    uploading?: boolean;
    error?: string;
}

interface Props {
    modelValue?: string[]; // 直接绑定字符串数组（文件URL列表）
    multiple?: boolean;
    accept?: string;
    maxSize?: number; // 字节
    maxFiles?: number;
    disabled?: boolean;
    dragText?: string;
    acceptText?: string;
    uploadOptions?: UploadOptions;
    autoUpload?: boolean;
}

interface Emits {
    (e: 'update:modelValue', value: string[]): void;

    (e: 'upload-success', files: FileItem[]): void;

    (e: 'upload-error', error: string): void;

    (e: 'file-remove', file: FileItem, index: number): void;
}

const props = withDefaults(defineProps<Props>(), {
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    disabled: false,
    autoUpload: true
});

const emit = defineEmits<Emits>();

const fileInput = ref<HTMLInputElement>();
const isDragOver = ref(false);
const isUploading = ref(false);
const progress = ref(0);
const errorMessage = ref('');
const fileList = ref<FileItem[]>([]);

// 计算当前文件的URL数组
const currentUrls = computed(() => {
    return fileList.value.map(file => file.url).filter(Boolean) as string[];
});

// 监听外部值变化，将URL数组转换为FileItem数组
watch(() => props.modelValue, (newValue) => {
    console.log('监听到 modelValue 变化:', newValue);

    // 比较当前URL数组和新值，避免不必要的更新
    const newUrls = (newValue || []).filter(Boolean);
    const current = currentUrls.value;

    // 只有当URL数组真正不同时才更新
    if (JSON.stringify(current.sort()) !== JSON.stringify(newUrls.sort())) {
        console.log('URL数组发生变化，更新 fileList');
        console.log('当前URLs:', current);
        console.log('新URLs:', newUrls);

        if (newUrls.length > 0) {
            fileList.value = newUrls.map((url) => ({
                name: extractFileNameFromUrl(url),
                size: 0, // 已上传的文件无法获取大小
                type: getFileTypeFromUrl(url),
                url: url
            }));
        } else {
            fileList.value = [];
        }
    }
}, { immediate: true });

// 监听URL数组变化，更新modelValue
watch(currentUrls, (newUrls) => {
    console.log('currentUrls 变化，更新 modelValue:', newUrls);
    emit('update:modelValue', newUrls);
});

/**
 * 从URL中提取文件名
 */
function extractFileNameFromUrl(url: string): string {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const fileName = pathname.split('/').pop() || 'unknown';
        return decodeURIComponent(fileName);
    } catch {
        return url.split('/').pop() || 'unknown';
    }
}

/**
 * 从URL中获取文件类型
 */
function getFileTypeFromUrl(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'webp':
            return 'image/*';
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'webm':
            return 'video/*';
        default:
            return 'application/octet-stream';
    }
}

/**
 * 判断是否为图片文件
 */
function isImage(file: FileItem): boolean {
    return file.type.startsWith('image/') ||
           ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(
             file.name.split('.').pop()?.toLowerCase() || ''
           );
}

/**
 * 判断是否为视频文件
 */
function isVideo(file: FileItem): boolean {
    return file.type.startsWith('video/') ||
           ['mp4', 'avi', 'mov', 'wmv', 'webm'].includes(
             file.name.split('.').pop()?.toLowerCase() || ''
           );
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 验证文件
 */
function validateFile(file: File): string | null {
    // 检查文件大小
    if (props.maxSize && file.size > props.maxSize) {
        return `文件大小不能超过 ${formatFileSize(props.maxSize)}`;
    }

    // 检查文件类型
    if (props.accept) {
        const acceptTypes = props.accept.split(',').map(type => type.trim());
        const fileType = file.type;
        const fileName = file.name.toLowerCase();

        const isAccepted = acceptTypes.some(acceptType => {
            if (acceptType.startsWith('.')) {
                return fileName.endsWith(acceptType.toLowerCase());
            } else if (acceptType.includes('*')) {
                const baseType = acceptType.split('/')[0];
                return fileType.startsWith(baseType);
            } else {
                return fileType === acceptType;
            }
        });

        if (!isAccepted) {
            return `不支持的文件类型，仅支持 ${props.accept}`;
        }
    }

    return null;
}

/**
 * 处理文件选择
 */
async function handleFiles(files: File[]) {
    errorMessage.value = '';

    // 检查文件数量限制
    if (!props.multiple && files.length > 1) {
        errorMessage.value = '只能选择一个文件';
        return;
    }

    if (props.maxFiles && fileList.value.length + files.length > props.maxFiles) {
        errorMessage.value = `最多只能上传 ${props.maxFiles} 个文件`;
        return;
    }

    // 验证文件
    const validFiles: File[] = [];
    for (const file of files) {
        const error = validateFile(file);
        if (error) {
            errorMessage.value = error;
            return;
        }
        validFiles.push(file);
    }

    // 添加到文件列表
    const newFileItems: FileItem[] = validFiles.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        file,
        uploading: false
    }));

    if (!props.multiple) {
        fileList.value = newFileItems;
    } else {
        fileList.value.push(...newFileItems);
    }

    // 自动上传
    if (props.autoUpload) {
        await uploadFiles(newFileItems);
    }
}

/**
 * 上传文件到阿里云OSS
 */
async function uploadFiles(files: FileItem[]) {
    if (files.length === 0) return;

    isUploading.value = true;
    progress.value = 0;

    try {
        // 标记文件为上传中
        files.forEach(fileItem => {
            fileItem.uploading = true;
            fileItem.error = undefined;
        });

        // 逐个上传文件
        for (let i = 0; i < files.length; i++) {
            const fileItem = files[i];
            if (!fileItem.file) continue;

            // 在 fileList 中找到对应的文件项
            const fileListIndex = fileList.value.findIndex(f =>
                f.name === fileItem.name &&
                f.size === fileItem.size &&
                f.type === fileItem.type
            );

            if (fileListIndex === -1) {
                console.warn('在 fileList 中找不到对应的文件项:', fileItem);
                continue;
            }

            try {
                const result = await uploadFileToOSS(fileItem.file, {
                    ...props.uploadOptions,
                    onProgress: (p) => {
                        // 计算总体进度
                        const totalProgress = ((i * 100) + p) / files.length;
                        progress.value = Math.round(totalProgress);
                    }
                });

                // 直接更新 fileList 中的对象
                fileList.value[fileListIndex].url = result.url;
                fileList.value[fileListIndex].uploading = false;

                // 同时更新传入的 files 数组（保持一致性）
                fileItem.url = result.url;
                fileItem.uploading = false;

                console.log('文件上传成功，更新 fileList[' + fileListIndex + ']:', fileList.value[fileListIndex]);

            } catch (error) {
                const errorMsg = error instanceof Error ? error.message : '上传失败';

                // 更新两个地方的错误状态
                fileList.value[fileListIndex].uploading = false;
                fileList.value[fileListIndex].error = errorMsg;
                fileItem.uploading = false;
                fileItem.error = errorMsg;

                throw error;
            }
        }

        console.log('上传完成，当前 fileList:', fileList.value);

        emit('upload-success', files);
        ElMessage.success('文件上传成功');

    } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '上传失败';
        emit('upload-error', errorMsg);
        errorMessage.value = errorMsg;

    } finally {
        isUploading.value = false;
        progress.value = 0;
    }
}

/**
 * 触发文件选择
 */
function triggerFileInput() {
    if (!props.disabled && !isUploading.value) {
        fileInput.value?.click();
    }
}

/**
 * 文件选择事件
 */
function handleFileSelect(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = Array.from(target.files || []);
    if (files.length > 0) {
        handleFiles(files);
    }
    // 清空input值，允许重复选择同一文件
    target.value = '';
}

/**
 * 拖拽事件处理
 */
function handleDragOver(event: DragEvent) {
    event.preventDefault();
    if (!props.disabled && !isUploading.value) {
        isDragOver.value = true;
    }
}

function handleDragEnter(event: DragEvent) {
    event.preventDefault();
    if (!props.disabled && !isUploading.value) {
        isDragOver.value = true;
    }
}

function handleDragLeave(event: DragEvent) {
    event.preventDefault();
    // 只有当离开整个拖拽区域时才取消高亮
    if (!event.currentTarget?.contains(event.relatedTarget as Node)) {
        isDragOver.value = false;
    }
}

function handleDrop(event: DragEvent) {
    event.preventDefault();
    isDragOver.value = false;

    if (props.disabled || isUploading.value) return;

    const files = Array.from(event.dataTransfer?.files || []);
    if (files.length > 0) {
        handleFiles(files);
    }
}

/**
 * 移除文件
 */
function removeFile(index: number) {
    const file = fileList.value[index];
    emit('file-remove', file, index);
    fileList.value.splice(index, 1);
}

/**
 * 预览文件
 */
function previewFile(file: FileItem) {
    if (file.url) {
        window.open(file.url, '_blank');
    }
}

/**
 * 手动上传（当autoUpload为false时使用）
 */
function upload() {
    const pendingFiles = fileList.value.filter(file => !file.url && !file.uploading && !file.error);
    if (pendingFiles.length > 0) {
        uploadFiles(pendingFiles);
    }
}

// 暴露方法给父组件
defineExpose({
    upload,
    triggerFileInput
});
</script>

<style scoped>
/* 样式与 OssDirectUpload.vue 相同 */
.oss-file-upload {
    width: 100%;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.upload-area:hover:not(.disabled):not(.uploading) {
    border-color: #409eff;
    background-color: #f0f9ff;
}

.upload-area.drag-over {
    border-color: #409eff;
    background-color: #eff6ff;
    transform: scale(1.02);
}

.upload-area.uploading {
    cursor: not-allowed;
    opacity: 0.7;
}

.upload-area.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #f5f5f5;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.upload-icon {
    color: #6b7280;
}

.upload-text .main-text {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin: 0 0 4px 0;
}

.upload-text .sub-text {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

/* 照片墙样式 */
.media-gallery {
    margin-top: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
}

.media-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e5e7eb;
    background-color: #f9fafb;
    transition: all 0.3s ease;
}

.media-item:hover {
    border-color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.media-item.upload-error {
    border-color: #f56c6c;
}

.media-item.uploading {
    border-color: #409eff;
}

.media-preview {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-preview img:hover {
    transform: scale(1.05);
}

.video-preview {
    position: relative;
}

.video-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    pointer-events: none;
}

.placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    background-color: #f3f4f6;
}

.upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(245, 108, 108, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.error-text {
    font-size: 10px;
    color: #f56c6c;
    text-align: center;
    margin-top: 4px;
    padding: 0 4px;
    word-break: break-all;
}

.media-actions {
    position: absolute;
    top: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.media-item:hover .media-actions {
    opacity: 1;
}

.media-actions .el-button {
    width: 24px;
    height: 24px;
    padding: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.media-actions .el-button--primary {
    background: rgba(64, 158, 255, 0.9);
    border-color: #409eff;
    color: white;
}

.media-actions .el-button--primary:hover {
    background: rgba(64, 158, 255, 1);
}

.media-actions .el-button--danger {
    background: rgba(245, 108, 108, 0.9);
    border-color: #f56c6c;
    color: white;
}

.media-actions .el-button--danger:hover {
    background: rgba(245, 108, 108, 1);
}

.media-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    font-size: 10px;
    padding: 8px 4px 4px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.error-message {
    margin-top: 12px;
}
</style>
