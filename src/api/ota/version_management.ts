import request from '@/utils/request';

// 查询版本管理列表
export function listVersionManagement(query: any) {
    return request({
        url: '/ota/version_management/list',
        method: 'get',
        params: query,
    });
}

// 查询版本管理详细
export function getVersionManagement(id: any) {
    return request({
        url: '/ota/version_management/' + id,
        method: 'get',
    });
}

// 新增版本管理
export function addVersionManagement(data: any) {
    return request({
        url: '/ota/version_management',
        method: 'post',
        data: data,
    });
}

// 修改版本管理
export function updateVersionManagement(data: any) {
    return request({
        url: '/ota/version_management',
        method: 'put',
        data: data,
    });
}

// 删除版本管理
export function delVersionManagement(id: any) {
    return request({
        url: '/ota/version_management/' + id,
        method: 'delete',
    });
}
