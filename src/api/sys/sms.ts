import request from '@/utils/request';

// 查询短信记录列表
export function listSms(query: any) {
    return request({
        url: '/sys/sms/list',
        method: 'get',
        params: query,
    });
}

// 查询短信记录详细
export function getSms(id: any) {
    return request({
        url: '/sys/sms/' + id,
        method: 'get',
    });
}

// 新增短信记录
export function addSms(data: any) {
    return request({
        url: '/sys/sms',
        method: 'post',
        data: data,
    });
}

// 修改短信记录
export function updateSms(data: any) {
    return request({
        url: '/sys/sms',
        method: 'put',
        data: data,
    });
}

// 删除短信记录
export function delSms(id: any) {
    return request({
        url: '/sys/sms/' + id,
        method: 'delete',
    });
}
