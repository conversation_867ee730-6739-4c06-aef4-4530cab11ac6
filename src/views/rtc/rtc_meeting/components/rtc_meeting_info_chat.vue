<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { getTableInfo } from '@/minix/tables';
import Message from './Message.vue';
import { ElMessage } from 'element-plus';
import { shieldRtcMember } from '@/api/rtc/rtc_meeting';
import { RtcChat } from '@/model/rtc_meeting';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps(['meetingId']);
const tableInfo = getTableInfo<RtcChat>(
    '/evms/rtc_meeting/chat/list',
    {
        meetingId: props.meetingId
    },
    true,
    true
);
tableInfo.load();
// 删除是否禁用
const multiple = ref(true);
// 屏蔽是否禁用
const shieldMultiple = ref(true);
const ids = ref<number[]>([]);

// 删除
const deleteBtnLoading = ref(false);
const handleDeleteSel = () => {
    if (ids.value.length) {
        deleteUser(ids.value.join(','));
    }
};
const handleDelete = async (row: any) => {
    deleteUser(row.id.toString());
};
// 删除用户
const deleteUser = async (id: string) => {
    if (!id) {
        ElMessage.error('请选择用户');
        return;
    }
    deleteBtnLoading.value = true;
    // 询问
    // proxy!.$modal.confirm('是否确认删除DMS设备编号为"' + id + '"的数据项？').then(function() {
    //     return delDeviceUser({
    //         deviceId: parseInt(deviceId),
    //         userIdsStr: id
    //     });
    // }).then((res) => {
    //     if (res.code === 200) {
    //         // getList();
    //         proxy!.$modal.msgSuccess('删除成功');
    //         // 重置列表
    //         tableInfo.reset();
    //     }
    // }).catch(() => {
    // });
    deleteBtnLoading.value = false;
};

// 列表选择
const handleSelectionChange = (selection: RtcChat[]) => {
    ids.value = selection.map(item => item.id);
    multiple.value = !selection.length;
    const arr = selection.filter(item => item.status === 0);
    shieldMultiple.value = arr.length !== selection.length;
};
const open = ref(false);
// 屏蔽
const handleShield = (row?: any) => {
    const id = row?.id || ids.value.join(',');
    if (!id) {
        ElMessage.error('请选择用户');
        return;
    }
    //询问
    proxy!.$modal
        .confirm('是否确认屏蔽编号为"' + id + '"的数据项？')
        .then(function() {
            return shieldRtcMember({
                chatIds: id
            });
        })
        .then(res => {
            if (res.code === 200) {
                // getList();
                proxy!.$modal.msgSuccess('删除成功');
                // 重置列表
                tableInfo.reset();
            }
        })
        .catch(() => {
        });
};
// 添加展开折叠控制
const isExpand = ref(false);
</script>

<template>
    <div class="dms_device_info_user">
        <el-form :inline="true" :model="tableInfo.from" label-width="100" size="default">
            <!-- 始终显示的基础搜索项 -->
            <el-form-item label="参会人ID">
                <el-input
                    v-model="tableInfo.from.fromId"
                    placeholder="请输入ID"
                    style="width: 240px"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>
            <el-form-item label="参会人姓名">
                <el-input
                    v-model="tableInfo.from.like_fromName"
                    placeholder="请输入用户名"
                    style="width: 240px"
                    @keyup.enter="tableInfo.search"
                ></el-input>
            </el-form-item>

            <!-- 可折叠的高级搜索项 -->
            <template v-if="isExpand">
                <el-form-item label="聊天关键词">
                    <el-input
                        v-model="tableInfo.from.like_message"
                        placeholder="请输入关键词"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    ></el-input>
                </el-form-item>
                <el-form-item label="屏蔽状态">
                    <el-select
                        v-model="tableInfo.from.status"
                        clearable
                        placeholder="请选择屏蔽状态"
                        style="width: 240px"
                    >
                        <el-option :value="0" label="未屏蔽" />
                        <el-option :value="1" label="已屏蔽" />
                    </el-select>
                </el-form-item>
            </template>

            <!-- 搜索按钮区域 -->
            <el-row :gutter="30" justify="start" style="margin-top: 10px">
                <el-col :span="12">
                    <el-button icon="Search" type="primary" @click="tableInfo.search">搜索</el-button>
                    <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                    <el-button type="text" @click="isExpand = !isExpand">
                        {{ isExpand ? '收起' : '展开' }}
                        <el-icon class="el-icon--right">
                            <arrow-up v-if="isExpand" />
                            <arrow-down v-else />
                        </el-icon>
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8" style="margin-top: 20px">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['evms:dms_hospital:add']"
                    :disabled="shieldMultiple"
                    color="#009dff"
                    icon="Plus"
                    plain
                    @click="handleShield"
                >屏蔽
                </el-button>
            </el-col>
            <!--            <el-col :span="1.5">-->
            <!--                <el-button-->
            <!--                    color="#ff5c00"-->
            <!--                    plain-->
            <!--                    icon="Delete"-->
            <!--                    :disabled="multiple"-->
            <!--                    @click="handleDeleteSel"-->
            <!--                    v-hasPermi="['evms:dms_hospital:remove']"-->
            <!--                    :loading="deleteBtnLoading"-->
            <!--                >删除-->
            <!--                </el-button>-->
            <!--            </el-col>-->
            <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
        </el-row>
        <el-table
            :data="tableInfo.list.value"
            height="450"
            row-key="id"
            style="width: 100%; margin-top: 20px"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="ID" prop="id" width="140"></el-table-column>
            <el-table-column label="参会人ID" prop="fromId" width="120"></el-table-column>
            <el-table-column label="参会人姓名" prop="fromName" width="120"></el-table-column>
            <el-table-column label="聊天内容" prop="message">
                <template #default="{ row }">
                    <el-popover v-if="row.type !== 10012" trigger="hover" width="300">
                        <Message :is-line="false" :message="row.message" :type="row.type"></Message>
                        <template #reference>
                            <!--                            <div style="width: 200px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">点击查看</div>-->
                            <Message
                                v-if="row.type !== 10012"
                                :is-line="true"
                                :message="row.message"
                                :type="row.type"
                            ></Message>
                        </template>
                    </el-popover>
                    <span v-else></span>
                </template>
            </el-table-column>
            <el-table-column label="时间" prop="eventTime"></el-table-column>
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-if="scope.row.status < 2"
                        v-hasPermi="['rtc:rtc_meeting:shield']"
                        :disabled="scope.row.status === 1"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleShield(scope.row)"
                    >{{ scope.row.status > 0 ? '已屏蔽' : '屏蔽' }}
                    </el-button>
                    <!--                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"-->
                    <!--                               v-hasPermi="['rtc:rtc_meeting:remove']" :loading="deleteBtnLoading">删除-->
                    <!--                    </el-button>-->
                </template>
            </el-table-column>
        </el-table>
        <el-row align="bottom" justify="space-between">
            <div>共 {{ tableInfo.total }} 项数据</div>
            <pagination
                v-show="tableInfo.total.value > 0"
                v-model:limit="tableInfo.pageParam.pageSize"
                v-model:page="tableInfo.pageParam.pageNum"
                :total="tableInfo.total.value"
                @pagination="tableInfo.pagination"
            />
        </el-row>
    </div>
</template>

<style lang="scss">
.dms_device_info_user_dialog,
.dms_device_info_user_edit {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}

.dms_device_info_user_edit {
    .el-dialog {
        border-radius: 20px;
        border: 1px solid rgba(204, 204, 204, 0.8);
        background: #f8f9fb;
        box-shadow: 10px 10px 20px 0 rgba(0, 0, 0, 0.15);
        overflow: clip;
    }

    .dialog_edit_header {
        padding: 6.5px 0;
        display: flex;
        justify-content: center;
        color: #333;
        text-align: center;
        font-size: 14px;
        background-color: #fff;
    }

    .el-dialog__headerbtn {
        top: 4.5px;
    }

    .el-form-item__content {
        justify-content: end;
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-form-item {
        padding: 20px 30px 12px;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: none;
        }
    }

    .el-form-item--default {
        margin: 0;
    }

    .el-form-item__label {
        color: #666;
        font-size: 14px;
        line-height: 150%; /* 21px */
        height: 21px;
    }

    .el-switch {
        height: 18px;
    }

    .dialog-footer {
        padding-top: 30px;
        display: flex;
        justify-content: center;

        .el-button {
            padding: 5px 36px;
            border-radius: 15px;
        }
    }
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
