<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
            <el-form-item label="应用程序类型">
                <el-select
                    v-model="queryParams.applicationProgram"
                    clearable
                    placeholder="请选择应用程序类型"
                >
                    <el-option
                        v-for="dict in evms_client_version_application_program"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="平台类型">
                <el-select v-model="queryParams.platform" clearable placeholder="请选择平台类型">
                    <el-option
                        v-for="dict in evms_client_version_platform"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="版本号" prop="version">
                <el-input
                    v-model="queryParams.version"
                    clearable
                    placeholder="请输入版本号"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="升级包MD5签名" prop="signature">
                <el-input
                    v-model="queryParams.signature"
                    clearable
                    placeholder="请输入升级包MD5签名"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" clearable placeholder="请选择状态">
                    <el-option
                        v-for="dict in evms_client_version_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="版本code" prop="code">
                <el-input
                    v-model="queryParams.code"
                    clearable
                    placeholder="请输入版本code，数字越大代表最新版本"
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="升级类型" prop="type">
                <el-select v-model="queryParams.type" clearable placeholder="请选择升级类型">
                    <el-option
                        v-for="dict in evms_client_version_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:client_version:add']"
                    icon="Plus"
                    plain
                    type="primary"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:client_version:edit']"
                    :disabled="single"
                    icon="Edit"
                    plain
                    type="success"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:client_version:remove']"
                    :disabled="multiple"
                    icon="Delete"
                    plain
                    type="danger"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['ota:client_version:export']"
                    icon="Download"
                    plain
                    type="warning"
                    @click="handleExport"
                >导出
                </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="ClientVersionList" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" />
            <el-table-column align="center" label="应用程序类型" prop="applicationProgram">
                <template #default="scope">
                    <dict-tag
                        :options="evms_client_version_application_program"
                        :value="scope.row.applicationProgram"
                    />
                </template>
            </el-table-column>
            <el-table-column align="center" label="平台类型" prop="platform">
                <template #default="scope">
                    <dict-tag :options="evms_client_version_platform" :value="scope.row.platform" />
                </template>
            </el-table-column>
            <el-table-column align="center" label="版本号" prop="version" />
            <el-table-column align="center" label="升级包MD5签名" prop="signature" />
            <el-table-column align="center" label="状态" prop="status">
                <template #default="scope">
                    <dict-tag :options="evms_client_version_status" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column align="center" label="版本code" prop="code" />
            <el-table-column align="center" label="升级包地址" prop="url" />
            <el-table-column align="center" label="升级类型" prop="type">
                <template #default="scope">
                    <dict-tag :options="evms_client_version_type" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column align="center" label="更新说明" prop="description" />
            <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['ota:client_version:edit']"
                        icon="Edit"
                        link
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>
                    <el-button
                        v-hasPermi="['ota:client_version:remove']"
                        icon="Delete"
                        link
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改版本升级对话框 -->
        <el-dialog v-model="open" :destroy-on-close="true" :title="title" append-to-body width="800px">
            <el-form ref="ClientVersionRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="应用程序类型">
                    <el-radio-group v-model="form.applicationProgram">
                        <el-radio
                            v-for="dict in evms_client_version_application_program"
                            :key="dict.value"
                            :label="dict.value"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="平台类型">
                    <el-radio-group v-model="form.platform">
                        <el-radio
                            v-for="dict in evms_client_version_platform"
                            :key="dict.value"
                            :label="dict.value"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="版本号" prop="version">
                    <el-input v-model="form.version" placeholder="请输入版本号" />
                </el-form-item>
                <el-form-item label="MD5签名" prop="signature">
                    <el-input
                        v-model="form.signature"
                        :disabled="true"
                        placeholder="上传文件后自动填充，不需输入"
                    />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio
                            v-for="dict in evms_client_version_status"
                            :key="dict.value"
                            :label="parseInt(dict.value)"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="版本code" prop="code">
                    <el-input v-model="form.code" :placeholder="codePlaceholder" />
                </el-form-item>
                <el-form-item label="升级包url" prop="url">
                    <file-upload v-model="form.url" v-model:signature="form.signature" />
                </el-form-item>
                <el-form-item label="升级类型" prop="type">
                    <el-radio-group v-model="form.type">
                        <el-radio
                            v-for="dict in evms_client_version_type"
                            :key="dict.value"
                            :label="parseInt(dict.value)"
                        >{{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="更新说明" prop="description">
                    <el-input v-model="form.description" placeholder="请输入内容" type="textarea" />
                </el-form-item>
                <el-form-item label="更新说明（英文）" prop="enDescription">
                    <el-input v-model="form.enDescription" placeholder="请输入英文内容" type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" name="ClientVersion" setup>
import {
    addClientVersion,
    delClientVersion,
    getClientVersion,
    getVersionCodePlaceHolder,
    listClientVersion,
    updateClientVersion
} from '@/api/ota/client_version';
import { queryFormat } from '@/utils/ruoyi';
import { ComponentInternalInstance, getCurrentInstance, reactive, ref, toRefs, watch } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
    evms_client_version_status,
    evms_client_version_platform,
    evms_client_version_type,
    evms_client_version_application_program
} = proxy!.useDict(
    'evms_client_version_status',
    'evms_client_version_platform',
    'evms_client_version_type',
    'evms_client_version_application_program'
);

const ClientVersionList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        platform: undefined,
        applicationProgram: undefined,
        version: undefined,
        signature: undefined,
        status: undefined,
        code: undefined,
        url: undefined,
        type: undefined,
        description: undefined,
        enDescription: undefined
    },
    rules: {
        version: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
        // code: [
        //     { required: true, message: "code不能为空", trigger: "blur" }
        // ],
        url: [{ required: true, message: '升级包url不能为空', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询版本升级列表 */
function getList() {
    loading.value = true;

    listClientVersion(queryFormat(queryParams.value)).then((response: any) => {
        ClientVersionList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        platform: undefined,
        version: undefined,
        signature: undefined,
        status: 0,
        code: undefined,
        url: undefined,
        type: 0,
        description: undefined,
        enDescription: undefined,
        createBy: undefined,
        updateBy: undefined,
        createTime: undefined,
        updateTime: undefined,
        applicationProgram: undefined
    };
    proxy!.resetForm('ClientVersionRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
const isAdd = ref(false);

function handleAdd() {
    isAdd.value = true;
    reset();
    open.value = true;
    title.value = '添加版本升级';
}

const codePlaceholder = ref('请输入版本code，数字越大代表最新版本');
// 新增时监听表单数据 修改版本code的提示词
watch(
    [() => form.value.applicationProgram, () => form.value.platform],
    async val => {
        if (isAdd.value) {
            if (form.value.applicationProgram && form.value.platform) {
                // 获取
                const res = await getVersionCodePlaceHolder({
                    platform: form.value.platform,
                    applicationProgram: form.value.applicationProgram
                });
                if (res.code === 200) {
                    if (res.data.lastCode) {
                        isAdd.value && (form.value.code = parseInt(res.data.lastCode) + 1);
                        codePlaceholder.value = `当前已发布最大版本code为${res.data.lastCode}`;
                    } else {
                        codePlaceholder.value = '请输入版本code，数字越大代表最新版本';
                    }
                }
            }
        }
    },
    { deep: true }
);

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getClientVersion(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改版本升级';
    });
}

/** 提交按钮 */
function submitForm() {
    const app = evms_client_version_application_program.value.filter(
        opt => opt.value == form.value.applicationProgram
    );
    const pf = evms_client_version_platform.value.filter(opt => opt.value == form.value.platform);

    (proxy?.$refs['ClientVersionRef'] as any).validate((valid: any) => {
        if (valid) {
            if (!form.value.applicationProgram) {
                proxy!.$modal.alertError('请选择应用程序类型！');
                return;
            }
            if (!form.value.platform) {
                proxy!.$modal.alertError('请选择平台！');
                return;
            }
            proxy!.$modal
                .confirmHtml(
                    '<div>您选择的应用程序类型为：<div style="font-weight: bold;color: red;">' +
                    app[0].label +
                    '</div></div><div>您选择的平台类型为：<div style="font-weight: bold;color: red;">' +
                    pf[0].label +
                    '</div></div><div>版本号为：<div style="font-weight: bold;color: red;">' +
                    form.value.version +
                    '</div></div><div>版本code为：<div style="font-weight: bold;color: red;">' +
                    form.value.code +
                    '</div></div>'
                )
                .then(function() {
                    if (form.value.id != null) {
                        updateClientVersion(form.value).then((response: any) => {
                            proxy!.$modal.msgSuccess('修改成功');
                            open.value = false;
                            getList();
                        });
                    } else {
                        addClientVersion(form.value).then((response: any) => {
                            proxy!.$modal.msgSuccess('新增成功');
                            open.value = false;
                            getList();
                            isAdd.value = false;
                        });
                    }
                })
                .catch(() => {
                });
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除版本升级编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delClientVersion(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'ota/ClientVersion/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `ClientVersion_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
