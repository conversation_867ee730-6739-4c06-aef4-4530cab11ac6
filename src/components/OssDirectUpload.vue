<template>
    <div class="oss-direct-upload">
        <!-- 拖拽上传区域 -->
        <div
            :class="{
        'drag-over': isDragOver,
        'uploading': isUploading,
        'disabled': disabled
      }"
            class="upload-area"
            @click="triggerFileInput"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @dragover="handleDragOver"
            @drop="handleDrop"
        >
            <input
                ref="fileInput"
                :accept="accept"
                :disabled="disabled"
                :multiple="multiple"
                style="display: none"
                type="file"
                @change="handleFileSelect"
            />

            <div class="upload-content">
                <div v-if="!isUploading" class="upload-icon">
                    <el-icon size="48">
                        <UploadFilled />
                    </el-icon>
                </div>

                <div v-if="isUploading" class="upload-progress">
                    <el-progress
                        :percentage="progress"
                        :stroke-width="6"
                        :width="60"
                        type="circle"
                    />
                </div>

                <div class="upload-text">
                    <p v-if="!isUploading" class="main-text">
                        {{ dragText || '点击或拖拽文件到此处上传' }}
                    </p>
                    <p v-else class="main-text">
                        正在上传文件...
                    </p>
                    <p class="sub-text">
                        {{ acceptText || `支持 ${accept || '所有'} 格式文件` }}
                        <span v-if="maxSize">，单个文件不超过 {{ formatFileSize(maxSize) }}</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- 文件列表 -->
        <div v-if="fileList.length > 0" class="file-list">
            <div
                v-for="(file, index) in fileList"
                :key="index"
                :class="{ 'upload-error': file.error }"
                class="file-item"
            >
                <div class="file-info">
                    <div class="file-icon">
                        <el-icon size="20">
                            <Document />
                        </el-icon>
                    </div>
                    <div class="file-details">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="file-meta">
                            {{ formatFileSize(file.size) }}
                            <span v-if="file.url" class="file-status success">✓ 上传成功</span>
                            <span v-else-if="file.error" class="file-status error">✗ {{ file.error }}</span>
                            <span v-else-if="file.uploading" class="file-status uploading">上传中...</span>
                        </div>
                    </div>
                </div>

                <div class="file-actions">
                    <el-button
                        v-if="file.url && !disabled"
                        link
                        size="small"
                        type="primary"
                        @click="previewFile(file)"
                    >
                        预览
                    </el-button>

                    <el-button
                        v-if="!disabled"
                        link
                        size="small"
                        type="danger"
                        @click="removeFile(index)"
                    >
                        删除
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="errorMessage" class="error-message">
            <el-alert
                :closable="false"
                :title="errorMessage"
                show-icon
                type="error"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Document, UploadFilled } from '@element-plus/icons-vue';
import { uploadFileToOSS, type UploadOptions } from '@/utils/oss';

interface FileItem {
    name: string;
    size: number;
    type: string;
    file?: File;
    url?: string;
    uploading?: boolean;
    error?: string;
}

interface Props {
    modelValue?: FileItem[];
    multiple?: boolean;
    accept?: string;
    maxSize?: number; // 字节
    maxFiles?: number;
    disabled?: boolean;
    dragText?: string;
    acceptText?: string;
    uploadOptions?: UploadOptions;
    autoUpload?: boolean;
}

interface Emits {
    (e: 'update:modelValue', value: FileItem[]): void;

    (e: 'upload-success', files: FileItem[]): void;

    (e: 'upload-error', error: string): void;

    (e: 'file-remove', file: FileItem, index: number): void;
}

const props = withDefaults(defineProps<Props>(), {
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    disabled: false,
    autoUpload: true
});

const emit = defineEmits<Emits>();

const fileInput = ref<HTMLInputElement>();
const isDragOver = ref(false);
const isUploading = ref(false);
const progress = ref(0);
const errorMessage = ref('');
const fileList = ref<FileItem[]>(props.modelValue || []);

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        fileList.value = [...newValue];
    }
}, { deep: true });

// 监听文件列表变化
watch(fileList, (newValue) => {
    emit('update:modelValue', newValue);
}, { deep: true });

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 验证文件
 */
function validateFile(file: File): string | null {
    // 检查文件大小
    if (props.maxSize && file.size > props.maxSize) {
        return `文件大小不能超过 ${formatFileSize(props.maxSize)}`;
    }

    // 检查文件类型
    if (props.accept) {
        const acceptTypes = props.accept.split(',').map(type => type.trim());
        const fileType = file.type;
        const fileName = file.name.toLowerCase();

        const isAccepted = acceptTypes.some(acceptType => {
            if (acceptType.startsWith('.')) {
                return fileName.endsWith(acceptType.toLowerCase());
            } else if (acceptType.includes('*')) {
                const baseType = acceptType.split('/')[0];
                return fileType.startsWith(baseType);
            } else {
                return fileType === acceptType;
            }
        });

        if (!isAccepted) {
            return `不支持的文件类型，仅支持 ${props.accept}`;
        }
    }

    return null;
}

/**
 * 处理文件选择
 */
async function handleFiles(files: File[]) {
    errorMessage.value = '';

    // 检查文件数量限制
    if (!props.multiple && files.length > 1) {
        errorMessage.value = '只能选择一个文件';
        return;
    }

    if (props.maxFiles && fileList.value.length + files.length > props.maxFiles) {
        errorMessage.value = `最多只能上传 ${props.maxFiles} 个文件`;
        return;
    }

    // 验证文件
    const validFiles: File[] = [];
    for (const file of files) {
        const error = validateFile(file);
        if (error) {
            errorMessage.value = error;
            return;
        }
        validFiles.push(file);
    }

    // 添加到文件列表
    const newFileItems: FileItem[] = validFiles.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        file,
        uploading: false
    }));

    if (!props.multiple) {
        fileList.value = newFileItems;
    } else {
        fileList.value.push(...newFileItems);
    }

    // 自动上传
    if (props.autoUpload) {
        await uploadFiles(newFileItems);
    }
}

/**
 * 上传文件到阿里云OSS
 */
async function uploadFiles(files: FileItem[]) {
    if (files.length === 0) return;

    isUploading.value = true;
    progress.value = 0;

    try {
        // 标记文件为上传中
        files.forEach(fileItem => {
            fileItem.uploading = true;
            fileItem.error = undefined;
        });

        // 逐个上传文件
        for (let i = 0; i < files.length; i++) {
            const fileItem = files[i];
            if (!fileItem.file) continue;

            try {
                const result = await uploadFileToOSS(fileItem.file, {
                    ...props.uploadOptions,
                    onProgress: (p) => {
                        // 计算总体进度
                        const totalProgress = ((i * 100) + p) / files.length;
                        progress.value = Math.round(totalProgress);
                    }
                });

                fileItem.url = result.url;
                fileItem.uploading = false;

            } catch (error) {
                const errorMsg = error instanceof Error ? error.message : '上传失败';
                fileItem.uploading = false;
                fileItem.error = errorMsg;
                throw error;
            }
        }

        emit('upload-success', files);
        ElMessage.success('文件上传成功');

    } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '上传失败';
        emit('upload-error', errorMsg);
        errorMessage.value = errorMsg;

    } finally {
        isUploading.value = false;
        progress.value = 0;
    }
}

/**
 * 触发文件选择
 */
function triggerFileInput() {
    if (!props.disabled && !isUploading.value) {
        fileInput.value?.click();
    }
}

/**
 * 文件选择事件
 */
function handleFileSelect(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = Array.from(target.files || []);
    if (files.length > 0) {
        handleFiles(files);
    }
    // 清空input值，允许重复选择同一文件
    target.value = '';
}

/**
 * 拖拽事件处理
 */
function handleDragOver(event: DragEvent) {
    event.preventDefault();
    if (!props.disabled && !isUploading.value) {
        isDragOver.value = true;
    }
}

function handleDragEnter(event: DragEvent) {
    event.preventDefault();
    if (!props.disabled && !isUploading.value) {
        isDragOver.value = true;
    }
}

function handleDragLeave(event: DragEvent) {
    event.preventDefault();
    // 只有当离开整个拖拽区域时才取消高亮
    if (!event.currentTarget?.contains(event.relatedTarget as Node)) {
        isDragOver.value = false;
    }
}

function handleDrop(event: DragEvent) {
    event.preventDefault();
    isDragOver.value = false;

    if (props.disabled || isUploading.value) return;

    const files = Array.from(event.dataTransfer?.files || []);
    if (files.length > 0) {
        handleFiles(files);
    }
}

/**
 * 移除文件
 */
function removeFile(index: number) {
    const file = fileList.value[index];
    emit('file-remove', file, index);
    fileList.value.splice(index, 1);
}

/**
 * 预览文件
 */
function previewFile(file: FileItem) {
    if (file.url) {
        window.open(file.url, '_blank');
    }
}

/**
 * 手动上传（当autoUpload为false时使用）
 */
function upload() {
    const pendingFiles = fileList.value.filter(file => !file.url && !file.uploading && !file.error);
    if (pendingFiles.length > 0) {
        uploadFiles(pendingFiles);
    }
}

// 暴露方法给父组件
defineExpose({
    upload,
    triggerFileInput
});
</script>

<style scoped>
.oss-direct-upload {
    width: 100%;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.upload-area:hover:not(.disabled):not(.uploading) {
    border-color: #409eff;
    background-color: #f0f9ff;
}

.upload-area.drag-over {
    border-color: #409eff;
    background-color: #eff6ff;
    transform: scale(1.02);
}

.upload-area.uploading {
    cursor: not-allowed;
    opacity: 0.7;
}

.upload-area.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #f5f5f5;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.upload-icon {
    color: #6b7280;
}

.upload-text .main-text {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin: 0 0 4px 0;
}

.upload-text .sub-text {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.file-list {
    margin-top: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: white;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item.upload-error {
    background-color: #fef2f2;
    border-color: #fecaca;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-icon {
    color: #6b7280;
    flex-shrink: 0;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-status {
    font-weight: 500;
}

.file-status.success {
    color: #059669;
}

.file-status.error {
    color: #dc2626;
}

.file-status.uploading {
    color: #3b82f6;
}

.file-actions {
    display: flex;
    gap: 8px;
}

.error-message {
    margin-top: 12px;
}
</style>
