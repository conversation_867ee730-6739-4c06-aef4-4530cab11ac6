export interface DmsHospitalInfo {
    id?: number;
    createBy: null;
    updateBy: null;
    createTime: string;
    updateTime: string;
    name: string;
    enName?: string; // 英文名称，非必填
    type: number;
    area: null;
    province: null;
    city: null;
    address: null;
    description: null;
    areaArray?: string[]; // 前端自定义字段 用于三级联动的表单校验使用
}
export interface DmsHospitalListResponse {
    total: number;
    rows: DmsHospitalInfo[];
    current: number;
    size: number;
}
// 根据关键词查询医院列表参数
export interface HospitalListParams {
    keywords: string; // 关键词
}
