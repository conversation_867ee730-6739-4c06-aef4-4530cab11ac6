import request from '@/utils/request';

// 查询EBS素材库列表
export function listMaterial(query: any) {
    return request({
        url: '/evms/material/list',
        method: 'get',
        params: query,
    });
}

// 查询EBS素材库详细
export function getMaterial(id: any) {
    return request({
        url: '/evms/material/' + id,
        method: 'get',
    });
}

// 新增EBS素材库
export function addMaterial(data: any) {
    return request({
        url: '/evms/material',
        method: 'post',
        data: data,
    });
}

// 修改EBS素材库
export function updateMaterial(data: any) {
    return request({
        url: '/evms/material',
        method: 'put',
        data: data,
    });
}

// 删除EBS素材库
export function delMaterial(id: any) {
    return request({
        url: '/evms/material/' + id,
        method: 'delete',
    });
}
