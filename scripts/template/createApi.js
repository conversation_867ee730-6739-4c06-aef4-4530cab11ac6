import { createDaoType } from './createTemplate.js';

export function createApi(genTable) {
    let functionName = genTable.functionName;
    let moduleName = genTable.moduleName;
    let businessName = genTable.businessName;
    let BusinessName = genTable.BusinessName;
    return `
import request from '@/utils/request';
interface Response<T> {
    msg: string;
    code: number;
    data: T;
    timestamp: number;
    success: boolean;
}
interface PageResponse<T> {
    rows: Array<T>;
    total: number;
    pages: number;
}
export interface ${genTable.className} {
    ${createDaoType(genTable.columns)}
}
// 查询${functionName}列表
export function list${BusinessName}(query: any) {
    return request({
        url: '/${moduleName}/${businessName}/list',
        method: 'get',
        params: query,
    }) as unknown as  Promise<Response<PageResponse<${genTable.className}>>>;
}

// 查询${functionName}详细
export function get${BusinessName}(id: any){
    return request({
        url: '/${moduleName}/${businessName}/' + id,
        method: 'get',
    }) as unknown as Promise<Response<${genTable.className}>> ;
}

// 新增${functionName}
export function add${BusinessName}(data: ${genTable.className}) {
    return request({
        url: '/${moduleName}/${businessName}',
        method: 'post',
        data: data,
    }) as unknown as Promise<Response<${genTable.className}>>;
}

// 修改${functionName}
export function update${BusinessName}(data: ${genTable.className}) {
    return request({
        url: '/${moduleName}/${businessName}',
        method: 'put',
        data: data,
    }) as unknown as Promise<Response<${genTable.className}>>;
}

// 删除${functionName}
export function del${BusinessName}(id: any){
    return request({
        url: '/${moduleName}/${businessName}/' + id,
        method: 'delete',
    }) as unknown as Promise<Response<${genTable.className}>> ;
}
        
`;
}
