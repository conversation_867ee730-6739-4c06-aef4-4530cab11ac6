import request from '@/utils/request';

export function getOtherInfoById(deviceId: number) {
    const data = {
        id: deviceId
    };
    return request({
        url: '/evms/device/other/getOtherInfoById',
        method: 'get',
        params: data,
    });
}

export function addOtherInfo(data: any) {
    return request({
        url: '/evms/device/other',
        method: 'post',
        data: data,
    });
}
