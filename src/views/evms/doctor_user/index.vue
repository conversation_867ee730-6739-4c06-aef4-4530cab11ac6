<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="用户名" prop="username">
                <el-input
                    v-model="queryParams.username"
                    placeholder="请输入用户名"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="真实姓名" prop="realname">
                <el-input
                    v-model="queryParams.realname"
                    placeholder="请输入真实姓名"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <!-- 可折叠的高级搜索项 -->
            <template v-if="isExpand">
                <el-form-item label="用户头像" prop="avatar">
                    <el-input
                        v-model="queryParams.avatar"
                        placeholder="请输入用户头像"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="职称" prop="title">
                    <el-input
                        v-model="queryParams.title"
                        placeholder="请输入职称"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="公司" prop="company">
                    <el-input
                        v-model="queryParams.company"
                        placeholder="请输入公司"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="部门" prop="department">
                    <el-input
                        v-model="queryParams.department"
                        placeholder="请输入部门"
                        clearable
                        style="width: 240px"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item label="用户身份" prop="identity">
                    <el-select v-model="queryParams.identity" placeholder="请选择用户身份" clearable>
                        <el-option
                            v-for="dict in evms_doctor_identity"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="对患认证状态" prop="statusForPatient">
                    <el-select
                        v-model="queryParams.statusForPatient"
                        placeholder="请选择对患认证状态"
                        clearable
                    >
                        <el-option
                            v-for="dict in evms_doctor_user_statusForPatient"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
                        <el-option
                            v-for="dict in sys_user_sex"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="角色" prop="role">
                    <el-select v-model="queryParams.role" placeholder="请选择角色" clearable>
                        <el-option
                            v-for="dict in evms_doctor_role"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
            </template>

            <!-- 搜索按钮区域 -->
            <el-row justify="start" :gutter="20" style="width: 100%; margin-top: 10px">
                <el-col :span="12" style="padding-left: 50px">
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    <el-button type="text" @click="isExpand = !isExpand">
                        {{ isExpand ? '收起' : '展开' }}
                        <el-icon class="el-icon--right">
                            <arrow-up v-if="isExpand" />
                            <arrow-down v-else />
                        </el-icon>
                    </el-button>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['evms:doctor_user:add']"
                >新增
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['evms:doctor_user:edit']"
                >修改
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['evms:doctor_user:remove']"
                >删除
                </el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['evms:doctor_user:export']"
                >导出
                </el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="DoctorUserList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户名" align="center" prop="username" />
            <el-table-column label="真实姓名" align="center" prop="realname" />
            <el-table-column label="用户头像" align="center" prop="avatar" />
            <el-table-column label="职称" align="center" prop="title" />
            <el-table-column label="公司" align="center" prop="company" />
            <el-table-column label="部门" align="center" prop="department" />
            <el-table-column label="用户身份" align="center" prop="identity">
                <template #default="scope">
                    <dict-tag :options="evms_doctor_identity" :value="scope.row.identity" />
                </template>
            </el-table-column>
            <el-table-column label="对患认证状态" align="center" prop="statusForPatient">
                <template #default="scope">
                    <dict-tag
                        :options="evms_doctor_user_statusForPatient"
                        :value="scope.row.statusForPatient"
                    />
                </template>
            </el-table-column>
            <el-table-column label="性别" align="center" prop="gender">
                <template #default="scope">
                    <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
                </template>
            </el-table-column>
            <el-table-column label="角色" align="center" prop="role">
                <template #default="scope">
                    <dict-tag :options="evms_doctor_role" :value="scope.row.role" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['evms:doctor_user:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['evms:doctor_user:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 添加或修改医生用户信息对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="DoctorUserRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="form.username" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item label="真实姓名" prop="realname">
                    <el-input v-model="form.realname" placeholder="请输入真实姓名" />
                </el-form-item>
                <el-form-item label="用户头像" prop="avatar">
                    <el-input v-model="form.avatar" placeholder="请输入用户头像" />
                </el-form-item>
                <el-form-item label="职称" prop="title">
                    <el-input v-model="form.title" placeholder="请输入职称" />
                </el-form-item>
                <el-form-item label="公司" prop="company">
                    <el-input v-model="form.company" placeholder="请输入公司" />
                </el-form-item>
                <el-form-item label="部门" prop="department">
                    <el-input v-model="form.department" placeholder="请输入部门" />
                </el-form-item>
                <el-form-item label="用户身份" prop="identity">
                    <el-select v-model="form.identity" placeholder="请选择用户身份">
                        <el-option
                            v-for="dict in evms_doctor_identity"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="对患认证状态" prop="statusForPatient">
                    <el-select v-model="form.statusForPatient" placeholder="请选择对患认证状态">
                        <el-option
                            v-for="dict in evms_doctor_user_statusForPatient"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-select v-model="form.gender" placeholder="请选择性别">
                        <el-option
                            v-for="dict in sys_user_sex"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="角色" prop="role">
                    <el-select v-model="form.role" placeholder="请选择角色">
                        <el-option
                            v-for="dict in evms_doctor_role"
                            :key="dict.value"
                            :label="dict.label"
                            :value="parseInt(dict.value)"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="DoctorUser" lang="ts">
import {
    listDoctorUser,
    getDoctorUser,
    delDoctorUser,
    addDoctorUser,
    updateDoctorUser
} from '@/api/evms/doctor_user';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';

// 添加展开折叠控制
const isExpand = ref(false);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { evms_doctor_user_statusForPatient, sys_user_sex, evms_doctor_role, evms_doctor_identity } =
    proxy!.useDict(
        'evms_doctor_user_statusForPatient',
        'sys_user_sex',
        'evms_doctor_role',
        'evms_doctor_identity'
    );

const DoctorUserList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: undefined,
        realname: undefined,
        avatar: undefined,
        title: undefined,
        company: undefined,
        department: undefined,
        identity: undefined,
        statusForPatient: undefined,
        gender: undefined,
        role: undefined
    },
    rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 查询医生用户信息列表 */
function getList() {
    loading.value = true;

    listDoctorUser(queryFormat(queryParams.value)).then((response: any) => {
        DoctorUserList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: undefined,
        username: undefined,
        realname: undefined,
        avatar: undefined,
        title: undefined,
        company: undefined,
        department: undefined,
        identity: undefined,
        statusForPatient: undefined,
        gender: undefined,
        role: undefined
    };
    proxy!.resetForm('DoctorUserRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy!.resetForm('queryRef');
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加医生用户信息';
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
    reset();
    const _id = row.id || ids.value;
    getDoctorUser(_id).then((response: any) => {
        form.value = response.data;
        open.value = true;
        title.value = '修改医生用户信息';
    });
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['DoctorUserRef'] as any).validate((valid: any) => {
        if (valid) {
            if (form.value.id != null) {
                updateDoctorUser(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('修改成功');
                    open.value = false;
                    getList();
                });
            } else {
                addDoctorUser(form.value).then((response: any) => {
                    proxy!.$modal.msgSuccess('新增成功');
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除医生用户信息编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delDoctorUser(_ids);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        })
        .catch(() => {
        });
}

/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'evms/DoctorUser/exportExcel',
        {
            ...queryFormat(queryParams.value)
        },
        `DoctorUser_${new Date().getTime()}#.xlsx`
    );
}

getList();
</script>
<style lang="scss" scoped>
.app-container {
    background: #f8f9fb;
}

// 添加展开按钮样式
:deep(.el-button--text) {
    margin-left: 8px;
    padding: 0;
    height: 32px;
    line-height: 32px;
}

// 调整表单项间距
:deep(.el-form--inline .el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;
}
</style>
