import { MemoryStorage, WebStorage, MemoryStorageType } from './storage';

const _global = typeof window !== 'undefined' ? window : global || {};
import { storageOptions, sessionOptions } from '@/defaultSettings';

export function createStorage(options: { storage: string; name: string }): WebStorage {
    const _options = Object.assign({}, options, {
        storage: options.storage || 'local',
        name: options.name || 'ls',
    });

    if (_options.storage && ['memory', 'local', 'session'].indexOf(_options.storage) === -1) {
        throw new Error(`Vue-ls: Storage "${_options.storage}" is not supported`);
    }

    let store: unknown = null;
    switch (
        _options.storage // eslint-disable-line
        ) {
        case 'local':
            store = 'localStorage' in _global ? _global.localStorage : null;
            break;

        case 'session':
            store = 'sessionStorage' in _global ? _global.sessionStorage : null;
            break;
        case 'memory':
            store = MemoryStorage;
            break;
    }

    if (!store) {
        store = MemoryStorage;
        // eslint-disable-next-line
        console.error(
            `Vue-ls: Storage "${_options.storage}" is not supported your system, use memory storage`
        );
    }
    const storage = new WebStorage(store as MemoryStorageType);

    storage.setOptions(
        Object.assign(
            storage.options,
            {
                namespace: ''
            },
            _options || {}
        )
    );
    return storage;
}

export const ls = createStorage(storageOptions);
export const ss = createStorage(sessionOptions);
