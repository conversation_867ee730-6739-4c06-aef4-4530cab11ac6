<template>
    <div class="app-container">
        <OcConsultationSearch
            :form-model="tableInfo.from"
            @reset="tableInfo.reset"
            @search="tableInfo.search"
            @update:device-id="handleDeviceIdUpdate"
            @update:hospital-name="handleHospitalNameUpdate"
        />

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <OcConsultationActions
                :multiple="multiple"
                :single="single"
                @add="handleAdd"
                @cancel="handleCancel"
                @close="handleClose"
                @delete="handleDelete"
                @update="handleUpdate"
            />

            <el-table
                :data="tableInfo.list.value"
                height="450"
                row-key="id"
                style="margin-top: 20px"
                @selection-change="handleSelectionChange"
                @sort-change="tableInfo.sort"
            >
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="ID" prop="id" />
                <el-table-column align="center" label="进行状态" prop="status">
                    <template #default="scope">
                        <dict-tag :options="cns_consultation_status_enum" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="示教主题" prop="meeting.theme" />
                <el-table-column align="center" label="发起人" prop="organizer.realname" />
                <el-table-column align="center" label="手术室" prop="device.name" />
                <el-table-column align="center" label="医院" prop="device.hospitalVo.name" />
                <el-table-column align="center" label="示教群组" prop="expert">
                    <template #default="scope">
                        <span>{{
                                scope.row.teachingGroups
                                    ? scope.row.teachingGroups.map((i: TeachingGroup) => i.name).join('、')
                                    : '待分配'
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="预约时间" prop="dateStr" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.dateStr }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                    <template #default="scope">
                        <el-button
                            v-hasPermi="['cns:cns_consultation:edit']"
                            icon="Edit"
                            link
                            type="primary"
                            @click="handleUpdate(scope.row)"
                        >修改
                        </el-button>
                        <el-button
                            v-hasPermi="['cns:cns_consultation:remove']"
                            icon="Delete"
                            link
                            type="primary"
                            @click="handleDelete(scope.row)"
                        >删除
                        </el-button>
                        <el-button
                            v-hasPermi="['cns:cns_consultation:memebr_list']"
                            icon="User"
                            link
                            type="warning"
                            @click="handleLook(scope.row)"
                        >查看会议详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    v-model:page="tableInfo.pageParam.pageNum"
                    :total="tableInfo.total.value"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import type { OcConsultationOrder } from '@/model/oc_consultation';
import type { TeachingGroup } from '@/types/consultation';
import { oc_cancelConsultationGroup, oc_closeConsultationGroup, oc_delCnsGroup } from '@/api/evms/oc_consultation';
import router from '@/router';
import { getTableInfo } from '@/minix/tables';
import OcConsultationSearch from '@/components/OcConsultationSearch/index.vue';
import OcConsultationActions from '@/components/OcConsultationActions/index.vue';

// 组件实例
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cns_consultation_status_enum } = proxy!.useDict('cns_consultation_status_enum');

// 表格数据
const tableInfo = getTableInfo<OcConsultationOrder>('/oc/consultation_group/list', {
    sort: 'id desc'
});

// 选择相关
const ids = ref<string[]>([]);
const single = ref(true);
const multiple = ref(true);

// 处理设备ID更新
const handleDeviceIdUpdate = (id: number) => {
    if (tableInfo.from) {
        tableInfo.from.like_Jdevice_id = id;
    }
};

const handleHospitalNameUpdate = (name: string) => {
    if (tableInfo.from) {
        tableInfo.from.like_Jdevice_hospital_name = name;
    }
};

// 新增
const handleAdd = () => {
    router.push({ path: '/oc_consultation/expertDemonstration_info' });
};

// 修改
const handleUpdate = (row?: OcConsultationOrder) => {
    const _id = row?.id || ids.value[0];
    if (_id) {
        router.push({ path: `/oc_consultation/expertDemonstration_info/${_id}` });
    }
};

// 多选框选中数据
const handleSelectionChange = (selection: OcConsultationOrder[]) => {
    ids.value = selection.map(item => (item.id || '').toString());
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
};

// 查看参会专家
const handleLook = (row: OcConsultationOrder) => {
    router.push({ path: `/oc_consultation/look_meeting/${row.meeting.id}` });
};


// 删除操作
const handleDelete = async (row?: OcConsultationOrder) => {
    const id = row?.id?.toString() || ids.value.join(',');
    if (!id) {
        proxy!.$modal.msgError('请选择示教');
        return;
    }

    try {
        await proxy!.$modal.confirm('是否确认删除示教ID为"' + id + '"的数据项？');
        const res = await oc_delCnsGroup(id);
        if (res.code === 200) {
            proxy!.$modal.msgSuccess('删除成功');
            await tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '删除失败');
        }
    } catch (error) {
        proxy!.$modal.msgError('操作失败');
    }
};

// 取消会诊操作
const handleCancel = async () => {
    const id = ids.value.join(',');
    if (!id) {
        proxy!.$modal.msgError('请选择要取消的会诊');
        return;
    }

    try {
        await proxy!.$modal.confirm(`是否确认取消选中的会诊？`);

        const res = await oc_cancelConsultationGroup(id);

        if (res.code === 1) {
            proxy!.$modal.msgSuccess('取消会诊成功');
            await tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '取消会诊失败');
        }
    } catch (error) {
        console.error('取消会诊失败:', error);
    }
};

// 结束会诊操作
const handleClose = async () => {
    const id = row?.id || ids.value.join(',');
    if (!id) {
        proxy!.$modal.msgError('请选择要结束的会诊');
        return;
    }

    try {
        await proxy!.$modal.confirm(`是否确认结束选中的会诊？`);

        const res = await oc_closeConsultationGroup(id);

        if (res.code === 1) {
            proxy!.$modal.msgSuccess('结束会诊成功');
            await tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '结束会诊失败');
        }
    } catch (error) {
        console.error('结束会诊失败:', error);
    }
};

// 初始化加载
tableInfo.load();
</script>

<style lang="scss">
.consultation_order_experts {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}

.consultation_order_look {
    .el-dialog {
        border-radius: 20px;
        border: 1px solid rgba(204, 204, 204, 0.8);
        background: #fff !important;
        box-shadow: 10px 10px 20px 0 rgba(0, 0, 0, 0.15);
        overflow: clip;
    }

    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_edit_header {
        padding: 6.5px 0;
        display: flex;
        justify-content: center;
        color: #333;
        text-align: center;
        font-size: 14px;
    }

    .el-dialog__body {
        padding: 0;

        div {
            color: #333;
            font-size: 14px;
            line-height: 150%;

            .title {
                color: #666;
            }
        }
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
<style lang="scss" scoped></style>
