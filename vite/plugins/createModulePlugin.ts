import moment from 'moment';
import { createModule } from '../../scripts/createModule';
import bodyParser from 'body-parser';

function createResponse<T>(message: string, result: T, code: number, success: boolean) {
    return JSON.stringify({
        code,
        msg: message,
        data: result,
        success,
        timestamp: moment().valueOf()
    });
}

function success(result: any, message: string = '操作成功') {
    return createResponse(message, result, 200, true);
}

function error(result: any, message: string = '操作失败') {
    return createResponse(message, result, 500, false);
}
export default function createModulePlugin() {
    return {
        name: 'create-module-plugin',
        configureServer(server) {
            server.middlewares.use((req, res, next) => {
                if (req.url.indexOf('/api/createModule') !== -1 || req.url.indexOf('/api/preview') !== -1) {
                    bodyParser.json()(req, res, err => {
                        if (err) {
                            next(err);
                        } else {
                            let write = false;
                            if (req.url.indexOf('/api/createModule') !== -1) {
                                write = true;
                            }
                            res.setHeader('Content-Type', 'text/plain; charset=utf-8');
                            createModule(req.body, write)
                                .then(result => {
                                    let succesMsg = success(result, 'module创建成功');
                                    res.end(succesMsg);
                                })
                                .catch(e => {
                                    res.end(error(e, 'module创建失败'));
                                });
                        }
                    });
                } else {
                    next();
                }
            });
        },
        enforce: 'pre',
        transform(code: string, id: string) {
            let reg = /const\s+\{\s*queryParams\s*,\s*form,\s*rules\s*}\s*=\s*toRefs\(data\);+/;
            if (id.indexOf('vue') !== -1) {
                if (code.indexOf('getInstanceTableParams') === -1 && reg.test(code)) {
                    code = code.replace(
                        reg,
                        'const {queryParams,form,rules}=toRefs(data);(getCurrentInstance()! as any).ctx.getInstanceTableParams = ()=>([queryParams,handleQuery])'
                    );
                    return {
                        code,
                        map: null
                    };
                }
            }
        }
    };
}
