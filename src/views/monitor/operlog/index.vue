<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" label-width="68px">
            <el-form-item label="系统模块" prop="title">
                <el-input
                    v-model="queryParams.title"
                    placeholder="请输入系统模块"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="操作人员" prop="operator">
                <el-input
                    v-model="queryParams.operator"
                    placeholder="请输入操作人员"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>
            <el-form-item label="类型" prop="operateType">
                <el-select
                    v-model="queryParams.operateType"
                    placeholder="操作类型"
                    clearable
                    style="width: 240px"
                >
                    <el-option
                        v-for="dict in sys_oper_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="操作状态" clearable style="width: 240px">
                    <el-option
                        v-for="dict in sys_common_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="操作时间" style="width: 308px">
                <el-date-picker
                    v-model="dateRange"
                    value-format="YYYY-MM-DD"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['monitor:operlog:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    >删除</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['monitor:operlog:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    @click="handleClean"
                    >清空</el-button
                >
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['monitor:operlog:export']"
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    >导出</el-button
                >
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table
            ref="operlogRef"
            v-loading="loading"
            :data="operlogList"
            :default-sort="defaultSort"
            @selectionChange="handleSelectionChange"
            @sortChange="handleSortChange"
        >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="日志编号" align="center" prop="id" />
            <el-table-column label="系统模块" align="center" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="操作类型" align="center" prop="operateType">
                <template #default="scope">
                    <dict-tag :options="sys_oper_type" :value="scope.row.operateType" />
                </template>
            </el-table-column>
            <el-table-column
                label="操作人员"
                align="center"
                width="110"
                prop="operator"
                :show-overflow-tooltip="true"
                sortable="custom"
                :sort-orders="['descending', 'ascending']"
            />
            <el-table-column
                label="主机"
                align="center"
                prop="ip"
                width="130"
                :show-overflow-tooltip="true"
            />
            <el-table-column label="操作状态" align="center" prop="status">
                <template #default="scope">
                    <dict-tag :options="sys_common_status" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column
                label="操作日期"
                align="center"
                prop="createTime"
                sortable="custom"
                :sort-orders="['descending', 'ascending']"
                width="180"
            >
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="消耗时间"
                align="center"
                prop="cost"
                width="110"
                :show-overflow-tooltip="true"
                sortable="custom"
                :sort-orders="['descending', 'ascending']"
            >
                <template #default="scope">
                    <span>{{ scope.row.cost }}毫秒</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['monitor:operlog:query']"
                        link
                        type="primary"
                        icon="View"
                        @click="handleView(scope.row, scope.index)"
                        >详细</el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />

        <!-- 操作日志详细 -->
        <el-dialog v-model="open" title="操作日志详细" width="700px" append-to-body>
            <el-form :model="form" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="操作模块："
                            >{{ form.title }} / {{ typeFormat(form) }}</el-form-item
                        >
                        <el-form-item label="登录信息："
                            >{{ form.operator }} / {{ form.ip }} / {{ form.location }}</el-form-item
                        >
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="请求地址：">{{ form.url }}</el-form-item>
                        <el-form-item label="请求方式：">{{ form.request }}</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="请求参数：">{{ form.param }}</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="返回参数：">{{ form.result }}</el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="操作状态：">
                            <div v-if="form.status === 0">正常</div>
                            <div v-else-if="form.status === 1">失败</div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="消耗时间：">{{ form.cost }}毫秒</el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="操作时间：">{{ parseTime(form.createTime) }}</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item v-if="form.status === 1" label="异常信息：">{{
                            form.errorMsg
                        }}</el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="open = false">关 闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Operlog" lang="ts">
/* eslint-disable camelcase */
import { list, delOperlog, cleanOperlog } from '@/api/monitor/operlog';
import { queryFormat, parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_oper_type, sys_common_status } = proxy!.useDict('sys_oper_type', 'sys_common_status');

const operlogList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const dateRange = ref<any[]>([]);
const defaultSort = ref({ prop: 'createTime', order: 'descending' });

const data = reactive<{
    form: any;
    queryParams: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        operator: undefined,
        operateType: undefined,
        status: undefined,
    },
});

const { queryParams, form } = toRefs(data);

/** 查询登录日志 */
function getList() {
    loading.value = true;
    list(proxy!.addDateRange(queryParams.value, dateRange.value)).then((response: any) => {
        operlogList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}
/** 操作日志类型字典翻译 */
function typeFormat(row: any, column?: any) {
    return proxy!.selectDictLabel(sys_oper_type.value, row.operateType);
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = [];
    proxy!.resetForm('queryRef');
    queryParams.value.pageNum = 1;
    (proxy!.$refs['operlogRef'] as any).sort(defaultSort.value.prop, defaultSort.value.order);
}
/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    multiple.value = !selection.length;
}
/** 排序触发事件 */
function handleSortChange(column: any, prop?: any, order?: any) {
    if (column.order == 'descending') {
        queryParams.value.sort = column.prop + ' desc';
    } else {
        queryParams.value.sort = column.prop;
    }
    // queryParams.value.isAsc = column.order;
    getList();
}
/** 详细按钮操作 */
function handleView(row: any, index?: any) {
    open.value = true;
    form.value = row;
}
/** 删除按钮操作 */
function handleDelete(row: any) {
    const operIds = row.id || ids.value;
    proxy!.$modal
        .confirm('是否确认删除日志编号为"' + operIds + '"的数据项?')
        .then(function () {
            return delOperlog(operIds);
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('删除成功');
        });
    //   .catch(() => {});
}
/** 清空按钮操作 */
function handleClean() {
    proxy!.$modal
        .confirm('是否确认清空所有操作日志数据项?')
        .then(function () {
            return cleanOperlog();
        })
        .then(() => {
            getList();
            proxy!.$modal.msgSuccess('清空成功');
        });
    //   .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
    proxy!.download(
        'monitor/operatelog/exportExcel',
        {
            ...queryFormat(queryParams.value),
        },
        `operatelog_${new Date().getTime()}.xlsx`
    );
}

getList();
</script>
