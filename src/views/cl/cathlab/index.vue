<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="医院名称" prop="hospital_name">
                <el-input
                    v-model="queryParams.hospital_name"
                    placeholder="请输入医院名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item label="导管室名称" prop="name">
                <el-input
                    v-model="queryParams.name"
                    placeholder="请输入导管室名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter="handleQuery"
                />
            </el-form-item>

            <el-form-item label="类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
                    <el-option
                        v-for="dict in cl_cath_lab_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:cathlab:add']"
                    type="primary"
                    plain
                    icon="Plus"
                    :disabled="able"
                    @click="handleCathLabReport"
                >新增
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:cathlab:edit']"
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleCathLabReport"
                >修改
                </el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['cl:cathlab:remove']"
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="cathlabList" @selectionChange="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="ID" align="center" width="150" prop="id" />

            <el-table-column label="导管室名称" align="center" prop="name" />

            <el-table-column label="医院名称" align="center" width="250" prop="hospitalName" />

            <el-table-column label="医院院区" align="center" width="100" prop="courtyard" />

            <el-table-column label="设备状态" align="center" prop="equipmentStatus" />

            <el-table-column label="EBS病例数量" align="center" prop="ebsCaseSize" />

            <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['cl:cathlab:edit']"
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleCathLabReport(scope.row)"
                    >修改
                    </el-button>

                    <el-button
                        v-hasPermi="['cl:cathlab:remove']"
                        link
                        type="primary"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>

                    <el-button
                        link
                        type="success"
                        icon="Search"
                        @click="handleEbsCaseReport(scope.row)"
                        v-hasPermi="['cl:cathlab:list']"
                    >EBS病例管理
                    </el-button>

                    <el-button
                        link
                        type="danger"
                        icon="Search"
                        @click="handleFirstActiveReport(scope.row)"
                        v-hasPermi="['cl:cathlab:list']"
                    >首次激活报告
                    </el-button>

                    <el-button
                        link
                        type="warning"
                        icon="Search"
                        @click="handleElabOnlineReport(scope.row)"
                        v-hasPermi="['cl:cathlab:list']"
                    >E-lab线上巡检报告
                    </el-button>

                    <el-button
                        link
                        type="warning"
                        icon="Search"
                        @click="handleElabOfflineReport(scope.row)"
                        v-hasPermi="['cl:cathlab:list']"
                    >E-lab线下巡检报告
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />
    </div>
</template>

<script setup name="Cathlab" lang="ts">
import { listCathlab, delCathlab } from '@/api/cl/cathlab';
import { queryFormat } from '@/utils/ruoyi';
import { getCurrentInstance, ComponentInternalInstance, ref, reactive, toRefs } from 'vue';
import { useRouter } from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cl_cath_lab_type } = proxy!.useDict('cl_cath_lab_type');

const cathlabList = ref<any[]>([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<any[]>([]);
const single = ref(true);
const able = ref(false);
const multiple = ref(true);
const total = ref(0);
const defaultValue = ref(0);
const title = ref('');
const load = ref(true);
const router = useRouter();

const data = reactive<{
    form: any;
    queryParams: any;
    rules: any;
}>({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        hospital_name: undefined,
        name: undefined,
        type: undefined,
        address: undefined,
        province: undefined,
        sort: 'id desc'
    },
    rules: {
        name: [{ required: true, message: '导管室名称不能为空', trigger: 'blur' }]
    },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询导管室列表 */
function getList() {
    loading.value = true;
    listCathlab(queryFormat(queryParams.value)).then((response: any) => {
        cathlabList.value = response.data.rows;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 新增修改页面 */
function handleCathLabReport(row: any) {
    let cathId = row.id || ids.value;
    if (cathId && cathId.length > 0) {
    } else {
        cathId = defaultValue.value;
    }
    router.push('/cl/cath-lab/cathLabInfo/' + cathId);
}

/** 首次激活报告跳转 */
function handleFirstActiveReport(row: any) {
    const cathId = row.id;
    router.push('/cl/cath-lab/firstActiveReport/' + cathId);
}

/** Elab线下巡检报告 */
function handleElabOfflineReport(row: any) {
    const cathId = row.id;
    router.push('/cl/cath-lab/elabOfflineReport/' + cathId);
}

/** Elab线上巡检报告 */
function handleElabOnlineReport(row: any) {
    const cathId = row.id;
    router.push('/cl/cath-lab/elabOnlineReport/' + cathId);
}

/** EBS病例管理 */
function handleEbsCaseReport(row: any) {
    const cathId = row.id;
    router.push('/cl/cath-lab/ebsCase/' + cathId);
}

/**
 * 取消按钮
 */
function cancel() {
    open.value = false;
    reset();
}

/**
 * 表单重置
 */
function reset() {
    form.value = {
        id: undefined,
        hospitalId: undefined,
        name: undefined,
        type: undefined,
        address: undefined,
        province: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
    };
    proxy?.resetForm('cathlabRef');
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy?.resetForm('queryRef');
    handleQuery();
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection: any[]) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    able.value = selection.length >= 1;
    multiple.value = !selection.length;
}

/** 删除按钮操作 */
function handleDelete(row: any) {
    const _ids = row.id || ids.value;
    proxy?.$modal
        .confirm('是否确认删除导管室编号为"' + _ids + '"的数据项？')
        .then(function() {
            return delCathlab(_ids);
        })
        .then(() => {
            getList();
            proxy?.$modal.msgSuccess('删除成功');
        })
        .catch(e => {
            console.log(e);
        });
}

getList();
</script>
