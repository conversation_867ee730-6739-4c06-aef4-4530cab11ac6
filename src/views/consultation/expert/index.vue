<script lang="ts">
export default {
    name: 'ConsultationExpert'
};
</script>
<script lang="ts" setup>
import { getTableInfo } from '@/minix/tables';
import { ConsultationExpert } from '@/model/consultation';
import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { addExpert, deleteExpert } from '@/api/evms/consultation';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const tableInfo = getTableInfo<ConsultationExpert>('/evms/doctor_expert/list');
tableInfo.load();

// 新增
const open = ref(false);
const handleAdd = () => {
    open.value = true;
    tableInfoAdd.load();
};
const tableInfoAdd = getTableInfo<ConsultationExpert>('/evms/doctor_user/list', {
    q_type: 0
});
const addIds = ref<number[]>([]);
const addLoading = ref(false);
const multipleAdd = ref(false);

const addHandleSelect = (selection: ConsultationExpert[]) => {
    addIds.value = selection.map(item => Number(item.id));
    multipleAdd.value = !!selection.length;
};

const submitForm = async () => {
    if (!addIds.value.length) {
        proxy!.$modal.msgWarning('请选择专家');
        return;
    }

    addLoading.value = true;
    try {
        const res = await addExpert(addIds.value.join(','));
        if (res.code === 200) {
            proxy!.$modal.msgSuccess('添加成功');
            await tableInfo.reset();
            open.value = false;
            addIds.value = [];
            multipleAdd.value = false;
        } else {
            proxy!.$modal.msgWarning(res.msg || '添加失败');
        }
    } catch (error) {
        proxy!.$modal.msgError('操作失败');
    } finally {
        addLoading.value = false;
    }
};

const cancel = () => {
    if (addLoading.value) return;
    open.value = false;
    addIds.value = [];
    multipleAdd.value = false;
};

const lookClose = () => {
    if (addLoading.value) return;
    open.value = false;
    addIds.value = [];
    multipleAdd.value = false;
};

// 删除
const ids = ref<number[]>([]);
const multiple = ref(true);

// 多选框选中数据
function handleSelectionChange(selection: ConsultationExpert[]) {
    ids.value = selection.map(item => Number(item.id));
    multiple.value = !selection.length;
}

const handleDelete = (row: ConsultationExpert) => {
    if (row.id) {
        deleteFn(row.id.toString());
    }
};

const handleDeleteSel = () => {
    if (ids.value.length) {
        deleteFn(ids.value.join(','));
    }
};

const deleteBtnLoading = ref(false);

const deleteFn = async (ids: string) => {
    if (!ids) {
        proxy!.$modal.msgError('请选择用户');
        return;
    }

    deleteBtnLoading.value = true;
    try {
        await proxy!.$modal.confirm('是否确认删除用户ID为"' + ids + '"的数据项？');
        const res = await deleteExpert(ids);
        if (res.code === 200) {
            proxy!.$modal.msgSuccess('删除成功');
            await tableInfo.reset();
        } else {
            proxy!.$modal.msgError(res.msg || '删除失败');
        }
    } catch (error) {
        proxy!.$modal.msgError('操作失败');
    } finally {
        deleteBtnLoading.value = false;
    }
};
</script>

<template>
    <div class="app-container">
        <el-card shadow="never" style="padding-right: 50px; border-radius: 12px">
            <el-form :inline="true" :model="tableInfo.from" label-width="100" size="default">
                <el-form-item label="ID" prop="id">
                    <el-input
                        v-model="tableInfo.from.id"
                        clearable
                        placeholder="请输入ID"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="姓名" prop="type">
                    <el-input
                        v-model="tableInfo.from.like_realname"
                        clearable
                        placeholder="请输入姓名"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>
                <el-form-item label="手机号" prop="type">
                    <el-input
                        v-model="tableInfo.from.mobile"
                        clearable
                        placeholder="请输入手机号"
                        style="width: 240px"
                        @keyup.enter="tableInfo.search"
                    />
                </el-form-item>

                <el-row :gutter="20" justify="start" style="padding-left: 50px">
                    <el-button v-hasPermi="['ums:doctor_expert:query']" icon="Search" type="primary"
                               @click="tableInfo.search">搜索
                    </el-button>
                    <el-button icon="Refresh" @click="tableInfo.reset">重置</el-button>
                </el-row>

                <!--            <el-form-item label="描述" prop="description">-->
                <!--                <el-input-->
                <!--                    v-model="tableInfo.from.description"-->
                <!--                    placeholder="请输入描述"-->
                <!--                    clearable-->
                <!--                    style="width: 240px"-->
                <!--                    @keyup.enter="handleQuery"-->
                <!--                />-->
                <!--            </el-form-item>-->
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px; border-radius: 12px">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['ums:doctor_expert:add']"
                        color="#009dff"
                        icon="Plus"
                        plain
                        @click="handleAdd"
                    >新增
                    </el-button>
                </el-col>

                <el-col :span="1.5">
                    <el-button
                        v-hasPermi="['ums:doctor_expert:remove']"
                        :disabled="multiple"
                        color="#ff5c00"
                        icon="Delete"
                        plain
                        @click="handleDeleteSel"
                    >删除
                    </el-button>
                </el-col>
                <!--            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
            </el-row>

            <el-table
                :data="tableInfo.list.value"
                height="450"
                row-key="id"
                style="margin-top: 20px"
                @selection-change="handleSelectionChange"
            >
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="用户ID" prop="id" />
                <el-table-column align="center" label="用户姓名" prop="realname" />

                <el-table-column align="center" label="手机号" prop="mobile" />
                <el-table-column align="center" label="医院" prop="company" />
                <el-table-column align="center" label="科室" prop="department" />

                <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                    <template #default="scope">
                        <el-button
                            v-hasPermi="['ums:doctor_expert:remove']"
                            icon="Delete"
                            link
                            type="danger"
                            @click="handleDelete(scope.row)"
                        >删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-row align="bottom" justify="space-between">
                <div>共 {{ tableInfo.total }} 项数据</div>
                <pagination
                    v-show="tableInfo.total.value > 0"
                    v-model:limit="tableInfo.pageParam.pageSize"
                    v-model:page="tableInfo.pageParam.pageNum"
                    :total="tableInfo.total.value"
                    @pagination="tableInfo.pagination"
                />
            </el-row>
        </el-card>
        <!--    查看参会专家-->
        <el-dialog
            v-model="open"
            append-to-body
            draggable
            modal-class="consultation_experts_add"
            width="1156px"
            @closed="lookClose"
        >
            <template #header>
                <div class="my-header">
                    <div class="dialog_header">新增专家库</div>
                </div>
            </template>
            <div>
                <el-form :inline="true" :model="tableInfoAdd.from" label-width="100">
                    <el-row justify="space-between">
                        <el-col :span="6">
                            <el-form-item label="ID" prop="id">
                                <el-input
                                    v-model="tableInfoAdd.from.id"
                                    clearable
                                    placeholder="请输入ID"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="姓名" prop="userId">
                                <el-input
                                    v-model="tableInfoAdd.from.like_realname"
                                    clearable
                                    placeholder="请输入姓名"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="手机号" prop="mobile">
                                <el-input
                                    v-model="tableInfoAdd.from.mobile"
                                    clearable
                                    placeholder="请输入手机号"
                                    @keyup.enter="tableInfoAdd.search"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-button icon="Search" type="primary" @click="tableInfoAdd.search"
                            >搜索
                            </el-button
                            >
                            <el-button icon="Refresh" @click="tableInfoAdd.reset">重置</el-button>
                        </el-col>
                    </el-row>
                </el-form>
                <el-table
                    :data="tableInfoAdd.list.value"
                    height="350"
                    row-key="id"
                    style="width: 100%; margin-top: 20px"
                    @selection-change="addHandleSelect"
                >
                    <el-table-column align="center" type="selection" width="55" />
                    <el-table-column align="center" label="ID" prop="id" width="140"></el-table-column>
                    <el-table-column align="center" label="用户姓名" prop="realname" width="120">
                    </el-table-column>
                    <el-table-column align="center" label="手机号" prop="mobile"></el-table-column>
                    <el-table-column align="center" label="医院" prop="company"></el-table-column>
                    <el-table-column align="center" label="科室" prop="department"></el-table-column>
                </el-table>
                <el-row align="bottom" justify="space-between">
                    <div>共 {{ tableInfoAdd.total }} 项数据</div>
                    <pagination
                        v-show="tableInfoAdd.total.value > 0"
                        v-model:limit="tableInfoAdd.pageParam.pageSize"
                        v-model:page="tableInfoAdd.pageParam.pageNum"
                        :total="tableInfoAdd.total.value"
                        @pagination="tableInfoAdd.pagination"
                    />
                </el-row>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button
                        :disabled="!multipleAdd"
                        :loading="addLoading"
                        type="primary"
                        @click="submitForm"
                    >确 定
                    </el-button
                    >
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss">
.consultation_experts_add {
    .el-dialog__header {
        margin: 0;
        padding: 0 !important;
    }

    .dialog_header {
        margin-left: 20px;
        display: inline-block;
        padding: 8px 10px;
        border-radius: 0 0 6px 6px;
        background: #009dff;
        color: #fff;
        font-size: 16px;
        line-height: 150%; /* 24px */
    }

    .el-dialog__headerbtn {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
    }
}
</style>
