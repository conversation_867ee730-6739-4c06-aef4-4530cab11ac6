<template>
  <div class="oss-upload-picture-card">
    <el-upload
      ref="uploadRef"
      v-model:file-list="uploadFileList"
      :multiple="multiple"
      :accept="accept"
      :disabled="disabled || isUploading"
      :show-file-list="false"
      :auto-upload="false"
      :on-change="handleFileChange"
      :before-upload="() => false"
      list-type="picture-card"
      :class="{ 'upload-disabled': disabled }"
    >
      <div class="upload-trigger">
        <el-icon v-if="!isUploading" size="28">
          <Plus />
        </el-icon>
        <el-progress
          v-else
          type="circle"
          :percentage="progress"
          :width="40"
          :stroke-width="4"
        />
      </div>
    </el-upload>

    <!-- 自定义文件列表 -->
    <div v-if="uploadFileList.length > 0" class="custom-file-list">
      <div
        v-for="(file, index) in uploadFileList"
        :key="file.uid"
        class="file-item"
        :class="{ 'uploading': file.status === 'uploading' }"
      >
        <!-- 图片预览 -->
        <div v-if="isImage(file)" class="file-preview image-preview" @click="handleFilePreview(file)">
          <img :src="file.url" :alt="file.name" />
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideo(file)" class="file-preview video-preview" @click="handleFilePreview(file)">
          <video
            :src="file.url"
            preload="metadata"
            muted
            :poster="getVideoPoster(file)"
            @loadedmetadata="onVideoLoaded"
          >
            您的浏览器不支持视频播放
          </video>
          <div class="video-overlay">
            <el-icon size="24"><VideoPlay /></el-icon>
          </div>
        </div>

        <!-- 其他文件类型 -->
        <div v-else class="file-preview other-preview" @click="handleFilePreview(file)">
          <el-icon size="40"><Document /></el-icon>
          <div class="file-name">{{ file.name }}</div>
        </div>

        <!-- 上传进度遮罩 -->
        <div v-if="file.status === 'uploading'" class="upload-overlay">
          <el-progress
            type="circle"
            :percentage="progress"
            :width="40"
            :stroke-width="4"
          />
        </div>

        <!-- 操作按钮 -->
        <div v-if="file.status !== 'uploading'" class="file-actions">
          <div class="action-buttons">
              <el-icon size="20" @click.stop="handleFilePreview(file)"><ZoomIn /></el-icon>
              <el-icon size="20" @click.stop="handleFileRemove(file)" ><Delete /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <el-alert
        :title="errorMessage"
        type="error"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 文件预览对话框 -->
    <el-dialog v-model="previewVisible" title="预览" width="80%" center>
      <div class="preview-container">
        <img
          v-if="currentPreviewFile && isImage(currentPreviewFile)"
          :src="currentPreviewFile.url"
          :alt="currentPreviewFile.name"
          class="preview-image"
        />
        <video
          v-else-if="currentPreviewFile && isVideo(currentPreviewFile)"
          :src="currentPreviewFile.url"
          controls
          class="preview-video"
          preload="metadata"
          @click.stop
        >
          您的浏览器不支持视频播放
        </video>
        <div v-else-if="currentPreviewFile" class="preview-other">
          <el-icon size="80"><Document /></el-icon>
          <p>{{ currentPreviewFile.name }}</p>
          <p>无法预览此文件类型</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, VideoPlay, Document, ZoomIn, Delete } from '@element-plus/icons-vue'
import { uploadFileToOSS, type UploadOptions } from '@/utils/oss'
import type { UploadFile, UploadFiles, UploadUserFile } from 'element-plus'
import { log } from 'console'

interface Props {
  modelValue?: string[] // 直接绑定字符串数组（文件URL列表）
  multiple?: boolean
  accept?: string
  maxSize?: number // 字节
  maxFiles?: number
  disabled?: boolean
  uploadOptions?: UploadOptions
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'upload-success', files: any[]): void
  (e: 'upload-error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  maxSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 10,
  disabled: false
})

const emit = defineEmits<Emits>()

const uploadRef = ref()
const isUploading = ref(false)
const progress = ref(0)
const errorMessage = ref('')
const uploadFileList = ref<UploadUserFile[]>([])
const previewVisible = ref(false)
const currentPreviewFile = ref<any>(null)
const isInternalUpdate = ref(false) // 防止循环更新的标志
const ossUrlMap = ref<Map<string, string>>(new Map()) // 存储文件UID到OSS URL的映射

// 监听外部值变化，将URL数组转换为UploadFile数组
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) {
    isInternalUpdate.value = false
    return
  }

  if (newValue && Array.isArray(newValue) && newValue.length > 0) {
    const currentUrls = uploadFileList.value.map(file => file.url).filter(Boolean)
    const newUrls = newValue.filter(Boolean)

    if (JSON.stringify(currentUrls.sort()) !== JSON.stringify(newUrls.sort())) {
      uploadFileList.value = newUrls.map((url, index) => {
        const uid = Date.now() + index
        // 为外部传入的URL创建映射
        ossUrlMap.value.set(String(uid), url)
        return {
          name: extractFileNameFromUrl(url),
          url: url,
          status: 'success' as const,
          uid: uid
        }
      })
    }
  } else if (!newValue || newValue.length === 0) {
    uploadFileList.value = []
  }
}, { immediate: true })

// 监听文件列表变化，更新modelValue
watch(uploadFileList, (newValue) => {
  // 优先使用OSS URL映射，如果没有则使用文件的URL
  console.log('文件列表变化，当前文件:', newValue.map(f => ({ uid: f.uid, url: f.url, status: f.status })))
  console.log('当前OSS URL映射:', Object.fromEntries(ossUrlMap.value))

  const urls = newValue.map(file => {
    const fileUid = String(file.uid)
    const ossUrl = ossUrlMap.value.get(fileUid)
    console.log(`文件 ${fileUid}: 原URL=${file.url}, OSS URL=${ossUrl}`)
    return ossUrl || file.url
  }).filter(Boolean) as string[]

  console.log('最终提取的URLs:', urls)

  isInternalUpdate.value = true
  emit('update:modelValue', urls)
}, { deep: true })

/**
 * 从URL中提取文件名
 */
function extractFileNameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const fileName = pathname.split('/').pop() || 'unknown'
    return decodeURIComponent(fileName)
  } catch {
    return url.split('/').pop() || 'unknown'
  }
}

/**
 * 判断是否为图片文件
 */
function isImage(file: any): boolean {
  if (file.type) {
    return file.type.startsWith('image/')
  }
  const name = file.name || file.url || ''
  return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(
    name.split('.').pop()?.toLowerCase() || ''
  )
}

/**
 * 判断是否为视频文件
 */
function isVideo(file: any): boolean {
  if (file.type) {
    return file.type.startsWith('video/')
  }
  const name = file.name || file.url || ''
  return ['mp4', 'avi', 'mov', 'wmv', 'webm'].includes(
    name.split('.').pop()?.toLowerCase() || ''
  )
}

/**
 * 验证文件
 */
function validateFile(file: File): string | null {
  // 检查文件大小
  if (props.maxSize && file.size > props.maxSize) {
    return `文件大小不能超过 ${formatFileSize(props.maxSize)}`
  }
  
  // 检查文件类型
  if (props.accept) {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const fileType = file.type
    const fileName = file.name.toLowerCase()
    
    const isAccepted = acceptTypes.some(acceptType => {
      if (acceptType.startsWith('.')) {
        return fileName.endsWith(acceptType.toLowerCase())
      } else if (acceptType.includes('*')) {
        const baseType = acceptType.split('/')[0]
        return fileType.startsWith(baseType)
      } else {
        return fileType === acceptType
      }
    })
    
    if (!isAccepted) {
      return `不支持的文件类型，仅支持 ${props.accept}`
    }
  }
  
  return null
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 文件变化处理
 */
async function handleFileChange(file: UploadFile, fileList: UploadFiles) {
  errorMessage.value = ''
  
  if (!file.raw) return
  
  // 检查文件数量限制
  if (props.maxFiles && fileList.length > props.maxFiles) {
    errorMessage.value = `最多只能上传 ${props.maxFiles} 个文件`
    // 移除超出的文件
    uploadFileList.value = fileList.slice(0, props.maxFiles)
    return
  }
  
  // 验证文件
  const error = validateFile(file.raw)
  if (error) {
    errorMessage.value = error
    // 移除无效文件
    uploadFileList.value = fileList.filter(f => f.uid !== file.uid)
    return
  }
  
  // 开始上传
  await uploadFile(file)
}

/**
 * 上传单个文件
 */
async function uploadFile(file: UploadFile) {
  if (!file.raw) return
  
  isUploading.value = true
  progress.value = 0
  console.log('--text:=', JSON.stringify(uploadFileList.value), file.uid);
  
  
  // 更新文件状态为上传中
  const fileIndex = uploadFileList.value.findIndex(f => f.uid === file.uid)
  console.log('上传文件，文件索引:', fileIndex)
  if (fileIndex !== -1) {
    uploadFileList.value[fileIndex].status = 'uploading'
  }
  
  try {
    const result = await uploadFileToOSS(file.raw, {
      ...props.uploadOptions,
      onProgress: (p) => {
        progress.value = p
      }
    })
    
    console.log('上传成功，OSS返回的URL:', result.url)

    // 直接使用文件的UID存储OSS URL到映射中，不依赖 fileIndex
    const fileUid = String(file.uid)
    ossUrlMap.value.set(fileUid, result.url)
    console.log('存储OSS URL到映射，UID:', fileUid, 'URL:', result.url)

    // 等待 el-upload 组件更新 uploadFileList 后再更新状态
    nextTick(() => {
      const newFileIndex = uploadFileList.value.findIndex(f => f.uid === file.uid)
      console.log('nextTick 中查找文件索引:', newFileIndex)
      console.log('nextTick 中的 uploadFileList:', uploadFileList.value.map(f => ({ uid: f.uid, name: f.name, status: f.status })))

      if (newFileIndex !== -1) {
        uploadFileList.value[newFileIndex].status = 'success'
        // 强制触发响应式更新
        uploadFileList.value = [...uploadFileList.value]
      }
    })

    emit('upload-success', [{ ...file, url: result.url }])
    ElMessage.success('文件上传成功')
    
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '上传失败'
    
    // 更新文件状态为失败
    if (fileIndex !== -1) {
      uploadFileList.value[fileIndex].status = 'fail'
    }
    
    emit('upload-error', errorMsg)
    errorMessage.value = errorMsg
    
  } finally {
    isUploading.value = false
    progress.value = 0
  }
}

/**
 * 文件移除处理
 */
function handleFileRemove(file: UploadFile) {
  // 清理OSS URL映射
  ossUrlMap.value.delete(String(file.uid))
  uploadFileList.value = uploadFileList.value.filter(f => f.uid !== file.uid)
}

/**
 * 获取视频海报图片
 */
function getVideoPoster(file: UploadFile): string | undefined {
  // 可以返回一个默认的视频缩略图，或者通过其他方式生成
  return undefined
}

/**
 * 视频加载完成后生成缩略图
 */
function onVideoLoaded(event: Event) {
  const video = event.target as HTMLVideoElement
  if (video && video.videoWidth > 0) {
    // 设置视频到第一帧
    video.currentTime = 0.1

    // 创建canvas来生成缩略图
    nextTick(() => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (ctx) {
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        ctx.drawImage(video, 0, 0)

        // 将canvas转换为blob并设置为poster
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob)
            video.poster = url
          }
        }, 'image/jpeg', 0.8)
      }
    })
  }
}

/**
 * 文件预览处理
 */
function handleFilePreview(file: UploadFile) {
  currentPreviewFile.value = file
  previewVisible.value = true
}

// 暴露方法给父组件
defineExpose({
  uploadRef
})
</script>

<style scoped>
.oss-upload-picture-card {
  width: 100%;
}

.upload-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #8c939d;
  font-size: 28px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-trigger:hover {
  border-color: #409eff;
  color: #409eff;
}

.upload-disabled .upload-trigger {
  cursor: not-allowed;
  opacity: 0.5;
}

.error-message {
  margin-top: 12px;
}

.preview-container {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.preview-video {
  max-width: 100%;
  max-height: 70vh;
}

.preview-other {
  text-align: center;
  color: #8c939d;
  padding: 40px;
}

.preview-other p {
  margin: 12px 0;
}

/* 自定义 el-upload 样式 */
:deep(.el-upload--picture-card) {
  width: 148px;
  height: 148px;
}

/* 自定义文件列表样式 */
.custom-file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.file-item {
  position: relative;
  width: 148px;
  height: 148px;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.file-item:hover {
  border-color: #409eff;
}

.file-item.uploading {
  opacity: 0.7;
}

.file-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-preview {
  position: relative;
}

.video-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: none; /* 防止在缩略图状态下播放 */
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.video-overlay .el-icon {
  margin-left: 2px; /* 播放按钮视觉居中调整 */
}

.other-preview {
  flex-direction: column;
  color: #8c939d;
}

.file-name {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
  word-break: break-all;
  padding: 0 4px;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.file-item:hover .file-actions {
  opacity: 1;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-icon {
  color: #fff;
}
</style>
