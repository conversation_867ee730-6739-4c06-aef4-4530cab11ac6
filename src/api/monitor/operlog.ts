import request from '@/utils/request';

// 查询操作日志列表
export function list(query: any) {
    return request({
        url: '/monitor/operatelog/list',
        method: 'get',
        params: query,
    });
}

// 删除操作日志
export function delOperlog(operId: any) {
    return request({
        url: '/monitor/operatelog/' + operId,
        method: 'delete',
    });
}

// 清空操作日志
export function cleanOperlog() {
    return request({
        url: '/monitor/operatelog/clean',
        method: 'delete',
    });
}
