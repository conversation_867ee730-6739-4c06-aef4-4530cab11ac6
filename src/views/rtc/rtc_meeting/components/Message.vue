<script setup lang="ts">
import { ref, defineProps } from 'vue';
import { RtcMessage } from '@/model/rtc_meeting';

const props = defineProps(['message', 'type', 'isLine']);
const message = ref<RtcMessage>(JSON.parse(props.message));
</script>

<template>
    <div>
        <div v-if="type === 10001" :class="[isLine ? 'hide' : '']">{{ message.text }}</div>
        <div v-else-if="type === 10003">
            <div :class="[isLine ? 'hide' : '']">
                {{ isLine ? '[图片]' : message.__files?.media.remoteURL }}
            </div>
            <!--        <img :src="message.__files?.media.remoteURL" alt="image" :style="{-->
            <!--            width: '300px',-->
            <!--            height: 300 * message.__files?.media.ext['s:file_ext_key_origin_height'] / message.__files?.media['s:file_ext_key_origin_width'] + 'px'-->
            <!--        }" />-->
        </div>
        <div v-else-if="type === 10004">
            <video controls>
                <source :src="message.video" type="video/mp4" />
            </video>
        </div>
        <div v-else-if="type === 10006">
            <audio controls>
                <source :src="message.audio" type="audio/mpeg" />
            </audio>
        </div>
        <div v-else-if="type === 10005">
            <a :href="message.file" download>Download File</a>
        </div>
        <div v-else>
            <p>Unsupported message type</p>
        </div>
    </div>
</template>

<style scoped lang="scss">
.hide {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
