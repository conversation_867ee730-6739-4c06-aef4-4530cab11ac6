import { defineComponent, ref, reactive, nextTick, getCurrentInstance, onMounted } from 'vue';
import {
    ElButton,
    ElCheckbox,
    ElCheckboxGroup,
    ElCol,
    ElDatePicker,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElOption,
    ElPagination,
    ElRadio,
    ElRadioGroup,
    ElRow,
    ElSelect,
    ElTable,
    ElTableColumn
} from 'element-plus';
import { RefsKes } from '@/type/common';
import Player from 'xgplayer';
import 'xgplayer/dist/index.min.css';

export default defineComponent({
    setup(props, { emit, expose }) {
        const { proxy } = getCurrentInstance()!;
        const state = reactive({
            visible: false,
            player: null as any,
            title: '查看视频'
        });

        let instance: RefsKes;
        onMounted(() => {
            instance = getCurrentInstance() as RefsKes;
        });
        function handleOk() {
            close();
        }
        function close() {
            state.visible = false;
        }

        function onClose() {
            state.player.destroy();
        }

        function show(url: string, title: string) {
            state.visible = true;
            state.title = title ? title : state.title;
            setTimeout(() => {
                state.player = new Player({
                    id: 'mse', //元素id
                    lang: 'zh', //设置中文
                    volume: 0, // 默认静音
                    autoplay: false, //自动播放
                    screenShot: false, // 开启截图功能
                    //视频地址
                    url: url,
                    //封面图
                    poster: '',
                    fluid: true, // 填满屏幕 （流式布局）
                    playbackRate: [0.5, 0.75, 1, 1.5, 2] //传入倍速可选数组
                });
            }, 50);
        }
        const slots = {
            modalFooter: {
                footer() {
                    return (
                        <div class="dialog-footer">
                            <ElButton type="primary" onClick={handleOk}>
                                确 定
                            </ElButton>
                            <ElButton onClick={close}>取 消</ElButton>
                        </div>
                    );
                },
            },
        };

        expose({
            show
        });
        return () => (
            <ElDialog
                title={state.title}
                v-model={state.visible}
                appendToBody
                width="1000px"
                v-slots={slots.modalFooter}
                onClose={onClose}
            >
                <div id="mse" />
            </ElDialog>
        );
    },
});
