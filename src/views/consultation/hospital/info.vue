<script lang="ts">
export default {
    name: 'ConsultationHospitalInfo'
};
</script>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import ConsultationHospitalBase from './components/ConsultationHospitalBase.vue';
import ConsultationHospitalMember from './components/ConsultationHospitalMember.vue';

type TabNameType = 'base' | 'member';

const TAB_NAMES = {
    base: 'base',
    member: 'member'
} as const;

const activeName = ref<TabNameType>(TAB_NAMES.base);
const route = useRoute();
const groupId = ref<string | string[]>(route.params.id);
const isLook = ref<string | undefined>(route.query.look as string);

// 如果是查看模式，默认显示成员标签页
if (isLook.value) {
    activeName.value = TAB_NAMES.member;
}
</script>

<template>
    <div class="consultation_hospital_container">
        <el-card style="padding: 0 20px 20px; border-radius: 12px">
            <el-tabs v-model="activeName" class="demo-tabs" type="card">
                <el-tab-pane :label="'基础信息'" :name="TAB_NAMES.base">
                    <ConsultationHospitalBase v-model:groupId="groupId" />
                </el-tab-pane>
                <el-tab-pane
                    v-if="!!groupId"
                    :disabled="!groupId"
                    :label="'查看群组成员'"
                    :lazy="true"
                    :name="TAB_NAMES.member"
                >
                    <ConsultationHospitalMember
                        v-if="!!groupId && activeName == TAB_NAMES.member"
                        :groupId="groupId"
                    />
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<style lang="scss" scoped>
.consultation_hospital_container {
    height: calc(100vh - 84px);
    padding: 20px;
    background: #f8f9fb;

    :deep(.el-card) {
        height: 100%;
    }

    :deep(.el-card__body) {
        height: 100% !important;
        padding: 0 !important;
    }

    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);
    }

    :deep(.el-tab-pane) {
        height: 100% !important;
        overflow-y: scroll;
    }

    :deep(.el-tabs--card > .el-tabs__header) {
        border: none;

        .el-tabs__nav {
            border: none;
        }

        .el-tabs__item {
            margin-right: 1px;
            border: none;
            border-radius: 0 0 6px 6px;
            background: #ecf7ff;
            padding: 8px 10px;
            justify-content: center;
            align-items: center;
            color: #009dff;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%;

            &.is-active {
                background: #009dff;
                color: #fff;
            }
        }
    }
}
</style>
