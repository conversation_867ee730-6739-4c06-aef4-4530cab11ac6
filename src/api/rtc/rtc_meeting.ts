import { Http } from '@/utils/request';
import { RtcMeetingDetail, RtcMemberJoinLog, RtcOrgItem } from '@/model/rtc_meeting';
/**
 * 新增会议
 */
export async function addRtcMeeting(data: RtcMeetingDetail) {
    return await Http.post('/evms/rtc_meeting/add', data);
}

/**
 * 根据id查询会议
 */
export async function getMeetingById(id: number) {
    return await Http.get<RtcMeetingDetail>('/evms/rtc_meeting/' + id);
}

/**
 * 更新会议
 */
export async function updateRtcMeeting(data: RtcMeetingDetail) {
    return await Http.put('/evms/rtc_meeting/update', data);
}

/**
 * 删除会议
 */
export async function deleteRtcMeeting(id: string) {
    return await Http.delete('/evms/rtc_meeting/' + id);
}

/**
 * 移出成员
 */
export async function kictRtcMember(data: { banned: 0 | 1; ids: string }) {
    return await Http.post('/evms/rtc_meeting/member/kick', data, false, 'form');
}

/**
 * 删除成员
 */
export async function removeRtcMember(ids: string) {
    return await Http.delete('/evms/rtc_meeting/member/logical_delete/' + ids);
}
/**
 * 添加成员
 */
export async function addRtcMember(data: { meetingId: number; memberIds: number[] }) {
    return await Http.post('/evms/rtc_meeting/add_member', data);
}

/**
 * 根据ID查询成员进出日志
 */
export async function getMemberById(data: { meetingId: number; userId: number }) {
    return await Http.get<{ list: RtcMemberJoinLog[] }>('/evms/rtc_meeting/member/joinLog', data);
}

/**
 * 修改成员
 */
export async function updateRtcMember(data: { id: number; nickname: string; role: number }) {
    return await Http.post('/evms/rtc_meeting/member/edit', data, false, 'from');
}

/**
 * 屏蔽
 */
export async function shieldRtcMember(data: { chatIds: string }) {
    return await Http.post('/evms/rtc_meeting/chat/shield', data, false, 'form');
}

/**
 * 查询组织列表
 */
export async function getOrgList(data: { query: string; pageNum: number; pageSize: number }) {
    return await Http.get<RtcOrgItem>('/evms/rtc_org/list', data);
}
