<template>
    <div class="app-container">
        <el-tabs v-model="activeName" class="demo-tabs" type="card" @tab-click="handleClick">
            <el-tab-pane label="导管室基础信息" name="CLCathLabInfo"
            >{{ cathLabTitle }}--基础信息
            </el-tab-pane
            >
            <el-tab-pane label="导管室设备信息" name="CLEquipment">{{ cathLabTitle }}--设备信息</el-tab-pane>
            <el-tab-pane label="导管室联系信息" name="CLContactInfo"
            >{{ cathLabTitle }}--联系信息
            </el-tab-pane
            >
            <el-tab-pane label="导管室合作信息" name="CLCooperate">{{ cathLabTitle }}--合作信息</el-tab-pane>
        </el-tabs>

        <!-- 添加或修改导管室对话框 -->
        <el-form v-show="open" ref="cathlabRef" :model="form" :rules="rules" label-width="200px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="导管室名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入导管室名称" />
                    </el-form-item>
                </el-col>

                <el-col :span="12"></el-col>

                <el-col :span="12">
                    <el-form-item label="类型" prop="type">
                        <el-select v-model="form.type" placeholder="请选择类型">
                            <el-option
                                v-for="dict in cl_cath_lab_type"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="12"></el-col>

                <el-col :span="12">
                    <el-col :span="12">
                        <el-form-item label="医院名称" prop="hospitalId">
                            <el-select
                                v-model="form.hospitalId"
                                :loading="load"
                                :remote-method="searchHospitalList"
                                filterable
                                placeholder="请输入医院名称"
                                remote
                                remote-show-suffix
                                reserve-keyword
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in hospitalList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-col>

                <el-col :span="12"></el-col>

                <el-col :span="12">
                    <el-form-item label="医院院区" prop="courtyard">
                        <el-input v-model="form.courtyard" placeholder="请输入医院院区" />
                    </el-form-item>
                </el-col>

                <el-col :span="12"></el-col>

                <el-col :span="12">
                    <el-form-item label="导管室地址" prop="address">
                        <el-input v-model="form.address" placeholder="请输入地址" />
                    </el-form-item>
                </el-col>

                <el-col :span="12"></el-col>

                <el-col :span="12">
                    <el-form-item label="省市" prop="provinceCity">
                        <el-input v-model="form.provinceCity" placeholder="请输入省市" />
                    </el-form-item>
                </el-col>

                <el-col :span="12"></el-col>

                <el-col :span="12">
                    <el-form-item label="区域" prop="area">
                        <el-input v-model="form.area" placeholder="请输入区域" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-button type="primary" @click="submitForm">保 存</el-button>
            <el-button @click="cancel">取 消</el-button>
        </el-form>

        <el-form label-width="100px">
            <div style="text-align: center; margin-left: -120px; margin-top: 30px">
                <el-button @click="close()">返回</el-button>
            </div>
        </el-form>
    </div>
</template>

<script lang="ts" name="Cathlab" setup>
import { useRoute, useRouter } from 'vue-router';
import { addCathlab, getCathlab, getHospitalById, getHospitalByName, updateCathlab } from '@/api/cl/cathlab';
import { ComponentInternalInstance, getCurrentInstance, reactive, ref, toRefs } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cl_cath_lab_type } = proxy!.useDict('cl_cath_lab_type');

const hospitalList = ref<any[]>([]);
const loading = ref(true);
const open = ref(false);
const ids = ref<any[]>([]);
const load = ref(true);
const route = useRoute();
const router = useRouter();
const cathLabTitle = ref('');
const activeName = ref('CLCathLabInfo');

const data = reactive<{
    form: any;
    rules: any;
}>({
    form: {},
    rules: {
        name: [{ required: true, message: '导管室名称不能为空', trigger: 'blur' }],
        address: [{ required: true, message: '导管室地址不能为空', trigger: 'blur' }],
        hospitalId: [{ required: true, message: '请选择医院', trigger: 'blur' }]
    }
});

const { form, rules } = toRefs(data);

/**
 * 表单重置
 */
function reset() {
    form.value = {
        id: undefined,
        hospitalId: undefined,
        name: undefined,
        type: undefined,
        address: undefined,
        courtyard: undefined,
        provinceCity: undefined,
        area: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
    };
    proxy?.resetForm('cathlabRef');
}

/**
 * 取消按钮
 */
function cancel() {
    reset();
}

/** 关闭按钮 */
function close() {
    const obj = { path: '/cl/cathlab', name: 'Cathlab' };
    proxy!.$tab.closeOpenPage(obj).then(() => {
        // 页面关闭后，刷新页面
        proxy!.$tab.refreshPage(obj);
    });
}

function handleClick(tab: any) {
    const cathId = form.value.id || Number(route.params.cathId);
    if ('CLEquipment' == tab.props.name) {
        router.push('/cl/cath-lab/equipment/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    } else if ('CLContactInfo' == tab.props.name) {
        router.push('/cl/cath-lab/contactInfo/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    } else if ('CLCooperate' == tab.props.name) {
        router.push('/cl/cath-lab/cooperate/' + cathId).then(() => {
            // 在导航完成后刷新页面
            proxy!.$tab.refreshPage();
        });
    }
}

/** 提交按钮 */
function submitForm() {
    (proxy?.$refs['cathlabRef'] as any).validate((valid: boolean) => {
        if (valid) {
            if (form.value.id != null) {
                updateCathlab(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('修改成功');
                    form.value = response.data;
                    open.value = true;
                });
            } else {
                addCathlab(form.value).then((response: any) => {
                    proxy?.$modal.msgSuccess('新增成功');
                    form.value = response.data;
                    open.value = true;
                });
            }
        }
    });
}

/**
 * 获取医院列表
 */
function searchHospitalList(hospitalName?: String) {
    load.value = true;
    hospitalList.value = [];
    getHospitalByName(hospitalName).then(response => {
        hospitalList.value = response.data;
    });
    load.value = false;
}

function getCLEbscases(cathId: any) {
    if (cathId && cathId != 0) {
        getCathlab(cathId).then((response: any) => {
            cathLabTitle.value = response.data.name;
            form.value = response.data;
            if (form.value.hospitalId) {
                hospitalList.value = [];
                getHospitalById(form.value.hospitalId).then((response: any) => {
                    hospitalList.value = response.data;
                });
                load.value = false;
            }
            open.value = true;
        });
    } else {
        reset();
        open.value = true;
    }
}

getCLEbscases(route.params && route.params.cathId);
</script>
