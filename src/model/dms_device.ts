export interface DmsDeviceInfo {
    id: number;
    createBy?: string;
    updateBy?: string;
    createTime?: string;
    updateTime?: string;
    nativeId: string;
    name: string;
    enName: string;
    nvrIp: string;
    nvrPort: number;
    nvrPwd: string;
    type: number;
    description: string;
    installationTime?: null;
    rabbitmqConfigId: number;
    oldNativeId: null;
    permissionJson: null;
    status?: Status;
    hospitalVo?: HospitalVo;
    permission: null;
    weight?: number;
    Jhospital_name?: string; // 查询参数
    rabbitmqConfigVo?: RabbitmqConfigVo;
    courtyard: string;
    address: string;
    hospitalId?: number | string; // 这两个提供给前端使用
}

export interface RabbitmqConfigVo {
    id: number;
    name: string;
    ip: string;
    port: number;
    user: string;
    pwd: string;
    vhost: string;
}

export interface HospitalVo {
    id: number;
    name: string;
    type: number;
}

export interface Status {
    id: number;
    status: string;
    version: string;
    onofflineTime: string;
}

export interface CameraInfo {
    deviceId?: number;
    deviceName?: string;
    id?: number;
    control?: 0 | 1;
    createBy?: null;
    updateBy?: null;
    createTime?: string;
    updateTime?: string;
    name: string;
    channel: string;
    type?: number;
    deviceVo?: DeviceVo;
    weight?: number;
    realTimeSwitch?: 0 | 1;
    playbackSwitch?: 0 | 1;
    realTimeHiddenTime?: string | number;
    playbackHiddenTime?: string | number;
    realTimeHiddenTimestamp?: number;
    playbackHiddenTimestamp?: number;
}

export const HiddenTime = [
    { label: '1小时', value: 1 },
    { label: '6小时', value: 6 },
    { label: '12小时', value: 12 },
    { label: '1天', value: 24 },
    { label: '2天', value: 48 },
    { label: '3天', value: 72 },
    { label: '4天', value: 96 },
    { label: '5天', value: 120 },
    { label: '6天', value: 144 },
    { label: '7天', value: 168 },
    { label: '8天', value: 192 },
    { label: '9天', value: 216 },
    { label: '10天', value: 240 }
];
export const hiddenSwitchType = [
    {
        elTagType: 'success',
        label: '显示',
        value: '1'
    },
    {
        elTagType: 'danger',
        label: '隐藏',
        value: '0'
    },
];

export interface DeviceVo {
    id: number;
    nativeId: string;
    name: string;
    type: number;
    description: string;
    nvrIp: string;
    nvrPort: number;
    nvrPwd: string;
}

export interface DmsUserList {
    id?: number;
    username?: string;
    realname?: string;
    title?: null;
    company?: null;
    department?: null;
    identity?: null;
    statusForPatient?: null;
    gender?: string;
    role?: null;
    auth?: null;
    userid?: number;
    nickname?: string;
    videoExportPermission?: null;
    type?: null;
    permission?: Permission;
}

export interface Permission {
    elab: boolean;
    ebs: boolean;
    outpatient: boolean;
    videoExportPermission: boolean;
}

export interface RabbitmqInfo {
    id: number;
    createBy: string;
    updateBy: string;
    createTime: string;
    updateTime: string;
    name: string;
    ip: string;
    port: number;
    user: string;
    pwd: string;
    vhost: string;
}

// 设备管理-用户
export interface DeviceUserItem {
    id: number;
    username: string;
    realname: string;
    title?: string;
    company?: string;
    department?: string;
    permission?: DeviceUserPermission;
    avatar?: string;
}

export interface DeviceUserPermission {
    realplay: 0 | 1;
    backplay: 0 | 1;
    download: 0 | 1;
    meeting: 0 | 1;
    talk: 0 | 1;
    control: 0 | 1;
    watermark: 0 | 1;
    visibleState: 0 | 1 | 2;
}

// 添加用户 参数
export interface AddDeviceUserParams {
    id: number;
    userIdsStr: string;
}

// 修改用户设备权限 参数
export interface UpdateDeviceUserParams {
    deviceId: number;
    userId: number;
    permission: string;
}
