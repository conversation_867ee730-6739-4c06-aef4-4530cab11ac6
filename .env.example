###
 # @Author: tb0912 <EMAIL>
 # @Date: 2025-06-25 14:28:19
 # @LastEditors: tb0912 <EMAIL>
 # @LastEditTime: 2025-06-25 16:29:48
 # @FilePath: /evms-web/.env.example
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
# 阿里云OSS配置（可选，用于回退，推荐使用STS临时凭证）
# VITE_OSS_REGION=oss-cn-shenzhen
# VITE_OSS_BUCKET=evms-resource
# VITE_OSS_ENDPOINT=https://oss-cn-shenzhen.aliyuncs.com

# 注意：生产环境推荐使用STS临时凭证，通过后台接口 /sys/oss/getAssumeRoleResponse 获取
# 以下配置仅用于开发环境或STS服务不可用时的回退
# VITE_OSS_ACCESS_KEY_ID=your_access_key_id
# VITE_OSS_ACCESS_KEY_SECRET=your_access_key_secret

# API配置
VITE_API_BASE_URL=http://localhost:3000/api

# 其他配置
VITE_APP_TITLE=EVMS系统
