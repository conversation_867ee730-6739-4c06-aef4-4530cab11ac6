import request, { Http } from '@/utils/request';
import { AddDeviceUserParams, DmsDeviceInfo, RabbitmqInfo, UpdateDeviceUserParams } from '@/model/dms_device';
import { DmsHospitalInfo } from '@/model/dms_hospital';

// 查询DMS设备列表
export async function listDmsDevice(query: any) {
    return await Http.get<DmsDeviceInfo>('/evms/dms_device/list', query);
}

// 查询rabbitmq配置列表
export async function listDmsRabbitMQ() {
    return await Http.get<RabbitmqInfo>('/evms/dms_rabbitmq/getList');
}

// 查询rabbitmq配置列表
export async function getHospitalListByKeywords(query: { keywords: string }) {
    return Http.get<DmsHospitalInfo>('/evms/dms_hospital/getListByKeywords', query);
    // return request({
    //     url: '/evms/dms_hospital/getListByKeywords',
    //     method: 'get',
    //     params: query,
    // });
}

// 查询DMS设备详细
export async function getDmsDevice(id: number) {
    return await Http.get<DmsDeviceInfo>('/evms/dms_device/' + id);
}

// 新增DMS设备
export async function addDmsDevice(data: Partial<DmsDeviceInfo>) {
    return await Http.post<DmsDeviceInfo>('/evms/dms_device', data);
}

// 修改DMS设备
export async function updateDmsDevice(data: Partial<DmsDeviceInfo>) {
    return await Http.put<DmsDeviceInfo>('/evms/dms_device', data);
}

// 删除DMS设备
export async function delDmsDevice(id: any) {
    return await Http.delete<null>('/evms/dms_device/' + id);
}
// 给设备添加用户
export async function addDeviceUser(data: AddDeviceUserParams) {
    return await Http.post<null>('/evms/dms_device/addDeviceUser', data, false, 'form');
}
// 更改用户设备权限
export async function updateDeviceUser(data: UpdateDeviceUserParams) {
    return await Http.post<null>('/evms/dms_device/updateUserPermission', data, false, 'form');
}
// 删除设备用户
export async function delDeviceUser(data: { deviceId: number; userIdsStr: string }) {
    return await Http.post<null>('/evms/dms_device/deleteUserByDeviceIdAndUserIdsStr', data, false, 'form');
}

// 查询设备列表
export function listDevice(query: any) {
    return request({
        url: '/evms/dms_device/list',
        method: 'get',
        params: query,
    });
}
